# 前端页面使用说明

## 概述

本项目提供了完整的前端界面来调用阿里百炼智能体API，采用Vue 3 + Element Plus构建，支持多种对话模式和文件处理功能。

## 页面结构

```
static/
├── index.html              # 首页 - 系统导航页面
├── bailian-chat.html       # 百炼智能体对话页面
├── css/
│   └── common.css          # 通用样式文件
├── js/
│   └── utils.js            # 工具函数库
└── README.md               # 使用说明
```

## 功能特性

### 1. 首页 (index.html)
- 系统概览和功能介绍
- 快速导航到各个功能模块
- 响应式设计，支持移动端
- 服务状态检查

### 2. 百炼智能体对话页面 (bailian-chat.html)

#### 核心功能：
- **普通对话**: 标准的问答模式
- **流式对话**: 实时流式响应（模拟效果）
- **多轮对话**: 支持上下文的连续对话
- **知识库检索**: 基于知识库的问答
- **文件处理**: 上传文件并进行AI分析

#### 界面特性：
- 现代化聊天界面设计
- 消息气泡样式，区分用户和AI回复
- 实时加载状态显示
- 会话历史管理
- 文件拖拽上传支持

## 使用方法

### 1. 启动应用
```bash
# 启动Spring Boot应用
mvn spring-boot:run

# 或者运行jar包
java -jar target/gk-client-api.jar
```

### 2. 访问页面
- 首页: http://localhost:8080/
- 百炼对话: http://localhost:8080/bailian-chat.html
- API文档: http://localhost:8080/doc.html

### 3. 配置应用ID
1. 在页面右上角输入您的百炼应用ID
2. 选择对话模式（普通/流式/多轮/知识库）
3. 开始对话

### 4. 对话操作

#### 发送消息：
- 在输入框中输入问题
- 点击"发送"按钮或按 Ctrl+Enter
- 等待AI回复

#### 文件处理：
1. 点击"文件"按钮选择文件
2. 输入处理要求
3. 点击"处理文件"
4. 查看AI分析结果

#### 会话管理：
- 点击"历史"查看会话记录
- 点击"清除"清空当前对话
- 点击"新会话"开始新的对话

## 技术栈

### 前端技术：
- **Vue 3**: 渐进式JavaScript框架
- **Element Plus**: Vue 3组件库
- **Axios**: HTTP客户端（通过CDN引入）
- **原生JavaScript**: 工具函数和DOM操作

### 样式技术：
- **CSS3**: 现代CSS特性
- **Flexbox/Grid**: 响应式布局
- **CSS动画**: 交互动效
- **媒体查询**: 移动端适配

## 配置说明

### API配置
```javascript
const config = reactive({
    appId: '',                    // 百炼应用ID
    sessionId: generateSessionId(), // 会话ID
    baseURL: '/api/bailian'       // API基础路径
});
```

### 支持的文件类型
- 文本文件: .txt, .md
- 数据文件: .json, .xml
- 文档文件: .doc, .docx（需要后端支持）
- 最大文件大小: 10MB

## 自定义开发

### 1. 添加新的对话模式
```javascript
// 在sendMessage函数中添加新的case
switch (chatMode.value) {
    case 'your-new-mode':
        response = await handleYourNewMode(userMessage);
        break;
    // ...
}

// 实现处理函数
async function handleYourNewMode(message) {
    return await axios.post(`${config.baseURL}/your-endpoint`, {
        // 请求参数
    });
}
```

### 2. 自定义样式
```css
/* 在bailian-chat.html的<style>标签中添加 */
.your-custom-class {
    /* 自定义样式 */
}
```

### 3. 添加新功能按钮
```html
<!-- 在input-tools区域添加 -->
<el-button type="text" size="small" @click="yourFunction">
    <el-icon><YourIcon /></el-icon>
    功能名称
</el-button>
```

## 常见问题

### 1. 页面无法加载
- 检查Spring Boot应用是否正常启动
- 确认端口8080是否被占用
- 查看浏览器控制台错误信息

### 2. API调用失败
- 检查应用ID是否正确配置
- 确认百炼API密钥是否有效
- 查看后端日志错误信息

### 3. 文件上传失败
- 检查文件大小是否超过限制
- 确认文件类型是否支持
- 查看网络连接状态

### 4. 流式对话不工作
- 当前实现为模拟流式效果
- 真实流式需要SSE支持
- 可以参考后端流式接口实现

## 浏览器兼容性

### 支持的浏览器：
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 不支持的功能：
- IE浏览器（不支持Vue 3）
- 旧版本移动浏览器

## 性能优化建议

### 1. 资源优化
- 使用CDN加载第三方库
- 启用浏览器缓存
- 压缩CSS和JavaScript

### 2. 用户体验
- 添加加载状态提示
- 实现错误重试机制
- 优化移动端体验

### 3. 安全考虑
- 验证用户输入
- 防止XSS攻击
- 限制文件上传类型

## 更新日志

### v1.0.0 (2025-07-03)
- 初始版本发布
- 支持基础对话功能
- 实现文件处理功能
- 添加会话管理功能

## 技术支持

如有问题，请：
1. 查看浏览器控制台错误
2. 检查后端API日志
3. 参考API文档说明
4. 联系技术支持团队
