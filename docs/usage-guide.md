# 百炼智能体前端使用指南

## 🚀 快速开始

### 1. 访问地址
- **应用端口**: 8081 (已从8080改为8081避免冲突)
- **首页**: http://localhost:8081/
- **快速测试**: http://localhost:8081/quick-test.html
- **完整聊天**: http://localhost:8081/bailian-chat.html
- **调试工具**: http://localhost:8081/debug.html
- **API文档**: http://localhost:8081/doc.html

### 2. 应用ID配置
默认应用ID: `3267ecc2febd4c37b0baf9a08957daaa`
您可以在页面中修改为自己的应用ID。

## 📋 功能页面说明

### 🔧 快速测试页面 (quick-test.html)
**推荐首次使用**
- 简单直观的测试界面
- 自动检查服务状态
- 基础功能测试
- 错误诊断提示

**使用步骤**:
1. 打开 http://localhost:8081/quick-test.html
2. 确认服务状态为"在线"
3. 输入应用ID（或使用默认值）
4. 输入测试消息
5. 点击"测试对话"按钮

### 💬 完整聊天页面 (bailian-chat.html)
**功能最完整的聊天界面**
- 现代化聊天UI
- 支持多种对话模式
- 文件上传处理
- 会话历史管理

**对话模式**:
- **普通对话**: 标准问答
- **流式对话**: 实时响应（模拟效果）
- **多轮对话**: 上下文连续对话
- **知识库检索**: 基于知识库的专业问答

### 🔍 调试工具页面 (debug.html)
**开发者调试工具**
- 全面的API测试
- 系统信息检查
- 网络状态诊断
- 错误排查工具

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 页面无法访问
**症状**: 浏览器显示"无法访问此网站"
**解决方案**:
```bash
# 检查应用是否启动
netstat -ano | findstr :8081

# 重新启动应用
mvn spring-boot:run
```

#### 2. API调用失败
**症状**: 显示"网络错误"或"请求失败"
**可能原因**:
- 应用ID错误
- API密钥未配置或无效
- 网络连接问题
- 服务未正常启动

**解决步骤**:
1. 访问 http://localhost:8081/quick-test.html
2. 检查服务状态指示器
3. 点击"测试连接"验证基础连接
4. 确认应用ID正确
5. 查看浏览器控制台错误信息

#### 3. 对话无响应
**症状**: 发送消息后没有回复
**检查项目**:
- [ ] 应用ID是否正确
- [ ] API密钥是否配置在 `application.yml` 中
- [ ] 网络是否能访问阿里云服务
- [ ] 百炼服务是否正常

#### 4. 文件上传失败
**症状**: 文件处理返回错误
**限制说明**:
- 文件大小: 最大10MB
- 支持格式: .txt, .json, .xml, .md
- 编码要求: UTF-8

#### 5. 跨域问题
**症状**: 控制台显示CORS错误
**解决方案**: 控制器已配置 `@CrossOrigin(origins = "*")`，如仍有问题请检查浏览器设置。

## 🔧 开发者信息

### API端点列表
```
POST /api/bailian/chat                    # 普通对话
POST /api/bailian/chat/multi-turn         # 多轮对话
POST /api/bailian/knowledge/search        # 知识库检索
POST /api/bailian/file/process            # 文件处理
GET  /api/bailian/session/{id}/history    # 获取会话历史
DELETE /api/bailian/session/{id}/history  # 清除会话历史
```

### 请求示例
```javascript
// 普通对话
const response = await fetch('http://localhost:8081/api/bailian/chat', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        appId: 'your-app-id',
        prompt: '你好',
        sessionId: 'session-123',
        incrementalOutput: false
    })
});
```

### 响应格式
```json
{
    "text": "您好！我是阿里百炼智能体...",
    "sessionId": "session-123",
    "code": 200,
    "timestamp": 1672531200000
}
```

## 📞 技术支持

### 日志查看
- **应用日志**: 查看控制台输出
- **浏览器日志**: F12 → Console
- **网络请求**: F12 → Network

### 联系方式
如遇到问题，请提供：
1. 错误截图
2. 浏览器控制台错误信息
3. 使用的应用ID
4. 具体操作步骤

## 🔄 更新记录

### v1.0.0 (2025-07-04)
- ✅ 解决端口冲突问题（8080 → 8081）
- ✅ 修复所有API调用路径
- ✅ 创建快速测试页面
- ✅ 完善错误处理和用户提示
- ✅ 添加服务状态检查
- ✅ 优化用户体验

### 已知问题
- 流式对话为模拟效果，非真实SSE实现
- 部分旧浏览器可能不支持某些ES6特性

### 计划改进
- [ ] 实现真正的流式响应
- [ ] 添加更多文件格式支持
- [ ] 优化移动端体验
- [ ] 添加主题切换功能
