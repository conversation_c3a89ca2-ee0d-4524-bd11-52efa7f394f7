# 阿里百炼智能体 Java SDK 使用指南

## 概述

本项目已集成阿里百炼智能体功能，提供完整的对话、流式响应、多轮对话、知识库检索和文件处理能力。

## 配置说明

### 1. 配置文件设置

在 `application.yml` 中配置您的百炼参数：

```yaml
bailian:
  api-key: your-actual-api-key  # 您的百炼API密钥
  default-app-id: your-app-id   # 默认应用ID（可选）
  timeout: 30000                # 请求超时时间（毫秒）
  max-retries: 3               # 最大重试次数
  incremental-output: true     # 是否启用增量输出
  stream-buffer-size: 1024     # 流式输出缓冲区大小
  session-timeout: 30          # 会话超时时间（分钟）
```

### 2. 获取API密钥

1. 登录阿里云控制台
2. 进入百炼平台
3. 创建应用并获取API密钥
4. 将密钥配置到 `application.yml` 中

## API 接口说明

### 1. 普通对话

**接口地址：** `POST /api/bailian/chat`

**请求示例：**
```json
{
    "appId": "your-app-id",
    "prompt": "你好，请介绍一下自己",
    "sessionId": "session-123",
    "incrementalOutput": false,
    "parameters": {
        "temperature": 0.7,
        "max_tokens": 1000
    }
}
```

**响应示例：**
```json
{
    "text": "您好！我是阿里百炼智能体...",
    "sessionId": "session-123",
    "code": 200,
    "metadata": null,
    "error": null
}
```

### 2. 流式对话

**接口地址：** `POST /api/bailian/chat/stream`

**请求示例：**
```json
{
    "appId": "your-app-id",
    "prompt": "请详细解释人工智能的发展历程",
    "sessionId": "session-456"
}
```

**响应格式：** Server-Sent Events (SSE)
```
data: {"text":"人工智能","sessionId":"session-456","timestamp":1672531200000}
data: {"text":"的发展","sessionId":"session-456","timestamp":1672531200100}
data: {"text":"可以分为","sessionId":"session-456","timestamp":1672531200200}
```

### 3. 多轮对话

**接口地址：** `POST /api/bailian/chat/multi-turn`

**请求示例：**
```json
{
    "appId": "your-app-id",
    "sessionId": "session-789",
    "messages": [
        {"role": "user", "content": "什么是机器学习？"},
        {"role": "assistant", "content": "机器学习是人工智能的一个分支..."},
        {"role": "user", "content": "它有哪些应用场景？"}
    ]
}
```

### 4. 知识库检索

**接口地址：** `POST /api/bailian/knowledge/search`

**请求示例：**
```json
{
    "appId": "your-app-id",
    "query": "机器学习算法"
}
```

### 5. 文件处理

**接口地址：** `POST /api/bailian/file/process`

**请求参数：**
- `file`: 上传的文件（支持文本文件，最大10MB）
- `appId`: 应用ID
- `prompt`: 处理提示

**使用示例：**
```bash
curl -X POST \
  http://localhost:8080/api/bailian/file/process \
  -F "file=@document.txt" \
  -F "appId=your-app-id" \
  -F "prompt=请总结这个文档的主要内容"
```

### 6. 会话管理

#### 获取会话历史
**接口地址：** `GET /api/bailian/session/{sessionId}/history`

#### 清除会话历史
**接口地址：** `DELETE /api/bailian/session/{sessionId}/history`

## Java 代码示例

### 1. 基本对话调用

```java
@Autowired
private BailianService bailianService;

public void testChat() {
    BailianRequest request = new BailianRequest();
    request.setAppId("your-app-id");
    request.setPrompt("你好，请介绍一下自己");
    request.setSessionId("session-123");
    
    try {
        BailianResponse response = bailianService.chat(request);
        System.out.println("回复: " + response.getText());
        System.out.println("会话ID: " + response.getSessionId());
    } catch (Exception e) {
        System.err.println("调用失败: " + e.getMessage());
    }
}
```

### 2. 流式对话调用

```java
@GetMapping("/stream-chat")
public SseEmitter streamChat() {
    BailianRequest request = new BailianRequest();
    request.setAppId("your-app-id");
    request.setPrompt("请详细解释人工智能");
    
    return bailianService.streamChat(request);
}
```

### 3. 文件处理调用

```java
public void processFile(MultipartFile file) {
    try {
        BailianResponse response = bailianService.processFile(
            file, 
            "your-app-id", 
            "请分析这个文件的内容"
        );
        System.out.println("分析结果: " + response.getText());
    } catch (Exception e) {
        System.err.println("文件处理失败: " + e.getMessage());
    }
}
```

## 错误处理

系统提供了完整的异常处理机制：

- `BailianException`: 业务异常
- `ApiException`: API调用异常
- `NoApiKeyException`: API密钥异常
- `InputRequiredException`: 输入参数异常

所有异常都会被全局异常处理器捕获并返回友好的错误信息。

## 注意事项

1. **API密钥安全**: 请妥善保管您的API密钥，不要在代码中硬编码
2. **会话管理**: 系统会自动清理过期会话，默认30分钟超时
3. **文件限制**: 上传文件大小限制为10MB，仅支持文本类型文件
4. **并发限制**: 请根据您的百炼套餐配置合理控制并发请求数
5. **错误重试**: 系统内置重试机制，默认重试3次

## 测试建议

建议编写单元测试来验证功能：

```bash
# 运行测试
mvn test -Dtest=BailianServiceTest
```

## 技术支持

如有问题，请查看：
1. 阿里百炼官方文档
2. DashScope SDK 文档
3. 项目日志文件
