# PaddleOCR 安装和问题解决指南

## 问题描述
当前PaddleOCR识别功能不可用，主要原因是Python环境中缺少PaddleOCR相关依赖包。

## 快速解决方案

### 方法一：使用快速安装脚本（推荐）
```bash
# 运行快速安装脚本
scripts\quick_install.bat
```

### 方法二：手动安装（如果脚本失败）
在命令行中依次执行以下命令：

```bash
# 使用国内镜像源，速度更快
pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple

# 如果上面的命令失败，使用默认源
pip install paddleocr
```

### 方法三：完整安装所有依赖
```bash
pip install paddlepaddle -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install opencv-python -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install pillow -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install numpy -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 验证安装

### 1. 简单验证
```bash
python -c "import paddleocr; print('PaddleOCR安装成功')"
```

### 2. 完整测试
```bash
python scripts/test_paddleocr.py
```

## 安装后操作

1. **重启Java应用**：安装完成后需要重启应用以重新检测Python环境
2. **测试OCR功能**：通过API接口测试PaddleOCR识别功能
3. **查看日志**：检查应用日志确认PaddleOCR初始化成功

## 常见问题

### Q1: 安装速度很慢
**解决方案**：使用国内镜像源
```bash
pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### Q2: 网络连接错误
**解决方案**：
1. 检查网络连接
2. 尝试不同的镜像源
3. 使用VPN或代理

### Q3: 权限错误
**解决方案**：
1. 以管理员身份运行命令行
2. 使用用户安装：`pip install --user paddleocr`

### Q4: Python版本不兼容
**解决方案**：
1. 确保Python版本 >= 3.7
2. 升级Python到最新版本

## 技术说明

### 修复内容
1. **增强环境检查**：不仅检查Python，还检查PaddleOCR包是否可用
2. **改进错误提示**：当PaddleOCR不可用时，提供详细的安装指导
3. **优化安装脚本**：使用国内镜像源，提高安装成功率
4. **完善日志信息**：提供更清晰的状态信息和错误提示

### 代码变更
- `SimplePaddleOCRService.java`：增强环境检查和错误处理
- `scripts/quick_install.bat`：新增快速安装脚本
- `scripts/install_paddleocr.bat`：优化原有安装脚本

## 联系支持
如果按照以上步骤仍无法解决问题，请提供：
1. Python版本信息
2. 错误日志
3. 网络环境信息
