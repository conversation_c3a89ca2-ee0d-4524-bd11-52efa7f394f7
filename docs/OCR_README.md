# OCR文字识别集成说明

本项目集成了两种OCR引擎：**PaddleOCR** 和 **Tesseract**，提供强大的中英文文字识别功能。

## 🚀 功能特性

### PaddleOCR
- ✅ 支持中英文混合识别
- ✅ 高精度文本检测和识别
- ✅ 支持多种图片格式
- ✅ 自动模型下载
- ✅ 模拟模式支持（开发阶段）

### Tesseract
- ✅ 经典开源OCR引擎
- ✅ 支持多语言
- ✅ 稳定可靠
- ✅ 轻量级部署

## 📦 依赖配置

### Maven依赖
```xml
<!-- PaddleOCR相关依赖 -->
<dependency>
    <groupId>ai.djl</groupId>
    <artifactId>api</artifactId>
    <version>0.20.0</version>
</dependency>
<dependency>
    <groupId>ai.djl.paddlepaddle</groupId>
    <artifactId>paddlepaddle-engine</artifactId>
    <version>0.20.0</version>
</dependency>
<dependency>
    <groupId>ai.djl.paddlepaddle</groupId>
    <artifactId>paddlepaddle-model-zoo</artifactId>
    <version>0.20.0</version>
</dependency>

<!-- Tesseract相关依赖 -->
<dependency>
    <groupId>net.sourceforge.tess4j</groupId>
    <artifactId>tess4j</artifactId>
    <version>5.2.1</version>
</dependency>
```

## ⚙️ 配置说明

### application.yml配置
```yaml
# PaddleOCR配置
paddle:
  ocr:
    enable: true
    mock-mode: true  # 开发阶段使用模拟模式
    model-dir: models/ocr
    max-image-size: 10485760  # 10MB
    supported-formats: jpg,jpeg,png,bmp,gif

# Tesseract OCR配置
tesseract:
  ocr:
    enable: true
    data-path: tessdata
    language: chi_sim+eng
    timeout: 30000
```

## 📁 目录结构

```
项目根目录/
├── models/
│   └── ocr/                    # PaddleOCR模型目录（自动创建）
├── tessdata/                   # Tesseract训练数据目录
│   ├── chi_sim.traineddata     # 中文简体训练数据
│   └── eng.traineddata         # 英文训练数据
└── src/
    └── main/
        ├── java/
        │   └── com/xygk/api/ocr/
        │       ├── PaddleOCRService.java
        │       ├── OCRService.java
        │       └── OCRController.java
        └── resources/
            └── static/
                └── ocr.html    # OCR前端页面
```

## 🔧 安装步骤

### 1. PaddleOCR设置

PaddleOCR支持自动模型下载，首次运行时会自动下载所需模型。

**手动下载模型（可选）：**
```bash
# 创建模型目录
mkdir -p models/ocr

# 下载中文检测模型
wget https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_det_infer.tar
tar -xf ch_PP-OCRv3_det_infer.tar -C models/ocr/

# 下载中文识别模型
wget https://paddleocr.bj.bcebos.com/PP-OCRv3/chinese/ch_PP-OCRv3_rec_infer.tar
tar -xf ch_PP-OCRv3_rec_infer.tar -C models/ocr/
```

### 2. Tesseract设置

**下载训练数据：**
```bash
# 创建数据目录
mkdir -p tessdata

# 下载中文简体训练数据
wget https://github.com/tesseract-ocr/tessdata/raw/main/chi_sim.traineddata -O tessdata/chi_sim.traineddata

# 下载英文训练数据
wget https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata -O tessdata/eng.traineddata
```

## 🌐 API接口

### 1. 智能OCR识别
```http
POST /api/ocr/recognize
Content-Type: multipart/form-data

参数：
- image: 图片文件
- engine: 首选引擎 (paddle/tesseract，默认paddle)
```

### 2. PaddleOCR识别
```http
POST /api/ocr/paddle/recognize
Content-Type: multipart/form-data

参数：
- image: 图片文件
```

### 3. Tesseract识别
```http
POST /api/ocr/tesseract/recognize
Content-Type: multipart/form-data

参数：
- image: 图片文件
```

### 4. 服务状态查询
```http
GET /api/ocr/status
```

## 📱 前端使用

访问 `http://localhost:8080/ocr.html` 使用Web界面：

1. **选择识别引擎**：智能识别、PaddleOCR、Tesseract
2. **上传图片**：支持拖拽或点击上传
3. **开始识别**：自动识别图片中的文字
4. **查看结果**：显示识别的文本内容
5. **操作结果**：复制文本或下载为文件

## 🔍 响应格式

```json
{
  "success": true,
  "message": "识别成功",
  "texts": [
    "识别到的第一行文字",
    "识别到的第二行文字"
  ],
  "timestamp": 1640995200000,
  "engine": "PaddleOCR"
}
```

## 🚨 常见问题

### 1. PaddleOCR模型下载失败
- **原因**：网络连接问题或防火墙限制
- **解决**：启用模拟模式或手动下载模型文件

### 2. Tesseract识别精度低
- **原因**：训练数据不完整或图片质量差
- **解决**：确保下载完整的训练数据，提高图片质量

### 3. 内存不足
- **原因**：模型文件较大，需要足够内存
- **解决**：增加JVM内存设置：`-Xmx2g`

## 🔧 开发模式

在开发阶段，可以启用模拟模式避免模型下载：

```yaml
paddle:
  ocr:
    mock-mode: true  # 启用模拟模式
```

模拟模式会返回示例文本，便于前端开发和测试。

## 📈 性能优化

1. **模型缓存**：模型加载后会缓存在内存中
2. **图片预处理**：自动调整图片大小和格式
3. **并发处理**：支持多线程并发识别
4. **资源管理**：自动清理临时文件和资源

## 🛡️ 安全考虑

1. **文件大小限制**：最大10MB
2. **文件类型验证**：仅支持图片格式
3. **临时文件清理**：自动删除处理后的临时文件
4. **错误处理**：完善的异常处理和日志记录

## 📞 技术支持

如遇到问题，请检查：
1. 日志输出中的错误信息
2. 模型文件是否正确下载
3. 训练数据文件是否存在
4. 网络连接是否正常

更多技术细节请参考源码注释和日志输出。
