# 前端页面使用指南

## 概述

本项目提供了多个前端页面来测试和使用阿里百炼智能体功能，包括完整的聊天界面和简单的测试页面。

## 页面列表

### 1. 首页 (index.html)
- **访问地址**: http://localhost:8080/
- **功能**: 系统导航和功能介绍
- **特点**: 响应式设计，功能模块导航

### 2. 百炼智能体聊天页面 (bailian-chat.html)
- **访问地址**: http://localhost:8080/bailian-chat.html
- **功能**: 完整的聊天界面，支持所有百炼功能
- **特点**: 现代化UI，实时对话，文件处理

### 3. 百炼测试页面 (test-bailian.html)
- **访问地址**: http://localhost:8080/test-bailian.html
- **功能**: 简单的API测试界面
- **特点**: 功能测试，API调试

## 快速开始

### 1. 启动应用
```bash
# 方式1: 使用Maven
mvn spring-boot:run

# 方式2: 运行JAR包
java -jar target/gk-client-api.jar
```

### 2. 访问页面
打开浏览器访问: http://localhost:8080/

### 3. 配置应用ID
在页面中输入您的百炼应用ID（已预设默认值）

### 4. 开始对话
选择对话模式，输入问题，点击发送

## 功能详解

### 普通对话
- 标准的问答模式
- 支持单轮对话
- 适合简单问题咨询

**使用方法**:
1. 选择"普通对话"模式
2. 输入问题
3. 点击"发送消息"

### 流式对话
- 实时响应显示
- 模拟打字效果
- 适合长文本生成

**使用方法**:
1. 选择"流式对话"模式
2. 输入问题
3. 观察实时响应

### 多轮对话
- 支持上下文理解
- 连续对话能力
- 适合复杂交互

**使用方法**:
1. 选择"多轮对话"模式
2. 进行连续对话
3. AI会记住对话历史

### 知识库检索
- 基于知识库问答
- 专业领域查询
- 准确性更高

**使用方法**:
1. 选择"知识库检索"模式
2. 输入查询关键词
3. 获取知识库结果

### 文件处理
- 支持文本文件上传
- AI分析文件内容
- 自定义处理要求

**支持格式**:
- .txt (文本文件)
- .json (JSON数据)
- .xml (XML文档)
- .md (Markdown文档)

**使用方法**:
1. 点击文件上传区域
2. 选择要处理的文件
3. 输入处理要求
4. 点击"处理文件"

## 界面说明

### 聊天界面 (bailian-chat.html)

#### 头部区域
- 系统标题和模式指示器
- 应用ID配置输入框
- 对话模式选择器

#### 消息区域
- 用户消息（右侧蓝色气泡）
- AI回复（左侧灰色气泡）
- 时间戳显示
- 加载状态提示

#### 输入区域
- 工具栏（文件、清除、历史、新会话）
- 文本输入框
- 发送按钮
- 快捷键提示（Ctrl+Enter）

### 测试界面 (test-bailian.html)

#### 配置区域
- 应用ID设置
- 对话模式选择

#### 操作区域
- 消息输入框
- 文件上传区域
- 功能按钮组

#### 结果区域
- 响应显示
- 历史记录
- 状态提示

## 配置说明

### 应用ID配置
```javascript
// 默认应用ID（可在页面中修改）
const defaultAppId = "3267ecc2febd4c37b0baf9a08957daaa";
```

### API端点配置
```javascript
// API基础路径
const baseURL = "/api/bailian";

// 具体端点
const endpoints = {
    chat: "/api/bailian/chat",
    stream: "/api/bailian/chat/stream", 
    multiTurn: "/api/bailian/chat/multi-turn",
    knowledge: "/api/bailian/knowledge/search",
    fileProcess: "/api/bailian/file/process",
    history: "/api/bailian/session/{sessionId}/history"
};
```

## 常见问题

### 1. 页面无法访问
**问题**: 浏览器显示"无法访问此网站"
**解决方案**:
- 确认Spring Boot应用已启动
- 检查端口8080是否被占用
- 确认防火墙设置

### 2. API调用失败
**问题**: 显示"网络错误"或"请求失败"
**解决方案**:
- 检查应用ID是否正确
- 确认API密钥配置
- 查看浏览器控制台错误信息
- 检查后端日志

### 3. 文件上传失败
**问题**: 文件处理返回错误
**解决方案**:
- 确认文件大小不超过10MB
- 检查文件格式是否支持
- 确认文件内容不为空

### 4. 流式对话不工作
**问题**: 流式对话显示异常
**解决方案**:
- 当前为模拟流式效果
- 检查网络连接稳定性
- 尝试普通对话模式

## 开发调试

### 浏览器开发者工具
1. 按F12打开开发者工具
2. 查看Console标签页的错误信息
3. 查看Network标签页的请求状态
4. 检查Elements标签页的DOM结构

### 常用调试命令
```javascript
// 在浏览器控制台中执行

// 检查当前会话ID
console.log('当前会话ID:', sessionId);

// 测试API连接
fetch('/actuator/health').then(r => r.json()).then(console.log);

// 查看本地存储
console.log('本地存储:', localStorage);
```

### 网络请求调试
```bash
# 使用curl测试API
curl -X POST http://localhost:8080/api/bailian/chat \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "your-app-id",
    "prompt": "你好",
    "sessionId": "test-session"
  }'
```

## 性能优化

### 前端优化
- 使用CDN加载第三方库
- 启用浏览器缓存
- 压缩静态资源

### 用户体验优化
- 添加加载动画
- 实现错误重试
- 优化移动端显示

### 安全考虑
- 验证用户输入
- 防止XSS攻击
- 限制文件上传类型和大小

## 扩展开发

### 添加新功能
1. 在HTML中添加UI元素
2. 在JavaScript中实现功能逻辑
3. 调用对应的后端API
4. 处理响应和错误

### 自定义样式
1. 修改CSS样式
2. 添加新的样式类
3. 实现响应式设计
4. 优化用户体验

### 集成其他服务
1. 添加新的API端点
2. 实现对应的前端调用
3. 处理不同的响应格式
4. 统一错误处理机制

## 技术支持

### 获取帮助
1. 查看浏览器控制台错误
2. 检查后端应用日志
3. 参考API文档
4. 联系技术支持

### 反馈问题
请提供以下信息：
- 浏览器类型和版本
- 错误截图或描述
- 操作步骤
- 控制台错误信息

### 更新日志
- v1.0.0: 初始版本，基础功能
- v1.1.0: 添加文件处理功能
- v1.2.0: 优化用户界面
- v1.3.0: 添加测试页面
