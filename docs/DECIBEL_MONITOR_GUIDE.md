# 课堂分贝检测器使用指南

## 概述

课堂分贝检测器是一个基于Web Audio API的实时音量监测工具，专为课堂环境设计，旨在通过可视化的方式激励学生积极朗读，提供量化的音量反馈。

## 功能特性

### 核心功能
- **实时分贝检测**: 使用Web Audio API获取麦克风音频输入，实时计算并显示分贝值
- **可视化反馈**: 提供数字显示、进度条和颜色变化等多种视觉反馈方式
- **智能分级**: 根据分贝值自动判断音量等级（安静、正常、活跃、响亮）
- **统计分析**: 实时计算平均分贝、最高分贝、活跃时长等统计数据
- **历史记录**: 支持数据存储和历史查询功能

### 技术特性
- **响应式设计**: 适配桌面端、平板和手机等不同设备
- **浏览器兼容**: 支持现代浏览器的Web Audio API
- **数据同步**: 可选的后端数据存储和同步功能
- **无障碍支持**: 提供屏幕阅读器支持和高对比度模式

## 使用方法

### 1. 访问页面
在浏览器中访问：`http://localhost:8080/decibel-monitor.html`

### 2. 授权麦克风
- 点击"开始检测"按钮
- 浏览器会请求麦克风权限，点击"允许"
- 等待系统初始化完成

### 3. 开始监测
- 权限授权后，系统自动开始实时检测
- 分贝值会实时更新显示
- 根据音量大小，状态文本和颜色会相应变化

### 4. 查看统计
- 页面下方显示实时统计信息
- 包括平均分贝、最高分贝、活跃时长和总时长

### 5. 停止监测
- 点击"停止检测"按钮结束监测
- 系统会保存本次会话的统计数据

## 分贝等级说明

| 等级 | 分贝范围 | 颜色 | 建议 |
|------|----------|------|------|
| 安静 | < 40 dB | 绿色 | 太安静了，请大声朗读！ |
| 正常 | 40-60 dB | 蓝色 | 音量正常，继续保持！ |
| 活跃 | 60-80 dB | 橙色 | 很好！朗读很积极！ |
| 响亮 | > 80 dB | 红色 | 声音太大了，请适当降低音量 |

## 技术实现

### 前端技术
- **HTML5**: 页面结构和语义化标记
- **CSS3**: 响应式布局和动画效果
- **JavaScript ES6+**: 核心逻辑和Web Audio API调用
- **Web Audio API**: 音频数据获取和处理

### 后端技术
- **Spring Boot**: RESTful API服务
- **Java**: 后端业务逻辑
- **内存存储**: 会话数据临时存储（可扩展为数据库）

### API接口

#### 1. 创建监测会话
```http
POST /api/classroom/decibel/session/start
Content-Type: application/json

{
  "thresholds": {
    "quiet": 40,
    "normal": 60,
    "active": 80
  }
}
```

#### 2. 记录分贝数据
```http
POST /api/classroom/decibel/session/{sessionId}/record
Content-Type: application/json

{
  "decibel": 65.5,
  "timestamp": 1692614400000
}
```

#### 3. 批量记录数据
```http
POST /api/classroom/decibel/session/{sessionId}/batch-record
Content-Type: application/json

{
  "data": [
    {"decibel": 65.5, "timestamp": 1692614400000},
    {"decibel": 67.2, "timestamp": 1692614401000}
  ]
}
```

#### 4. 获取统计信息
```http
GET /api/classroom/decibel/session/{sessionId}/stats
```

#### 5. 获取历史数据
```http
GET /api/classroom/decibel/session/{sessionId}/history?limit=100
```

#### 6. 结束会话
```http
POST /api/classroom/decibel/session/{sessionId}/end
```

## 配置说明

### 分贝阈值配置
可以通过修改JavaScript中的`thresholds`对象来调整分贝等级：

```javascript
this.thresholds = {
    quiet: 40,    // 安静阈值
    normal: 60,   // 正常阈值
    active: 80    // 活跃阈值
};
```

### 数据同步配置
可以调整数据同步频率：

```javascript
this.syncInterval = 5000; // 5秒同步一次
```

## 浏览器兼容性

### 支持的浏览器
- Chrome 66+
- Firefox 60+
- Safari 11.1+
- Edge 79+

### 不支持的浏览器
- Internet Explorer（所有版本）
- 旧版本的移动浏览器

### 检测方法
系统会自动检测浏览器是否支持Web Audio API，不支持时会显示错误提示。

## 故障排除

### 常见问题

#### 1. 无法获取麦克风权限
- **原因**: 用户拒绝权限或浏览器不支持
- **解决**: 检查浏览器设置，确保允许网站访问麦克风

#### 2. 分贝值显示为0或异常
- **原因**: 麦克风未正常工作或音频处理异常
- **解决**: 检查麦克风连接，刷新页面重试

#### 3. 数据同步失败
- **原因**: 后端服务未启动或网络连接问题
- **解决**: 检查后端服务状态，确保网络连接正常

#### 4. 页面在移动设备上显示异常
- **原因**: 移动浏览器的Web Audio API支持有限
- **解决**: 使用桌面浏览器或更新移动浏览器版本

### 调试方法

#### 1. 开启浏览器控制台
按F12打开开发者工具，查看Console标签页的错误信息。

#### 2. 检查网络请求
在Network标签页查看API请求是否正常。

#### 3. 测试麦克风
在浏览器设置中测试麦克风是否正常工作。

## 扩展功能

### 1. 数据库存储
可以将内存存储替换为数据库存储，支持长期数据保存：

```java
@Entity
public class DecibelRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String sessionId;
    private Double decibel;
    private Long timestamp;
    private LocalDateTime recordTime;
    
    // getters and setters
}
```

### 2. 实时图表
集成Chart.js等图表库，提供实时分贝变化图表：

```html
<canvas id="decibelChart" width="400" height="200"></canvas>
```

### 3. 多用户支持
支持多个用户同时使用，每个用户独立的会话管理。

### 4. 数据导出
支持将统计数据导出为Excel或PDF格式。

### 5. 声音事件检测
检测特定的声音事件，如掌声、笑声等。

## 安全考虑

### 1. 隐私保护
- 音频数据仅在本地处理，不上传原始音频
- 仅上传分贝数值，不包含任何语音内容
- 支持完全离线模式

### 2. 权限管理
- 严格的麦克风权限控制
- 用户可随时撤销权限

### 3. 数据安全
- 会话数据加密传输
- 支持数据自动清理

## 性能优化

### 1. 音频处理优化
- 使用Web Workers进行音频数据处理
- 优化FFT计算频率

### 2. 数据传输优化
- 批量上传减少网络请求
- 数据压缩传输

### 3. 内存管理
- 定期清理历史数据
- 优化数据结构

## 更新日志

### v1.0.0 (2025-08-21)
- 初始版本发布
- 实现基本的分贝检测功能
- 支持实时统计和历史记录
- 提供RESTful API接口

## 联系支持

如有问题或建议，请联系开发团队：
- 邮箱: <EMAIL>
- 文档: [项目文档](./README.md)
- 问题反馈: [GitHub Issues](https://github.com/example/project/issues)
