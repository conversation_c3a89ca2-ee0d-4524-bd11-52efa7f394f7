# 🔽 OCR模型手动下载指南

由于网络环境限制，自动下载脚本可能无法正常工作。请按照以下步骤手动下载所需的模型文件。

## 📁 目录结构

首先确保项目中有以下目录结构：

```
项目根目录/
├── models/
│   └── ocr/          # PaddleOCR模型目录
└── tessdata/         # Tesseract训练数据目录
```

## 🎯 必需文件列表

### 1. Tesseract训练数据（必须下载）

请下载以下文件到 `tessdata/` 目录：

| 文件名 | 下载地址 | 文件大小 | 用途 |
|--------|----------|----------|------|
| chi_sim.traineddata | [点击下载](https://github.com/tesseract-ocr/tessdata/raw/main/chi_sim.traineddata) | ~25MB | 中文简体识别 |
| eng.traineddata | [点击下载](https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata) | ~4MB | 英文识别 |
| osd.traineddata | [点击下载](https://github.com/tesseract-ocr/tessdata/raw/main/osd.traineddata) | ~10MB | 方向检测 |

### 2. PaddleOCR模型（推荐下载）

请下载以下文件到 `models/ocr/` 目录：

| 文件名 | 下载地址 | 文件大小 | 用途 |
|--------|----------|----------|------|
| ch_PP-OCRv4_det_infer.tar | [点击下载](https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_det_infer.tar) | ~3MB | 文本检测模型 |
| ch_PP-OCRv4_rec_infer.tar | [点击下载](https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_rec_infer.tar) | ~10MB | 文本识别模型 |
| ch_ppocr_mobile_v2.0_cls_infer.tar | [点击下载](https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar) | ~1MB | 方向分类模型 |

## 📥 下载步骤

### 方法1: 浏览器下载（推荐）

1. **创建目录**
   ```cmd
   mkdir models\ocr
   mkdir tessdata
   ```

2. **下载Tesseract数据**
   - 右键点击上表中的下载链接
   - 选择"另存为"
   - 保存到 `tessdata/` 目录

3. **下载PaddleOCR模型**
   - 右键点击上表中的下载链接
   - 选择"另存为"
   - 保存到 `models/ocr/` 目录

4. **解压PaddleOCR模型**
   ```cmd
   cd models\ocr
   tar -xf ch_PP-OCRv4_det_infer.tar
   tar -xf ch_PP-OCRv4_rec_infer.tar
   tar -xf ch_ppocr_mobile_v2.0_cls_infer.tar
   ```

### 方法2: 使用下载工具

如果浏览器下载失败，可以使用以下工具：

- **IDM (Internet Download Manager)**
- **迅雷**
- **wget for Windows**
- **aria2**

### 方法3: 国内镜像源（如果可用）

如果官方源下载慢，可以尝试以下镜像：

- **清华大学镜像**: https://mirrors.tuna.tsinghua.edu.cn/
- **阿里云镜像**: https://mirrors.aliyun.com/
- **华为云镜像**: https://mirrors.huaweicloud.com/

## 🔧 解压说明

### Windows解压命令

如果系统有tar命令：
```cmd
# 进入模型目录
cd models\ocr

# 解压所有模型
tar -xf ch_PP-OCRv4_det_infer.tar
tar -xf ch_PP-OCRv4_rec_infer.tar
tar -xf ch_ppocr_mobile_v2.0_cls_infer.tar

# 删除压缩文件（可选）
del *.tar
```

如果没有tar命令，可以使用：
- **7-Zip**
- **WinRAR**
- **Windows内置解压**

## ✅ 验证下载

下载完成后，目录结构应该如下：

```
项目根目录/
├── models/
│   └── ocr/
│       ├── ch_PP-OCRv4_det_infer/
│       │   ├── inference.pdmodel
│       │   ├── inference.pdiparams
│       │   └── inference.pdiparams.info
│       ├── ch_PP-OCRv4_rec_infer/
│       │   ├── inference.pdmodel
│       │   ├── inference.pdiparams
│       │   └── inference.pdiparams.info
│       └── ch_ppocr_mobile_v2.0_cls_infer/
│           ├── inference.pdmodel
│           ├── inference.pdiparams
│           └── inference.pdiparams.info
└── tessdata/
    ├── chi_sim.traineddata
    ├── eng.traineddata
    └── osd.traineddata
```

### 验证命令

```cmd
# 检查Tesseract数据
dir tessdata\*.traineddata

# 检查PaddleOCR模型
dir models\ocr\*\*.pdmodel
```

## ⚙️ 配置应用

下载完成后，修改配置文件：

### 编辑 `src/main/resources/application.yml`

```yaml
# PaddleOCR配置
paddle:
  ocr:
    enable: true
    mock-mode: false  # 改为false启用真实模型
    model-dir: models/ocr
    
# Tesseract OCR配置
tesseract:
  ocr:
    enable: true
    data-path: tessdata
    language: chi_sim+eng
```

## 🚀 启动测试

1. **重启应用**
   ```cmd
   mvn spring-boot:run
   ```

2. **查看启动日志**
   应该看到类似输出：
   ```
   PaddleOCR模型目录: E:\...\models\ocr
   Tesseract数据目录: E:\...\tessdata
   PaddleOCR初始化成功
   文本检测模型加载成功
   文本识别模型加载成功
   ```

3. **测试功能**
   访问: http://localhost:8080/ocr.html

## 🔄 备用方案

如果仍然无法下载，可以：

### 1. 使用模拟模式
在 `application.yml` 中保持：
```yaml
paddle:
  ocr:
    mock-mode: true  # 使用模拟模式
```

### 2. 仅使用Tesseract
如果只下载了Tesseract数据：
```yaml
paddle:
  ocr:
    enable: false  # 禁用PaddleOCR
tesseract:
  ocr:
    enable: true   # 仅使用Tesseract
```

### 3. 离线获取模型
- 从其他有网络的机器下载
- 使用U盘或网络共享传输
- 联系同事或朋友代为下载

## 📞 技术支持

如果遇到问题：

1. **检查文件完整性**
   - 确认文件大小是否正确
   - 尝试重新下载损坏的文件

2. **检查目录权限**
   - 确保应用有读取模型文件的权限

3. **查看应用日志**
   - 启动时的错误信息
   - OCR识别时的异常日志

4. **网络问题**
   - 尝试使用VPN或代理
   - 联系网络管理员

## 🎯 最小配置

如果存储空间有限，最小配置只需要：

**Tesseract（必需）:**
- chi_sim.traineddata (中文)
- eng.traineddata (英文)

**PaddleOCR（可选但推荐）:**
- ch_PP-OCRv4_det_infer (检测)
- ch_PP-OCRv4_rec_infer (识别)

总大小约: 40MB

这样就可以实现基本的中英文OCR功能了！
