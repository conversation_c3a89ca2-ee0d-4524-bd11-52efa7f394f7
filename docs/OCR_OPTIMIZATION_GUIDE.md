# OCR 识别优化指南

## 问题描述

Tesseract OCR 在识别中文文档时容易产生乱码，例如：
```
LLSO%2LL626122L0%E wees
BEMNIY HN
土 业 谊 肌 白 晔 口 春 昼 澈 口
日 B H LL 寺 6461
盂 骄 x [ :
```

## 解决方案

### 1. 优先使用 PaddleOCR

PaddleOCR 对中文识别有更好的准确性，已经配置为主要的 OCR 引擎。

#### 安装 PaddleOCR 环境

```bash
# 运行安装脚本
python scripts/install_paddleocr.py

# 或手动安装
pip install paddlepaddle paddleocr opencv-python pillow
```

#### 测试 PaddleOCR

```bash
# 测试环境
python scripts/test_paddleocr.py

# 测试识别
python scripts/paddle_ocr.py <图片路径>
```

### 2. Tesseract 优化配置

作为备用方案，Tesseract 已经进行了以下优化：

#### 配置优化
- **语言设置**: 仅使用中文 (`chi_sim`)，避免英文干扰
- **页面分割**: 使用模式 6（单一文本块）
- **缩放因子**: 增加到 2.0，提高图像清晰度
- **乱码清理**: 启用激进的乱码清理算法

#### 乱码清理算法
1. **模式识别清理**: 移除类似 `LLSO%2LL626122L0%E` 的乱码模式
2. **重复字符清理**: 移除连续重复的字符
3. **特殊符号清理**: 移除包含 `%` 和数字的乱码
4. **英文乱码清理**: 移除纯英文乱码词

### 3. 使用建议

#### API 调用优先级
1. **智能识别**: `/api/ocr/recognize` - 自动选择最佳引擎
2. **PaddleOCR**: `/api/ocr/paddle/recognize` - 高精度中文识别
3. **Tesseract**: `/api/ocr/tesseract/recognize` - 备用方案

#### 图片质量建议
1. **分辨率**: 建议 300 DPI 或更高
2. **格式**: 推荐 PNG 格式，避免 JPEG 压缩损失
3. **对比度**: 确保文字与背景有足够对比度
4. **清晰度**: 避免模糊、倾斜的图片

## 配置说明

### PaddleOCR 配置 (application.yml)

```yaml
paddle:
  ocr:
    enable: true
    mock-mode: false  # 启用真实识别
    python-path: python  # Python 解释器路径
    script-path: scripts/paddle_ocr.py  # 优化脚本路径
```

### Tesseract 配置 (application.yml)

```yaml
tesseract:
  ocr:
    enable: true
    language: chi_sim  # 仅中文
    page-seg-mode: 6   # 单一文本块
    scale-factor: 2.0  # 图像缩放
    chinese-only: true # 中文专用模式
    aggressive-cleaning: true  # 激进乱码清理
```

## 故障排除

### PaddleOCR 问题

1. **Python 环境问题**
   ```bash
   # 检查 Python 版本（需要 3.7+）
   python --version
   
   # 检查依赖
   pip list | grep paddle
   ```

2. **模型下载问题**
   - 首次使用会自动下载模型
   - 确保网络连接正常
   - 模型存储在 `~/.paddleocr/` 目录

3. **内存不足**
   - 减小图片尺寸
   - 关闭其他应用程序

### Tesseract 问题

1. **训练数据缺失**
   ```bash
   # 检查训练数据
   ls tessdata/
   # 应该包含 chi_sim.traineddata
   ```

2. **识别结果仍有乱码**
   - 启用激进清理模式
   - 调整图片预处理参数
   - 考虑使用 PaddleOCR

## 性能对比

| 引擎 | 中文准确率 | 速度 | 资源占用 | 推荐场景 |
|------|------------|------|----------|----------|
| PaddleOCR | 高 (90%+) | 中等 | 较高 | 高质量中文识别 |
| Tesseract | 中等 (70%+) | 快 | 较低 | 简单文档、备用方案 |

## 最佳实践

1. **优先使用 PaddleOCR** 进行中文文档识别
2. **图片预处理** 提高识别质量
3. **结果后处理** 清理乱码和无效内容
4. **错误处理** 提供降级方案
5. **性能监控** 记录识别成功率和耗时

## 更新日志

- **2024-07-08**: 优化 PaddleOCR 脚本，改进乱码清理算法
- **2024-07-08**: 增加 Tesseract 激进清理模式
- **2024-07-08**: 添加 Python 环境安装脚本
