#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试图片用于OCR识别测试
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_idcard_image():
    """创建一个模拟身份证的测试图片"""
    # 创建图片
    width, height = 800, 500
    img = Image.new('RGB', (width, height), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    try:
        # 尝试使用系统字体
        font_large = ImageFont.truetype("arial.ttf", 24)
        font_medium = ImageFont.truetype("arial.ttf", 18)
    except:
        # 如果没有找到字体，使用默认字体
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
    
    # 添加身份证信息文本
    y_pos = 50
    line_height = 40
    
    texts = [
        "姓名: 张三",
        "性别: 男",
        "民族: 汉族", 
        "出生: 1990年01月01日",
        "住址: 北京市朝阳区测试街道123号",
        "身份证号: 110101199001011234"
    ]
    
    for text in texts:
        draw.text((50, y_pos), text, fill='black', font=font_medium)
        y_pos += line_height
    
    # 保存图片
    output_path = "test_idcard.jpg"
    img.save(output_path, "JPEG", quality=95)
    print(f"测试图片已创建: {output_path}")
    return output_path

if __name__ == "__main__":
    create_test_idcard_image()
