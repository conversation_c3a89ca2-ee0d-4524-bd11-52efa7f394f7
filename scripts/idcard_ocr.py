#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证专用OCR识别脚本（兼容版本）
基于PaddleOCR，专门优化身份证识别准确性
"""

import sys
import os
import json
import argparse
import re
import warnings
import threading
import time
from pathlib import Path

# 设置编码和抑制警告
os.environ['PYTHONIOENCODING'] = 'utf-8'
warnings.filterwarnings('ignore')

# 重定向标准输出，确保UTF-8编码
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

# 完全抑制PaddleOCR的输出
os.environ['GLOG_minloglevel'] = '3'  # 抑制glog日志
os.environ['GLOG_v'] = '0'
os.environ['FLAGS_eager_delete_tensor_gb'] = '0'

try:
    # 重定向所有输出到null
    import io
    from contextlib import redirect_stdout, redirect_stderr
    
    # 创建空的输出流
    null_stream = io.StringIO()
    
    # 在导入PaddleOCR时完全抑制输出
    with redirect_stdout(null_stream), redirect_stderr(null_stream):
        from paddleocr import PaddleOCR
        import cv2
        import numpy as np
        from PIL import Image, ImageEnhance, ImageFilter
except ImportError as e:
    print(f"ERROR: 缺少必要的依赖包: {e}")
    print("请安装: pip install paddleocr opencv-python pillow")
    sys.exit(1)

# 全局OCR实例，实现单例模式
_ocr_instance = None
_ocr_lock = threading.Lock()

def get_ocr_instance():
    """获取OCR实例（单例模式）- 极速优化版本"""
    global _ocr_instance
    if _ocr_instance is None:
        with _ocr_lock:
            if _ocr_instance is None:
                try:
                    print("正在初始化PaddleOCR（极速优化版本）...")

                    # 完全抑制PaddleOCR的输出
                    import io
                    from contextlib import redirect_stdout, redirect_stderr
                    import logging

                    # 设置日志级别
                    logging.getLogger('ppocr').setLevel(logging.ERROR)
                    logging.getLogger('paddle').setLevel(logging.ERROR)

                    # 创建空的输出流
                    null_stream = io.StringIO()

                    # 完全抑制所有输出
                    with redirect_stdout(null_stream), redirect_stderr(null_stream):
                        # 设置环境变量抑制输出
                        os.environ['GLOG_minloglevel'] = '3'
                        os.environ['GLOG_v'] = '0'

                        # 使用极速优化配置
                        try:
                            # 极速配置：只使用兼容的参数
                            _ocr_instance = PaddleOCR(
                                lang='ch',
                                use_gpu=False,  # 使用CPU
                                # 降低检测分辨率以提高速度
                                det_limit_side_len=640,  # 适中的分辨率
                                det_limit_type='min'
                            )
                        except Exception as e:
                            print(f"极速配置失败，使用基本配置: {e}")
                            # 如果极速配置失败，使用最基本的配置
                            try:
                                _ocr_instance = PaddleOCR(lang='ch')
                            except:
                                _ocr_instance = PaddleOCR()

                    print("PaddleOCR极速实例初始化成功")
                except Exception as e:
                    print(f"ERROR: PaddleOCR初始化失败: {e}")
                    _ocr_instance = None
    return _ocr_instance

class IdCardOCR:
    """身份证专用OCR识别器"""
    
    def __init__(self, use_gpu=False):
        """
        初始化身份证OCR识别器

        Args:
            use_gpu: 是否使用GPU（兼容性参数，可能被忽略）
        """
        # 使用全局OCR实例，避免重复初始化
        self.ocr = get_ocr_instance()
        if self.ocr is None:
            print("ERROR: 无法获取OCR实例")
            sys.exit(1)
    
    def preprocess_idcard_image(self, image_path):
        """
        优化的身份证图像预处理 - 平衡速度和质量

        Args:
            image_path: 图像文件路径

        Returns:
            处理后的图像数组
        """
        try:
            print(f"DEBUG: 开始图像预处理: {image_path}")
            start_time = time.time()

            # 使用OpenCV直接读取（比PIL更快）
            img = cv2.imread(image_path)
            if img is None:
                raise ValueError(f"无法读取图像文件: {image_path}")

            # 转换为RGB格式
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

            # 获取图像尺寸
            height, width = img.shape[:2]
            print(f"DEBUG: 原始图像尺寸: {width}x{height}")

            # 优化的尺寸调整策略 - 减少处理时间
            max_width = 1200
            max_height = 800

            # 如果图像过大，缩小以提高处理速度
            if width > max_width or height > max_height:
                scale = min(max_width/width, max_height/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
                print(f"DEBUG: 图像缩放到: {new_width}x{new_height}")

            # 简化的图像增强 - 只做必要的处理
            # 检查图像质量，只在需要时进行增强
            gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            mean_brightness = np.mean(gray)
            contrast = np.std(gray)

            print(f"DEBUG: 图像亮度: {mean_brightness:.1f}, 对比度: {contrast:.1f}")

            # 只在图像质量较差时进行增强
            if mean_brightness < 80 or contrast < 30:
                print("DEBUG: 应用图像增强")
                # 轻量级的对比度和亮度调整
                alpha = 1.2 if contrast < 30 else 1.0  # 对比度
                beta = 20 if mean_brightness < 80 else 0  # 亮度
                img = cv2.convertScaleAbs(img, alpha=alpha, beta=beta)
            else:
                print("DEBUG: 图像质量良好，跳过增强")

            processing_time = time.time() - start_time
            print(f"DEBUG: 图像预处理完成，耗时: {processing_time:.2f}秒")

            return img

        except Exception as e:
            print(f"WARNING: 图像预处理失败，使用原图: {e}")
            # 如果预处理失败，直接返回原图的RGB版本
            try:
                img = cv2.imread(image_path)
                if img is not None:
                    return cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            except:
                pass
            raise e
    
    def clean_idcard_text(self, text):
        """
        清理身份证识别文本

        Args:
            text: 原始识别文本

        Returns:
            清理后的文本
        """
        if not text or not text.strip():
            return ""

        # 过滤乱码文本（优先处理）
        if '锟' in text or '拷' in text or '斤' in text:
            return ""

        # 过滤ANSI颜色代码（强化版）
        text = re.sub(r'\x1b\[[0-9;]*m', '', text)
        text = re.sub(r'\[3[0-9]m', '', text)  # 过滤颜色代码如[32m
        text = re.sub(r'\[33m', '', text)  # 过滤黄色代码
        text = re.sub(r'\[32m', '', text)  # 过滤绿色代码

        # 过滤系统日志和无关信息（大幅扩展关键词列表）
        system_keywords = [
            'warning', 'warn', 'creating', 'model', 'using', 'official',
            'downloaded', 'saved', 'paddlex', 'mkldnn', 'paddle',
            'users', '.paddlex', 'official_models', 'pp-', 'lcnet',
            'uvdoc', 'ocrv', 'server', 'det', 'rec', 'textline', 'ori',
            'automatically', 'files', 'will', 'be', 'mode', 'instead',
            'supported', 'run', 'not', 'the', 'in', 'and', 'to',
            'initializing', '初始化', '正在初始化', '成功', 'none',
            'x1_0_doc', 'x1_0_textline', 'ocrv5_server', 'c:', 'users',
            'model files', 'automatically downloaded', 'official model'
        ]

        text_lower = text.lower()
        for keyword in system_keywords:
            if keyword in text_lower:
                return ""

        # 过滤包含特殊字符的系统信息（强化版）
        if any(char in text for char in ['[', ']', '\\', '/', 'C:', '.paddlex', '\x1b', '(', ')', 'PP-']):
            return ""

        # 过滤包含数字和英文字母过多的系统信息
        if len(text) > 8:
            english_count = sum(1 for c in text if c.isalpha() and ord(c) < 128)
            if english_count > len(text) * 0.2:  # 如果英文字符超过20%，可能是系统信息
                return ""

        # 过滤纯数字或纯英文的短文本
        if len(text) < 6 and (text.isdigit() or text.isalpha()):
            return ""

        # 身份证专用文本清理
        # 保留中文、数字、常用标点和身份证特有字符
        text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s\.,;:!?()（）【】""''、。，；：！？年月日Xx]', '', text)

        # 移除过短的无意义文本
        if len(text.strip()) < 2:
            return ""

        # 特殊处理身份证常见误识别
        text = text.replace('O', '0')  # O替换为0
        text = text.replace('l', '1')  # l替换为1
        text = text.replace('I', '1')  # I替换为1

        return text.strip()
    
    def detect_card_type(self, texts):
        """
        检测身份证类型
        
        Args:
            texts: 识别文本列表
            
        Returns:
            卡片类型: 'front', 'back', 'unknown'
        """
        all_text = ' '.join(texts)
        
        # 正面特征
        front_keywords = ['姓名', '性别', '民族', '出生', '住址']
        front_score = sum(1 for keyword in front_keywords if keyword in all_text)
        
        # 背面特征
        back_keywords = ['签发机关', '有效期限', '公安局', '派出所']
        back_score = sum(1 for keyword in back_keywords if keyword in all_text)
        
        if front_score > back_score:
            return 'front'
        elif back_score > front_score:
            return 'back'
        else:
            return 'unknown'
    
    def extract_structured_info(self, texts, card_type):
        """
        提取结构化身份证信息

        Args:
            texts: 识别文本列表
            card_type: 卡片类型

        Returns:
            结构化信息字典
        """
        all_text = ' '.join(texts)
        info = {'card_type': card_type}

        # 打印调试信息
        print(f"DEBUG: 识别文本: {all_text}")
        print(f"DEBUG: 卡片类型: {card_type}")
        print(f"DEBUG: 文本行数: {len(texts)}")
        for i, text in enumerate(texts):
            print(f"DEBUG: 第{i+1}行: {text}")
        
        if card_type in ['front', 'unknown']:
            # 提取正面信息
            # 姓名 - 改进匹配逻辑，使用原始识别结果

            # 如果有原始识别结果，优先从中提取姓名
            if hasattr(self, '_raw_rec_texts'):
                raw_texts = self._raw_rec_texts
                print(f"DEBUG: 原始识别文本列表: {raw_texts}")

                # 查找包含"姓名"的文本行
                for text in raw_texts:
                    text = text.strip()
                    if '姓名' in text:
                        # 从"姓名党正芬"中提取"党正芬"
                        name_match = re.search(r'姓名(.+)', text)
                        if name_match:
                            name = name_match.group(1).strip()
                            if len(name) >= 2 and len(name) <= 4:
                                info['name'] = name
                                print(f"DEBUG: 从姓名行提取到姓名: {name}")
                                break

                # 如果没找到包含"姓名"的行，查找第一个纯中文姓名
                if 'name' not in info:
                    for text in raw_texts:
                        text = text.strip()
                        if (len(text) >= 2 and len(text) <= 4 and
                            all('\u4e00' <= c <= '\u9fff' for c in text) and
                            text not in ['公民', '身份', '号码', '证件', '中华', '人民', '共和国', '汉族', '回族', '蒙古', '藏族', '姓名', '性别', '民族', '出生', '住址']):
                            info['name'] = text
                            print(f"DEBUG: 从原始结果找到姓名: {text}")
                            break

            # 如果没找到，从处理后的文本中找
            if 'name' not in info:
                for text in texts:
                    text = text.strip()
                    # 如果这行只包含2-4个中文字符，可能是姓名
                    if (len(text) >= 2 and len(text) <= 4 and
                        all('\u4e00' <= c <= '\u9fff' for c in text) and
                        text not in ['公民', '身份', '号码', '证件', '中华', '人民', '共和国', '汉族', '回族', '蒙古', '藏族']):
                        info['name'] = text
                        print(f"DEBUG: 从处理文本找到姓名: {text}")
                        break

            # 如果还没找到，用正则表达式
            if 'name' not in info:
                name_patterns = [
                    r'姓名[\s:：]*([^\s]{2,10})',
                    r'姓[\s]*名[\s:：]*([^\s]{2,10})',
                    # 尝试匹配姓名后跟性别的模式
                    r'([^\s]{2,4})(?=[\s]*性别|[\s]*男|[\s]*女)',
                    # 尝试从第一行提取姓名（通常身份证第一行是姓名）
                    r'^([^\s\d]{2,4})',
                    # 匹配中文姓名模式
                    r'([一-龯]{2,4})(?=\s|$)'
                ]

                for pattern in name_patterns:
                    name_match = re.search(pattern, all_text)
                    if name_match:
                        name = name_match.group(1).strip()
                        # 验证是否为合理的中文姓名
                        if len(name) >= 2 and len(name) <= 4 and not any(c.isdigit() for c in name):
                            info['name'] = name
                            print(f"DEBUG: 用正则找到姓名: {name}")
                            break

            # 性别
            gender_patterns = [
                r'性别[\s:：]*([男女])',
                r'性[\s]*别[\s:：]*([男女])',
                r'([男女])(?=[\s]*民族|[\s]*汉|[\s]*族)'
            ]
            for pattern in gender_patterns:
                gender_match = re.search(pattern, all_text)
                if gender_match:
                    info['gender'] = gender_match.group(1)
                    print(f"DEBUG: 找到性别: {gender_match.group(1)}")
                    break

            # 民族 - 简化匹配
            nation_patterns = [
                r'民族[\s:：]*([^\s]{2,10})',
                r'民[\s]*族[\s:：]*([^\s]{2,10})',
                r'(汉族|回族|蒙古族|藏族|维吾尔族|苗族|彝族|壮族|布依族|朝鲜族|满族|侗族|瑶族|白族|土家族|哈尼族|哈萨克族|傣族|黎族|傈僳族|佤族|畲族|高山族|拉祜族|水族|东乡族|纳西族|景颇族|柯尔克孜族|土族|达斡尔族|仫佬族|羌族|布朗族|撒拉族|毛南族|仡佬族|锡伯族|阿昌族|普米族|塔吉克族|怒族|乌孜别克族|俄罗斯族|鄂温克族|德昂族|保安族|裕固族|京族|塔塔尔族|独龙族|鄂伦春族|赫哲族|门巴族|珞巴族|基诺族)'
            ]
            for pattern in nation_patterns:
                nation_match = re.search(pattern, all_text)
                if nation_match:
                    info['nation'] = nation_match.group(1).strip()
                    print(f"DEBUG: 找到民族: {nation_match.group(1)}")
                    break

            # 出生日期 - 改进匹配逻辑
            birth_patterns = [
                r'出生[\s:：]*([0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日)',
                r'出[\s]*生[\s:：]*([0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日)',
                r'([0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日)',
                r'([0-9]{4}\.[0-9]{1,2}\.[0-9]{1,2})',
                r'([0-9]{4}-[0-9]{1,2}-[0-9]{1,2})'
            ]

            # 先从单独的文本行中找出生日期
            for text in texts:
                text = text.strip()
                for pattern in birth_patterns:
                    birth_match = re.search(pattern, text)
                    if birth_match:
                        info['birth_date'] = birth_match.group(1)
                        print(f"DEBUG: 从单行找到出生日期: {birth_match.group(1)}")
                        break
                if 'birth_date' in info:
                    break

            # 如果没找到，再从全文中找
            if 'birth_date' not in info:
                for pattern in birth_patterns:
                    birth_match = re.search(pattern, all_text)
                    if birth_match:
                        info['birth_date'] = birth_match.group(1)
                        print(f"DEBUG: 从全文找到出生日期: {birth_match.group(1)}")
                        break

            # 住址 - 改进匹配逻辑，排除无关文字
            address_patterns = [
                r'住址[\s:：]*([^\n]{5,50})',
                r'住[\s]*址[\s:：]*([^\n]{5,50})',
                r'地址[\s:：]*([^\n]{5,50})'
            ]

            # 尝试组合多行文本作为地址
            address_lines = []
            exclude_keywords = ['公民', '身份', '号码', '证件', '姓名', '性别', '民族', '出生']

            for text in texts:
                text = text.strip()
                # 如果包含省、市、县、区、乡、村等地名关键词，且不包含排除关键词
                if (any(keyword in text for keyword in ['省', '市', '县', '区', '乡', '镇', '村', '街道', '路', '号']) and
                    not any(exclude in text for exclude in exclude_keywords) and
                    len(text) >= 3 and not any(c.isdigit() for c in text[:3])):  # 排除身份证号等
                    address_lines.append(text)

            if address_lines:
                # 组合地址行，但要清理掉可能混入的无关文字
                combined_address = ''.join(address_lines)
                # 移除可能混入的无关文字
                for exclude in exclude_keywords:
                    combined_address = combined_address.replace(exclude, '')

                # 清理多余的空格和标点
                combined_address = re.sub(r'\s+', '', combined_address)
                combined_address = re.sub(r'[，。、]', '', combined_address)

                if len(combined_address) >= 5:
                    info['address'] = combined_address
                    print(f"DEBUG: 组合地址: {combined_address}")

            # 如果没找到，用正则表达式
            if 'address' not in info:
                for pattern in address_patterns:
                    address_match = re.search(pattern, all_text)
                    if address_match:
                        address = address_match.group(1).strip()
                        # 清理地址中的无关文字
                        for exclude in exclude_keywords:
                            address = address.replace(exclude, '')
                        address = re.sub(r'\s+', '', address)

                        # 简单验证地址长度
                        if len(address) >= 5:
                            info['address'] = address
                            print(f"DEBUG: 正则找到住址: {address}")
                            break

            # 身份证号码 - 18位数字或17位数字+X
            id_patterns = [
                r'([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx])',
                r'([1-9]\d{16}[\dXx])',
                r'([1-9]\d{17})',
                # 更宽松的匹配
                r'([1-9]\d{5}[12]\d{11}[\dXx])'
            ]
            for pattern in id_patterns:
                id_match = re.search(pattern, all_text)
                if id_match:
                    id_number = id_match.group(1)
                    # 验证身份证号码长度
                    if len(id_number) == 18:
                        info['id_number'] = id_number
                        print(f"DEBUG: 找到身份证号: {id_number}")
                        break
        
        if card_type in ['back', 'unknown']:
            # 提取背面信息
            # 签发机关 - 简化匹配
            authority_patterns = [
                r'签发机关[\s:：]*([^\n]{5,30})',
                r'签[\s]*发[\s]*机[\s]*关[\s:：]*([^\n]{5,30})',
                r'(.*公安局.*)',
                r'(.*派出所.*)',
                r'(.*分局.*)'
            ]
            for pattern in authority_patterns:
                authority_match = re.search(pattern, all_text)
                if authority_match:
                    authority = authority_match.group(1).strip()
                    if len(authority) >= 5 and ('公安' in authority or '派出所' in authority or '分局' in authority):
                        info['issuing_authority'] = authority
                        print(f"DEBUG: 找到签发机关: {authority}")
                        break

            # 有效期限 - 支持多种格式
            valid_patterns = [
                r'有效期限[\s:：]*([0-9]{4}\.[0-9]{2}\.[0-9]{2}[-—][0-9]{4}\.[0-9]{2}\.[0-9]{2}|长期)',
                r'有[\s]*效[\s]*期[\s]*限[\s:：]*([0-9]{4}\.[0-9]{2}\.[0-9]{2}[-—][0-9]{4}\.[0-9]{2}\.[0-9]{2}|长期)',
                r'([0-9]{4}\.[0-9]{2}\.[0-9]{2}[-—][0-9]{4}\.[0-9]{2}\.[0-9]{2})',
                r'([0-9]{4}-[0-9]{2}-[0-9]{2}[-—][0-9]{4}-[0-9]{2}-[0-9]{2})',
                r'(长期)'
            ]
            for pattern in valid_patterns:
                valid_match = re.search(pattern, all_text)
                if valid_match:
                    info['valid_period'] = valid_match.group(1)
                    print(f"DEBUG: 找到有效期限: {valid_match.group(1)}")
                    break
        
        return info

    def recognize_idcard(self, image_path, output_format='json'):
        """
        识别身份证

        Args:
            image_path: 图像文件路径
            output_format: 输出格式，'list' 或 'json'

        Returns:
            识别结果
        """
        total_start_time = time.time()

        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图像文件不存在: {image_path}")

            # 预处理图像
            preprocess_start = time.time()
            processed_img = self.preprocess_idcard_image(image_path)
            preprocess_time = time.time() - preprocess_start
            print(f"DEBUG: 图像预处理耗时: {preprocess_time:.3f}秒")

            # 执行OCR识别（完全抑制输出）
            import io
            from contextlib import redirect_stdout, redirect_stderr
            import logging

            # 设置日志级别
            logging.getLogger('ppocr').setLevel(logging.ERROR)
            logging.getLogger('paddle').setLevel(logging.ERROR)

            # 创建空的输出流，抑制OCR过程中的系统日志
            null_stream = io.StringIO()

            try:
                # 完全抑制所有输出
                ocr_start = time.time()
                with redirect_stdout(null_stream), redirect_stderr(null_stream):
                    # 设置环境变量抑制输出
                    os.environ['GLOG_minloglevel'] = '3'
                    os.environ['GLOG_v'] = '0'

                    result = self.ocr.ocr(processed_img)  # 使用正确的ocr方法
                ocr_time = time.time() - ocr_start
                print(f"DEBUG: OCR识别耗时: {ocr_time:.3f}秒")
            except Exception as e:
                # 如果失败，尝试其他方式
                try:
                    with redirect_stdout(null_stream), redirect_stderr(null_stream):
                        result = self.ocr.ocr(processed_img)
                except:
                    result = None

            print(f"DEBUG: OCR识别完成，结果类型: {type(result)}")
            print(f"DEBUG: OCR原始结果: {result}")

            if not result or not result[0]:
                if output_format == 'json':
                    return {
                        "success": False,
                        "message": "未识别到文字内容",
                        "texts": [],
                        "card_type": "unknown"
                    }
                else:
                    return []

            # 提取和清理文字
            texts = []
            confidences = []

            # 新版PaddleOCR返回格式检查
            if isinstance(result, list) and len(result) > 0:
                ocr_result = result[0]
                if 'rec_texts' in ocr_result and 'rec_scores' in ocr_result:
                    # 新格式：包含rec_texts和rec_scores
                    rec_texts = ocr_result['rec_texts']
                    rec_scores = ocr_result['rec_scores']

                    print(f"DEBUG: 识别到的原始文本: {rec_texts}")

                    # 保存原始识别结果供后续使用
                    self._raw_rec_texts = rec_texts

                    for i, text in enumerate(rec_texts):
                        if i < len(rec_scores):
                            confidence = rec_scores[i]
                            # 清理文本
                            cleaned_text = self.clean_idcard_text(text)
                            if cleaned_text and len(cleaned_text) > 1:  # 过滤单字符
                                texts.append(cleaned_text)
                                confidences.append(confidence)
                else:
                    # 旧格式：[[[bbox], [text, confidence]], ...]
                    for line in result[0]:
                        if line and len(line) >= 2:
                            text_info = line[1]
                            if text_info and len(text_info) >= 2:
                                text = text_info[0]
                                confidence = text_info[1]

                                # 清理文本
                                cleaned_text = self.clean_idcard_text(text)
                                if cleaned_text:
                                    texts.append(cleaned_text)
                                    confidences.append(confidence)

            # 检测卡片类型
            detect_start = time.time()
            card_type = self.detect_card_type(texts)
            detect_time = time.time() - detect_start
            print(f"DEBUG: 卡片类型检测耗时: {detect_time:.3f}秒")

            if output_format == 'json':
                # 提取结构化信息
                extract_start = time.time()
                structured_info = self.extract_structured_info(texts, card_type)
                extract_time = time.time() - extract_start
                print(f"DEBUG: 信息提取耗时: {extract_time:.3f}秒")

                total_time = time.time() - total_start_time
                print(f"DEBUG: 总识别耗时: {total_time:.3f}秒")

                return {
                    "success": True,
                    "message": f"成功识别身份证{card_type}面",
                    "texts": texts,
                    "confidences": confidences,
                    "card_type": card_type,
                    "structured_info": structured_info,
                    "total_lines": len(texts),
                    "performance": {
                        "preprocess_time": preprocess_time,
                        "ocr_time": ocr_time,
                        "extract_time": extract_time,
                        "total_time": total_time
                    }
                }
            else:
                return texts

        except Exception as e:
            error_msg = f"身份证OCR识别失败: {e}"
            print(f"ERROR: {error_msg}")

            if output_format == 'json':
                return {
                    "success": False,
                    "message": error_msg,
                    "texts": [],
                    "card_type": "unknown"
                }
            else:
                return []


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='身份证OCR识别工具')
    parser.add_argument('image_path', help='身份证图像文件路径')
    parser.add_argument('--output-format', choices=['list', 'json'], default='json',
                       help='输出格式 (默认: json)')
    parser.add_argument('--card-type', choices=['auto', 'front', 'back'], default='auto',
                       help='身份证类型 (默认: auto)')
    parser.add_argument('--use-gpu', action='store_true', help='使用GPU加速（兼容性参数）')

    args = parser.parse_args()

    # 创建身份证OCR识别器
    ocr_engine = IdCardOCR(use_gpu=args.use_gpu)

    # 执行识别
    result = ocr_engine.recognize_idcard(args.image_path, args.output_format)

    # 输出结果
    if args.output_format == 'json':
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        for text in result:
            print(text)


if __name__ == '__main__':
    main()
