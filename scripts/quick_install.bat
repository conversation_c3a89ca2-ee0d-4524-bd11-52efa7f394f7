@echo off
chcp 65001 >nul
echo ================================================
echo PaddleOCR 快速安装脚本
echo ================================================

echo 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Python
    echo 请先安装 Python 3.7+ 
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ Python 环境可用

echo.
echo 开始安装 PaddleOCR...
echo 使用清华大学镜像源，速度更快
echo.

echo 正在安装 PaddleOCR...
python -m pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple --timeout 60
if errorlevel 1 (
    echo 尝试使用默认源...
    python -m pip install paddleocr --timeout 60
    if errorlevel 1 (
        echo ❌ PaddleOCR 安装失败
        echo 请检查网络连接或手动安装
        pause
        exit /b 1
    )
)

echo.
echo 测试 PaddleOCR...
python -c "import paddleocr; print('✓ PaddleOCR 安装成功')"
if errorlevel 1 (
    echo ❌ PaddleOCR 测试失败
    pause
    exit /b 1
)

echo.
echo ================================================
echo 🎉 PaddleOCR 安装完成！
echo ================================================
echo.
echo 现在可以：
echo 1. 重启 Java 应用
echo 2. 使用 PaddleOCR 功能进行文字识别
echo 3. 运行 python scripts/test_paddleocr.py 进行完整测试
echo.
pause
