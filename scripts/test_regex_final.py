#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正则表达式是否正确
"""

import re

# 测试文本
test_text = "姓名张三 性别男 民族汉族 出生1990年01月01日 住址北京市朝阳区 身份证号码110101199001011234"

print("测试正则表达式...")
print(f"测试文本: {test_text}")
print()

# 正确的正则表达式（修复Unicode字符范围）
correct_patterns = {
    'name': r'姓名[\s:：]*([\\u4e00-\\u9fa5·]{2,10})',
    'gender': r'性别[\s:：]*([男女])',
    'nation': r'民族[\s:：]*([\\u4e00-\\u9fa5]{2,10})',
    'birth': r'出生[\s:：]*([0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日)',
    'address': r'住址[\s:：]*([\\u4e00-\\u9fa5\\d\\s]+)',
    'id_number': r'([1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx])'
}

for name, pattern in correct_patterns.items():
    try:
        match = re.search(pattern, test_text)
        if match:
            result = match.group(1) if match.lastindex and match.lastindex >= 1 else match.group(0)
            print(f"✓ {name}: {result}")
        else:
            print(f"✗ {name}: 未匹配")
    except Exception as e:
        print(f"✗ {name}: 错误 - {e}")

print("\n使用正确的Unicode范围:")

# 使用正确的Unicode范围
final_patterns = {
    'name': r'姓名[\s:：]*([\\u4e00-\\u9fa5·]{2,10})',
    'gender': r'性别[\s:：]*([男女])',
    'nation': r'民族[\s:：]*([\\u4e00-\\u9fa5]{2,10})',
    'birth': r'出生[\s:：]*([0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日)',
    'address': r'住址[\s:：]*([\\u4e00-\\u9fa5\\d\\s]+)',
    'id_number': r'([1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx])'
}

for name, pattern in final_patterns.items():
    try:
        match = re.search(pattern, test_text)
        if match:
            result = match.group(1) if match.lastindex and match.lastindex >= 1 else match.group(0)
            print(f"✓ {name}: {result}")
        else:
            print(f"✗ {name}: 未匹配")
    except Exception as e:
        print(f"✗ {name}: 错误 - {e}")
