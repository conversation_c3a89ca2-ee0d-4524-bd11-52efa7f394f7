@echo off
chcp 65001 >nul
echo ================================================
echo PaddleOCR 环境安装脚本 (Windows)
echo ================================================

echo 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 Python，请先安装 Python 3.7 或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python 环境检查通过

echo.
echo 开始安装 PaddleOCR 依赖...
echo.

echo 升级 pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo 警告: pip 升级失败，继续安装其他包
)

echo.
echo 安装 PaddlePaddle...
python -m pip install paddlepaddle -i https://pypi.tuna.tsinghua.edu.cn/simple
if errorlevel 1 (
    echo 尝试使用默认源安装 PaddlePaddle...
    python -m pip install paddlepaddle
    if errorlevel 1 (
        echo 错误: PaddlePaddle 安装失败
        pause
        exit /b 1
    )
)

echo.
echo 安装 PaddleOCR...
python -m pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple
if errorlevel 1 (
    echo 尝试使用默认源安装 PaddleOCR...
    python -m pip install paddleocr
    if errorlevel 1 (
        echo 错误: PaddleOCR 安装失败
        pause
        exit /b 1
    )
)

echo.
echo 安装 OpenCV...
python -m pip install opencv-python -i https://pypi.tuna.tsinghua.edu.cn/simple
if errorlevel 1 (
    echo 尝试使用默认源安装 OpenCV...
    python -m pip install opencv-python
    if errorlevel 1 (
        echo 错误: OpenCV 安装失败
        pause
        exit /b 1
    )
)

echo.
echo 安装 Pillow...
python -m pip install pillow -i https://pypi.tuna.tsinghua.edu.cn/simple
if errorlevel 1 (
    echo 尝试使用默认源安装 Pillow...
    python -m pip install pillow
    if errorlevel 1 (
        echo 错误: Pillow 安装失败
        pause
        exit /b 1
    )
)

echo.
echo 安装 NumPy...
python -m pip install numpy -i https://pypi.tuna.tsinghua.edu.cn/simple
if errorlevel 1 (
    echo 尝试使用默认源安装 NumPy...
    python -m pip install numpy
    if errorlevel 1 (
        echo 错误: NumPy 安装失败
        pause
        exit /b 1
    )
)

echo.
echo 测试 PaddleOCR 安装...
python -c "from paddleocr import PaddleOCR; print('✓ PaddleOCR 安装成功')"
if errorlevel 1 (
    echo 错误: PaddleOCR 测试失败
    pause
    exit /b 1
)

echo.
echo ================================================
echo ✓ PaddleOCR 环境安装完成！
echo ================================================
echo.
echo 使用说明:
echo 1. 运行 'python scripts/test_paddleocr.py' 测试环境
echo 2. 运行 'python scripts/paddle_ocr.py 图片路径' 进行OCR识别
echo 3. 启动Java应用使用OCR功能
echo.
pause
