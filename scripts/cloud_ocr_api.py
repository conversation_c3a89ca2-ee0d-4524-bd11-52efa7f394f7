#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端OCR API集成方案 - 极速身份证识别
支持百度OCR、腾讯OCR、阿里云OCR等多种云端服务
"""

import sys
import os
import json
import base64
import requests
import time
from typing import Dict, Any, Optional

class CloudOCRService:
    """云端OCR服务集成"""
    
    def __init__(self, provider='baidu', **config):
        """
        初始化云端OCR服务
        
        Args:
            provider: 服务提供商 ('baidu', 'tencent', 'aliyun')
            **config: 配置参数（API密钥等）
        """
        self.provider = provider
        self.config = config
        self.session = requests.Session()
        self.session.timeout = 5  # 5秒超时
        
    def encode_image(self, image_path: str) -> str:
        """将图片编码为base64"""
        with open(image_path, 'rb') as f:
            return base64.b64encode(f.read()).decode('utf-8')
    
    def baidu_ocr(self, image_path: str) -> Dict[str, Any]:
        """百度OCR身份证识别"""
        try:
            # 获取access_token
            token_url = "https://aip.baidubce.com/oauth/2.0/token"
            token_params = {
                'grant_type': 'client_credentials',
                'client_id': self.config.get('api_key'),
                'client_secret': self.config.get('secret_key')
            }
            
            token_response = self.session.get(token_url, params=token_params)
            access_token = token_response.json().get('access_token')
            
            if not access_token:
                raise Exception("获取百度OCR access_token失败")
            
            # 身份证识别
            ocr_url = f"https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token={access_token}"
            
            image_data = self.encode_image(image_path)
            
            data = {
                'image': image_data,
                'id_card_side': 'front',  # front或back
                'detect_direction': 'false',  # 不检测方向，提高速度
                'detect_risk': 'false'  # 不检测风险，提高速度
            }
            
            response = self.session.post(ocr_url, data=data)
            result = response.json()
            
            if 'words_result' in result:
                return self.parse_baidu_result(result)
            else:
                raise Exception(f"百度OCR识别失败: {result}")
                
        except Exception as e:
            print(f"ERROR: 百度OCR识别失败: {e}")
            return {"success": False, "message": str(e)}
    
    def parse_baidu_result(self, result: Dict) -> Dict[str, Any]:
        """解析百度OCR结果"""
        words_result = result.get('words_result', {})
        
        structured_info = {
            'name': words_result.get('姓名', {}).get('words', ''),
            'gender': words_result.get('性别', {}).get('words', ''),
            'nation': words_result.get('民族', {}).get('words', ''),
            'birth_date': words_result.get('出生', {}).get('words', ''),
            'address': words_result.get('住址', {}).get('words', ''),
            'id_number': words_result.get('公民身份号码', {}).get('words', ''),
            'issuing_authority': words_result.get('签发机关', {}).get('words', ''),
            'valid_period': words_result.get('失效日期', {}).get('words', '')
        }
        
        # 清理空值
        structured_info = {k: v for k, v in structured_info.items() if v}
        
        return {
            "success": True,
            "message": "云端OCR识别成功",
            "provider": "baidu",
            "structured_info": structured_info,
            "raw_result": result
        }
    
    def tencent_ocr(self, image_path: str) -> Dict[str, Any]:
        """腾讯OCR身份证识别"""
        # TODO: 实现腾讯OCR API调用
        return {"success": False, "message": "腾讯OCR暂未实现"}
    
    def aliyun_ocr(self, image_path: str) -> Dict[str, Any]:
        """阿里云OCR身份证识别"""
        # TODO: 实现阿里云OCR API调用
        return {"success": False, "message": "阿里云OCR暂未实现"}
    
    def recognize_idcard(self, image_path: str) -> Dict[str, Any]:
        """
        识别身份证
        
        Args:
            image_path: 图片路径
            
        Returns:
            识别结果
        """
        start_time = time.time()
        
        try:
            if self.provider == 'baidu':
                result = self.baidu_ocr(image_path)
            elif self.provider == 'tencent':
                result = self.tencent_ocr(image_path)
            elif self.provider == 'aliyun':
                result = self.aliyun_ocr(image_path)
            else:
                raise Exception(f"不支持的OCR服务提供商: {self.provider}")
            
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            
            print(f"云端OCR识别完成，耗时: {processing_time:.3f}秒")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                "success": False,
                "message": f"云端OCR识别失败: {e}",
                "provider": self.provider,
                "processing_time": processing_time
            }

def main():
    """测试云端OCR服务"""
    import argparse
    
    parser = argparse.ArgumentParser(description='云端OCR身份证识别')
    parser.add_argument('image_path', help='身份证图片路径')
    parser.add_argument('--provider', choices=['baidu', 'tencent', 'aliyun'], 
                       default='baidu', help='OCR服务提供商')
    parser.add_argument('--api-key', help='API密钥')
    parser.add_argument('--secret-key', help='密钥')
    
    args = parser.parse_args()
    
    # 配置云端OCR服务
    config = {}
    if args.api_key:
        config['api_key'] = args.api_key
    if args.secret_key:
        config['secret_key'] = args.secret_key
    
    # 创建云端OCR服务实例
    cloud_ocr = CloudOCRService(provider=args.provider, **config)
    
    # 执行识别
    result = cloud_ocr.recognize_idcard(args.image_path)
    
    # 输出结果
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
