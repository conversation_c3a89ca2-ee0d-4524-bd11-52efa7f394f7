#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合OCR策略 - 智能选择最快的识别方案
结合本地PaddleOCR和云端API，实现3秒内识别目标
"""

import sys
import os
import json
import time
import threading
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, Any, Optional, List

# 导入本地OCR
try:
    from idcard_ocr import IdCardOCR
    LOCAL_OCR_AVAILABLE = True
except ImportError:
    LOCAL_OCR_AVAILABLE = False

# 导入云端OCR
try:
    from cloud_ocr_api import CloudOCRService
    CLOUD_OCR_AVAILABLE = True
except ImportError:
    CLOUD_OCR_AVAILABLE = False

class HybridOCRService:
    """混合OCR服务 - 智能选择最优方案"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化混合OCR服务
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.local_ocr = None
        self.cloud_services = []
        
        # 初始化本地OCR
        if LOCAL_OCR_AVAILABLE and self.config.get('enable_local', True):
            try:
                self.local_ocr = IdCardOCR()
                print("本地OCR初始化成功")
            except Exception as e:
                print(f"本地OCR初始化失败: {e}")
        
        # 初始化云端OCR服务
        if CLOUD_OCR_AVAILABLE and self.config.get('enable_cloud', True):
            cloud_configs = self.config.get('cloud_configs', [])
            for cloud_config in cloud_configs:
                try:
                    service = CloudOCRService(**cloud_config)
                    self.cloud_services.append(service)
                    print(f"云端OCR服务初始化成功: {cloud_config.get('provider')}")
                except Exception as e:
                    print(f"云端OCR服务初始化失败: {e}")
    
    def preprocess_for_speed(self, image_path: str) -> str:
        """
        为速度优化的图像预处理
        
        Args:
            image_path: 原始图片路径
            
        Returns:
            处理后的图片路径
        """
        try:
            from PIL import Image
            import cv2
            import numpy as np
            
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                return image_path
            
            # 快速缩放到合适尺寸（身份证识别的最优尺寸）
            height, width = img.shape[:2]
            target_width = 800
            target_height = 500
            
            if width > target_width or height > target_height:
                scale = min(target_width/width, target_height/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
            
            # 保存优化后的图像
            optimized_path = image_path.replace('.', '_optimized.')
            cv2.imwrite(optimized_path, img, [cv2.IMWRITE_JPEG_QUALITY, 85])
            
            return optimized_path
            
        except Exception as e:
            print(f"图像预处理失败，使用原图: {e}")
            return image_path
    
    def local_recognize(self, image_path: str) -> Dict[str, Any]:
        """本地OCR识别"""
        if not self.local_ocr:
            return {"success": False, "message": "本地OCR不可用", "source": "local"}
        
        try:
            start_time = time.time()
            result = self.local_ocr.recognize_idcard(image_path, output_format='json')
            processing_time = time.time() - start_time
            
            result['source'] = 'local'
            result['processing_time'] = processing_time
            return result
            
        except Exception as e:
            return {"success": False, "message": str(e), "source": "local"}
    
    def cloud_recognize(self, image_path: str, service: CloudOCRService) -> Dict[str, Any]:
        """云端OCR识别"""
        try:
            result = service.recognize_idcard(image_path)
            result['source'] = f'cloud_{service.provider}'
            return result
        except Exception as e:
            return {
                "success": False, 
                "message": str(e), 
                "source": f'cloud_{service.provider}'
            }
    
    def recognize_with_timeout(self, image_path: str, timeout: float = 3.0) -> Dict[str, Any]:
        """
        带超时的并行识别
        
        Args:
            image_path: 图片路径
            timeout: 超时时间（秒）
            
        Returns:
            识别结果
        """
        start_time = time.time()
        
        # 预处理图像以提高速度
        optimized_image = self.preprocess_for_speed(image_path)
        
        # 准备所有识别任务
        tasks = []
        
        # 添加本地OCR任务
        if self.local_ocr:
            tasks.append(('local', self.local_recognize, optimized_image))
        
        # 添加云端OCR任务
        for service in self.cloud_services:
            tasks.append((f'cloud_{service.provider}', self.cloud_recognize, optimized_image, service))
        
        if not tasks:
            return {"success": False, "message": "没有可用的OCR服务"}
        
        # 并行执行所有任务
        results = []
        with ThreadPoolExecutor(max_workers=len(tasks)) as executor:
            # 提交所有任务
            future_to_task = {}
            for task in tasks:
                if len(task) == 3:  # 本地OCR
                    future = executor.submit(task[1], task[2])
                else:  # 云端OCR
                    future = executor.submit(task[1], task[2], task[3])
                future_to_task[future] = task[0]
            
            # 等待第一个成功的结果或超时
            for future in as_completed(future_to_task, timeout=timeout):
                try:
                    result = future.result()
                    if result.get('success'):
                        # 找到第一个成功的结果，立即返回
                        processing_time = time.time() - start_time
                        result['total_processing_time'] = processing_time
                        
                        # 清理临时文件
                        if optimized_image != image_path:
                            try:
                                os.remove(optimized_image)
                            except:
                                pass
                        
                        print(f"混合OCR识别成功，来源: {result.get('source')}, 耗时: {processing_time:.3f}秒")
                        return result
                    else:
                        results.append(result)
                except Exception as e:
                    results.append({"success": False, "message": str(e)})
        
        # 如果没有成功的结果，返回最后一个尝试的结果
        processing_time = time.time() - start_time
        
        # 清理临时文件
        if optimized_image != image_path:
            try:
                os.remove(optimized_image)
            except:
                pass
        
        if results:
            final_result = results[-1]
            final_result['total_processing_time'] = processing_time
            final_result['all_attempts'] = results
            return final_result
        else:
            return {
                "success": False,
                "message": f"所有OCR服务在{timeout}秒内均未完成",
                "total_processing_time": processing_time
            }
    
    def recognize_idcard_fast(self, image_path: str) -> Dict[str, Any]:
        """
        快速身份证识别 - 3秒内完成
        
        Args:
            image_path: 图片路径
            
        Returns:
            识别结果
        """
        return self.recognize_with_timeout(image_path, timeout=3.0)

def main():
    """测试混合OCR服务"""
    import argparse
    
    parser = argparse.ArgumentParser(description='混合OCR身份证识别')
    parser.add_argument('image_path', help='身份证图片路径')
    parser.add_argument('--timeout', type=float, default=3.0, help='超时时间（秒）')
    parser.add_argument('--config', help='配置文件路径')
    
    args = parser.parse_args()
    
    # 加载配置
    config = {}
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
    
    # 创建混合OCR服务
    hybrid_ocr = HybridOCRService(config)
    
    # 执行快速识别
    result = hybrid_ocr.recognize_with_timeout(args.image_path, args.timeout)
    
    # 输出结果
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
