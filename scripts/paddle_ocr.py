#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR 文字识别脚本
优化版本，提高中文识别准确性，减少乱码问题
"""

import sys
import os
import json
import argparse
from pathlib import Path

try:
    from paddleocr import PaddleOCR
    import cv2
    import numpy as np
    from PIL import Image, ImageEnhance
except ImportError as e:
    print(f"ERROR: 缺少必要的依赖包: {e}")
    print("请安装: pip install paddleocr opencv-python pillow")
    sys.exit(1)


class OptimizedPaddleOCR:
    """优化的 PaddleOCR 识别器"""
    
    def __init__(self, use_gpu=False, lang='ch'):
        """
        初始化 OCR 识别器
        
        Args:
            use_gpu: 是否使用 GPU
            lang: 语言设置，'ch' 为中文，'en' 为英文
        """
        try:
            self.ocr = PaddleOCR(
                use_textline_orientation=True,  # 启用文字方向分类（替代use_angle_cls）
                lang=lang,
                use_gpu=use_gpu
                # 移除过时参数，使用默认配置以确保兼容性
            )
            print("PaddleOCR 初始化成功")
        except Exception as e:
            print(f"ERROR: PaddleOCR 初始化失败: {e}")
            sys.exit(1)
    
    def preprocess_image(self, image_path):
        """
        图像预处理，提高识别准确性
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            处理后的图像数组
        """
        try:
            # 使用 PIL 读取图像
            pil_image = Image.open(image_path)
            
            # 转换为 RGB 模式
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # 图像增强
            # 1. 对比度增强
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.2)
            
            # 2. 锐度增强
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.1)
            
            # 转换为 numpy 数组
            img_array = np.array(pil_image)
            
            # 使用 OpenCV 进一步处理
            # 转换为灰度图进行处理
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # 自适应阈值处理，提高文字清晰度
            adaptive_thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 转换回 RGB
            processed_img = cv2.cvtColor(adaptive_thresh, cv2.COLOR_GRAY2RGB)
            
            return processed_img
            
        except Exception as e:
            print(f"WARNING: 图像预处理失败，使用原图: {e}")
            # 如果预处理失败，直接返回原图
            return cv2.imread(image_path)
    
    def clean_text(self, text):
        """
        清理识别结果中的乱码和无效字符
        
        Args:
            text: 原始识别文本
            
        Returns:
            清理后的文本
        """
        if not text or not text.strip():
            return ""
        
        # 移除常见的 OCR 乱码字符
        import re
        
        # 移除特殊符号和控制字符
        text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s\.,;:!?()（）【】""''、。，；：！？]', '', text)
        
        # 移除过短的无意义文本
        if len(text.strip()) < 2:
            return ""
        
        # 移除纯数字或纯符号的行
        if re.match(r'^[\d\s\.,;:!?()（）【】""''、。，；：！？]+$', text.strip()):
            if len(text.strip()) < 3:  # 短的数字串可能是乱码
                return ""
        
        return text.strip()
    
    def recognize_text(self, image_path, output_format='list'):
        """
        识别图像中的文字
        
        Args:
            image_path: 图像文件路径
            output_format: 输出格式，'list' 或 'json'
            
        Returns:
            识别结果
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图像文件不存在: {image_path}")
            
            # 预处理图像
            processed_img = self.preprocess_image(image_path)
            
            # 执行 OCR 识别
            result = self.ocr.ocr(processed_img)  # 使用正确的ocr方法

            if not result or not result[0]:
                return [] if output_format == 'list' else {"texts": [], "message": "未识别到文字内容"}

            # 提取文字内容
            texts = []
            confidences = []

            # PaddleOCR返回格式: [[[bbox], [text, confidence]], ...]
            for line in result[0]:
                if line and len(line) >= 2:
                    text_info = line[1]
                    if text_info and len(text_info) >= 2:
                        text = text_info[0]
                        confidence = text_info[1]

                        # 清理文本
                        cleaned_text = self.clean_text(text)
                        if cleaned_text:
                            texts.append(cleaned_text)
                            confidences.append(confidence)
            
            if output_format == 'json':
                return {
                    "success": True,
                    "texts": texts,
                    "confidences": confidences,
                    "total_lines": len(texts),
                    "message": f"成功识别 {len(texts)} 行文字"
                }
            else:
                return texts
                
        except Exception as e:
            error_msg = f"OCR 识别失败: {e}"
            print(f"ERROR: {error_msg}")
            
            if output_format == 'json':
                return {
                    "success": False,
                    "texts": [],
                    "error": error_msg,
                    "message": error_msg
                }
            else:
                return []


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaddleOCR 文字识别工具')
    parser.add_argument('image_path', help='图像文件路径')
    parser.add_argument('--output-format', choices=['list', 'json'], default='list', 
                       help='输出格式 (默认: list)')
    parser.add_argument('--use-gpu', action='store_true', help='使用 GPU 加速')
    parser.add_argument('--lang', default='ch', choices=['ch', 'en'], 
                       help='识别语言 (默认: ch)')
    
    args = parser.parse_args()
    
    # 创建 OCR 识别器
    ocr_engine = OptimizedPaddleOCR(use_gpu=args.use_gpu, lang=args.lang)
    
    # 执行识别
    result = ocr_engine.recognize_text(args.image_path, args.output_format)
    
    # 输出结果
    if args.output_format == 'json':
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        for text in result:
            print(text)


if __name__ == '__main__':
    # 如果直接作为脚本运行
    if len(sys.argv) > 1:
        main()
    else:
        # 兼容 Java 调用方式
        if len(sys.argv) == 1 and hasattr(sys, 'argv'):
            # 简单的命令行调用方式
            print("请提供图像文件路径作为参数")
            sys.exit(1)
