#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证专用OCR识别脚本（简化版本）
使用Tesseract作为主要引擎，PaddleOCR作为备用
"""

import sys
import os
import json
import argparse
import re
import warnings
from pathlib import Path

# 设置编码和抑制警告
os.environ['PYTHONIOENCODING'] = 'utf-8'
warnings.filterwarnings('ignore')

# 重定向标准输出，确保UTF-8编码
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

try:
    import cv2
    import numpy as np
    from PIL import Image, ImageEnhance, ImageFilter
    import pytesseract
except ImportError as e:
    print(f"ERROR: 缺少必要的依赖包: {e}")
    print("请安装: pip install opencv-python pillow pytesseract")
    sys.exit(1)


class IdCardOCRSimple:
    """身份证专用OCR识别器（简化版）"""
    
    def __init__(self, tesseract_path=None):
        """
        初始化身份证OCR识别器
        
        Args:
            tesseract_path: Tesseract可执行文件路径
        """
        try:
            print("正在初始化身份证OCR（简化版）...")
            
            # 设置Tesseract路径
            if tesseract_path:
                pytesseract.pytesseract.tesseract_cmd = tesseract_path
            
            # 测试Tesseract是否可用
            try:
                pytesseract.get_tesseract_version()
                print("Tesseract OCR可用")
            except Exception as e:
                print(f"WARNING: Tesseract不可用: {e}")
                
            print("身份证OCR初始化成功")
        except Exception as e:
            print(f"ERROR: 身份证OCR初始化失败: {e}")
            sys.exit(1)
    
    def preprocess_idcard_image(self, image_path):
        """
        身份证图像预处理
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            处理后的图像数组
        """
        try:
            # 使用PIL读取图像
            pil_image = Image.open(image_path)
            
            # 转换为RGB模式
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # 身份证图像增强
            # 1. 锐度增强
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(2.0)
            
            # 2. 对比度增强
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.5)
            
            # 3. 亮度调整
            enhancer = ImageEnhance.Brightness(pil_image)
            pil_image = enhancer.enhance(1.2)
            
            # 转换为numpy数组
            img_array = np.array(pil_image)
            
            # 使用OpenCV进一步处理
            # 转换为灰度图
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # 高斯模糊去噪
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # 自适应阈值处理
            adaptive_thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 形态学操作，去除噪点
            kernel = np.ones((3, 3), np.uint8)
            cleaned = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)
            
            return cleaned
            
        except Exception as e:
            print(f"WARNING: 图像预处理失败，使用原图: {e}")
            return cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    
    def clean_idcard_text(self, text):
        """
        清理身份证识别文本

        Args:
            text: 原始识别文本

        Returns:
            清理后的文本
        """
        if not text or not text.strip():
            return ""

        # 移除换行符和多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 身份证专用文本清理
        # 保留中文、数字、常用标点和身份证特有字符
        text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s\.,;:!?()（）【】""''、。，；：！？年月日Xx]', '', text)

        # 移除过短的无意义文本
        if len(text.strip()) < 2:
            return ""

        # 特殊处理身份证常见误识别
        text = text.replace('O', '0')  # O替换为0
        text = text.replace('l', '1')  # l替换为1
        text = text.replace('I', '1')  # I替换为1

        return text.strip()
    
    def detect_card_type(self, texts):
        """
        检测身份证类型
        
        Args:
            texts: 识别文本列表
            
        Returns:
            卡片类型: 'front', 'back', 'unknown'
        """
        all_text = ' '.join(texts)
        
        # 正面特征
        front_keywords = ['姓名', '性别', '民族', '出生', '住址']
        front_score = sum(1 for keyword in front_keywords if keyword in all_text)
        
        # 背面特征
        back_keywords = ['签发机关', '有效期限', '公安局', '派出所']
        back_score = sum(1 for keyword in back_keywords if keyword in all_text)
        
        if front_score > back_score:
            return 'front'
        elif back_score > front_score:
            return 'back'
        else:
            return 'unknown'
    
    def extract_structured_info(self, texts, card_type):
        """
        提取结构化身份证信息
        
        Args:
            texts: 识别文本列表
            card_type: 卡片类型
            
        Returns:
            结构化信息字典
        """
        all_text = ' '.join(texts)
        info = {'card_type': card_type}
        
        if card_type in ['front', 'unknown']:
            # 提取正面信息
            # 姓名
            name_match = re.search(r'姓名[\s:：]*([\\u4e00-\\u9fa5·]{2,10})', all_text)
            if name_match:
                info['name'] = name_match.group(1)
            
            # 性别
            gender_match = re.search(r'性别[\s:：]*([男女])', all_text)
            if gender_match:
                info['gender'] = gender_match.group(1)
            
            # 民族
            nation_match = re.search(r'民族[\s:：]*([\\u4e00-\\u9fa5]{2,10})', all_text)
            if nation_match:
                info['nation'] = nation_match.group(1)
            
            # 出生日期
            birth_match = re.search(r'出生[\s:：]*([0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日)', all_text)
            if birth_match:
                info['birth_date'] = birth_match.group(1)
            
            # 住址
            address_match = re.search(r'住址[\s:：]*([\\u4e00-\\u9fa5\\d\\s]+)', all_text)
            if address_match:
                info['address'] = address_match.group(1)
            
            # 身份证号码
            id_match = re.search(r'([1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx])', all_text)
            if id_match:
                info['id_number'] = id_match.group(1)
        
        if card_type in ['back', 'unknown']:
            # 提取背面信息
            # 签发机关
            authority_match = re.search(r'签发机关[\s:：]*([\\u4e00-\\u9fa5\\s]+)', all_text)
            if authority_match:
                info['issuing_authority'] = authority_match.group(1).strip()
            
            # 有效期限
            valid_match = re.search(r'有效期限[\s:：]*([0-9]{4}\\.[0-9]{2}\\.[0-9]{2}[-—][0-9]{4}\\.[0-9]{2}\\.[0-9]{2}|长期)', all_text)
            if valid_match:
                info['valid_period'] = valid_match.group(1)
        
        return info
    
    def recognize_idcard(self, image_path, output_format='json'):
        """
        识别身份证
        
        Args:
            image_path: 图像文件路径
            output_format: 输出格式，'list' 或 'json'
            
        Returns:
            识别结果
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图像文件不存在: {image_path}")
            
            # 预处理图像
            processed_img = self.preprocess_idcard_image(image_path)
            
            # 使用Tesseract进行OCR识别
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\\u4e00-\\u9fff'
            
            try:
                # 尝试使用中文识别
                text_result = pytesseract.image_to_string(processed_img, lang='chi_sim', config=custom_config)
            except Exception as e:
                # 如果失败，使用基本配置
                text_result = pytesseract.image_to_string(processed_img, lang='chi_sim')
            
            if not text_result or not text_result.strip():
                if output_format == 'json':
                    return {
                        "success": False,
                        "message": "未识别到文字内容",
                        "texts": [],
                        "card_type": "unknown"
                    }
                else:
                    return []
            
            # 分割文本行并清理
            lines = text_result.split('\n')
            texts = []
            confidences = []
            
            for line in lines:
                cleaned_text = self.clean_idcard_text(line)
                if cleaned_text:
                    texts.append(cleaned_text)
                    confidences.append(0.8)  # 默认置信度
            
            # 检测卡片类型
            card_type = self.detect_card_type(texts)
            
            if output_format == 'json':
                # 提取结构化信息
                structured_info = self.extract_structured_info(texts, card_type)
                
                return {
                    "success": True,
                    "message": f"成功识别身份证{card_type}面",
                    "texts": texts,
                    "confidences": confidences,
                    "card_type": card_type,
                    "structured_info": structured_info,
                    "total_lines": len(texts)
                }
            else:
                return texts
                
        except Exception as e:
            error_msg = f"身份证OCR识别失败: {e}"
            print(f"ERROR: {error_msg}")
            
            if output_format == 'json':
                return {
                    "success": False,
                    "message": error_msg,
                    "texts": [],
                    "card_type": "unknown"
                }
            else:
                return []


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='身份证OCR识别工具（简化版）')
    parser.add_argument('image_path', help='身份证图像文件路径')
    parser.add_argument('--output-format', choices=['list', 'json'], default='json',
                       help='输出格式 (默认: json)')
    parser.add_argument('--tesseract-path', help='Tesseract可执行文件路径')
    
    args = parser.parse_args()
    
    # 创建身份证OCR识别器
    ocr_engine = IdCardOCRSimple(tesseract_path=args.tesseract_path)
    
    # 执行识别
    result = ocr_engine.recognize_idcard(args.image_path, args.output_format)
    
    # 输出结果
    if args.output_format == 'json':
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        for text in result:
            print(text)


if __name__ == '__main__':
    main()
