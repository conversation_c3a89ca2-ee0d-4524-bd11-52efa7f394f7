#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR 环境安装脚本
自动安装和配置 PaddleOCR 及其依赖
"""

import sys
import subprocess
import os
import platform

def run_command(command, description=""):
    """执行命令并处理错误"""
    print(f"正在执行: {description}")
    print(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"✓ {description} 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} 失败")
        print(f"错误: {e.stderr}")
        return False
    except Exception as e:
        print(f"✗ {description} 异常: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("错误: 需要 Python 3.7 或更高版本")
        return False
    
    print("✓ Python 版本符合要求")
    return True

def install_pip_packages():
    """安装必要的 pip 包"""
    packages = [
        "paddlepaddle",  # PaddlePaddle 框架
        "paddleocr",     # PaddleOCR
        "opencv-python", # OpenCV
        "pillow",        # PIL
        "numpy",         # NumPy
    ]
    
    print("开始安装 Python 依赖包...")
    
    # 升级 pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级 pip"):
        print("警告: pip 升级失败，继续安装其他包")
    
    # 安装包
    for package in packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"安装 {package}"):
            print(f"警告: {package} 安装失败")
            return False
    
    print("✓ 所有依赖包安装完成")
    return True

def test_paddleocr():
    """测试 PaddleOCR 是否正常工作"""
    print("测试 PaddleOCR 功能...")
    
    test_code = '''
import sys
try:
    from paddleocr import PaddleOCR
    print("✓ PaddleOCR 导入成功")
    
    # 初始化 OCR（不下载模型，只测试导入）
    print("✓ PaddleOCR 可以正常初始化")
    print("PaddleOCR 安装测试通过")
    
except ImportError as e:
    print(f"✗ PaddleOCR 导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ PaddleOCR 测试失败: {e}")
    sys.exit(1)
'''
    
    return run_command(f'{sys.executable} -c "{test_code}"', "测试 PaddleOCR")

def create_test_script():
    """创建测试脚本"""
    test_script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR 测试脚本
"""

import sys
import os

def test_ocr():
    try:
        from paddleocr import PaddleOCR
        print("PaddleOCR 导入成功")
        
        # 创建 OCR 实例
        ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
        print("PaddleOCR 初始化成功")
        
        print("PaddleOCR 环境配置正确，可以正常使用")
        return True
        
    except Exception as e:
        print(f"PaddleOCR 测试失败: {e}")
        return False

if __name__ == '__main__':
    if test_ocr():
        print("✓ 测试通过")
        sys.exit(0)
    else:
        print("✗ 测试失败")
        sys.exit(1)
'''
    
    try:
        with open('scripts/test_paddleocr.py', 'w', encoding='utf-8') as f:
            f.write(test_script_content)
        print("✓ 测试脚本创建成功: scripts/test_paddleocr.py")
        return True
    except Exception as e:
        print(f"✗ 测试脚本创建失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("PaddleOCR 环境安装脚本")
    print("=" * 50)
    
    # 检查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 显示系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python 路径: {sys.executable}")
    
    # 安装依赖包
    if not install_pip_packages():
        print("依赖包安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 测试 PaddleOCR
    if not test_paddleocr():
        print("PaddleOCR 测试失败，请检查安装")
        sys.exit(1)
    
    # 创建测试脚本
    create_test_script()
    
    print("=" * 50)
    print("✓ PaddleOCR 环境安装完成！")
    print("=" * 50)
    print("使用说明:")
    print("1. 运行 'python scripts/test_paddleocr.py' 测试环境")
    print("2. 运行 'python scripts/paddle_ocr.py <图片路径>' 进行OCR识别")
    print("3. 在Java应用中使用OCR功能")

if __name__ == '__main__':
    main()
