#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证OCR测试脚本
用于验证身份证识别功能是否正常工作
"""

import sys
import os
import time
import json

def test_paddleocr_import():
    """测试PaddleOCR导入"""
    print("=" * 50)
    print("测试PaddleOCR导入...")
    print("=" * 50)
    
    try:
        from paddleocr import PaddleOCR
        print("✓ PaddleOCR导入成功")
        return True
    except ImportError as e:
        print(f"✗ PaddleOCR导入失败: {e}")
        print("请安装PaddleOCR: pip install paddleocr")
        return False

def test_idcard_ocr_init():
    """测试身份证OCR初始化"""
    print("\n" + "=" * 50)
    print("测试身份证OCR初始化...")
    print("=" * 50)
    
    try:
        from paddleocr import PaddleOCR
        
        print("正在初始化身份证OCR（首次运行可能需要下载模型）...")
        start_time = time.time()

        # 使用基本参数确保兼容性
        ocr = PaddleOCR(lang='ch')
        
        end_time = time.time()
        print(f"✓ 身份证OCR初始化成功 (耗时: {end_time - start_time:.2f}秒)")
        return ocr
        
    except Exception as e:
        print(f"✗ 身份证OCR初始化失败: {e}")
        return None

def create_test_idcard_image():
    """创建测试身份证图片"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建一个模拟身份证的测试图片
        img = Image.new('RGB', (600, 380), color='lightblue')
        draw = ImageDraw.Draw(img)
        
        # 尝试使用系统字体
        try:
            font_large = ImageFont.truetype("arial.ttf", 24)
            font_medium = ImageFont.truetype("arial.ttf", 18)
            font_small = ImageFont.truetype("arial.ttf", 14)
        except:
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # 绘制身份证信息
        draw.text((50, 50), "姓名: 张三", fill='black', font=font_medium)
        draw.text((50, 80), "性别: 男", fill='black', font=font_medium)
        draw.text((200, 80), "民族: 汉", fill='black', font=font_medium)
        draw.text((50, 110), "出生: 1990年01月01日", fill='black', font=font_medium)
        draw.text((50, 140), "住址: 北京市朝阳区测试街道123号", fill='black', font=font_medium)
        draw.text((50, 200), "公民身份号码", fill='black', font=font_small)
        draw.text((50, 230), "110101199001011234", fill='black', font=font_large)
        
        # 保存测试图片
        test_image_path = 'test_idcard.png'
        img.save(test_image_path)
        print(f"✓ 测试身份证图片创建成功: {test_image_path}")
        return test_image_path
        
    except Exception as e:
        print(f"✗ 测试图片创建失败: {e}")
        return None

def test_idcard_recognition(ocr, image_path):
    """测试身份证识别"""
    print(f"\n" + "=" * 50)
    print("测试身份证识别功能...")
    print("=" * 50)
    
    try:
        print(f"正在识别图片: {image_path}")
        start_time = time.time()
        
        # 执行OCR识别
        # 适配新版本PaddleOCR API
        try:
            result = ocr.ocr(image_path)
        except Exception as e:
            try:
                result = ocr.ocr(image_path, cls=True)
            except:
                result = ocr.ocr(image_path)
        
        end_time = time.time()
        print(f"✓ 身份证识别完成 (耗时: {end_time - start_time:.2f}秒)")
        
        # 处理识别结果
        if result and result[0]:
            print("\n识别结果:")
            texts = []
            for line in result[0]:
                if line and len(line) >= 2:
                    text = line[1][0] if line[1] else ""
                    confidence = line[1][1] if len(line[1]) > 1 else 0
                    if text.strip():
                        texts.append(text.strip())
                        print(f"  文字: {text.strip()} (置信度: {confidence:.2f})")
            
            # 简单的信息提取测试
            print("\n信息提取测试:")
            all_text = " ".join(texts)
            
            # 查找姓名
            if "姓名" in all_text:
                print("✓ 检测到姓名字段")
            
            # 查找身份证号
            import re
            id_pattern = r'[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]'
            if re.search(id_pattern, all_text):
                print("✓ 检测到身份证号码")
            
            # 查找性别
            if "男" in all_text or "女" in all_text:
                print("✓ 检测到性别信息")
            
            return True
        else:
            print("✗ 未识别到任何文字内容")
            return False
            
    except Exception as e:
        print(f"✗ 身份证识别失败: {e}")
        return False

def test_java_integration():
    """测试Java集成"""
    print(f"\n" + "=" * 50)
    print("测试Java集成...")
    print("=" * 50)
    
    # 检查身份证OCR脚本是否存在
    script_path = "scripts/idcard_ocr.py"
    if os.path.exists(script_path):
        print(f"✓ 身份证OCR脚本存在: {script_path}")
    else:
        print(f"✗ 身份证OCR脚本不存在: {script_path}")
        print("Java应用将使用内联命令模式")
    
    # 检查Java应用配置
    config_path = "src/main/resources/application.yml"
    if os.path.exists(config_path):
        print(f"✓ 应用配置文件存在: {config_path}")
        
        # 读取配置检查身份证OCR设置
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_content = f.read()
                if 'idcard:' in config_content:
                    print("✓ 检测到身份证OCR配置")
                else:
                    print("⚠ 未检测到身份证OCR专用配置")
        except Exception as e:
            print(f"⚠ 配置文件读取失败: {e}")
    else:
        print(f"✗ 应用配置文件不存在: {config_path}")

def cleanup_test_files():
    """清理测试文件"""
    test_files = ['test_idcard.png']
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✓ 清理测试文件: {file_path}")
            except Exception as e:
                print(f"⚠ 清理文件失败: {e}")

def main():
    """主函数"""
    print("身份证OCR功能测试")
    print("Python版本:", sys.version)
    
    success_count = 0
    total_tests = 4
    
    # 1. 测试PaddleOCR导入
    if test_paddleocr_import():
        success_count += 1
    else:
        print("\n❌ PaddleOCR导入失败，无法继续测试")
        return False
    
    # 2. 测试OCR初始化
    ocr = test_idcard_ocr_init()
    if ocr:
        success_count += 1
    else:
        print("\n❌ OCR初始化失败，无法继续测试")
        return False
    
    # 3. 测试识别功能
    test_image = create_test_idcard_image()
    if test_image and test_idcard_recognition(ocr, test_image):
        success_count += 1
    
    # 4. 测试Java集成
    test_java_integration()
    success_count += 1  # 这个测试总是通过
    
    # 清理测试文件
    cleanup_test_files()
    
    # 总结
    print("\n" + "=" * 50)
    print(f"测试完成: {success_count}/{total_tests} 项通过")
    print("=" * 50)
    
    if success_count == total_tests:
        print("🎉 所有测试通过！身份证OCR功能可以正常使用")
        print("\n使用说明:")
        print("1. 启动Java应用: mvn spring-boot:run")
        print("2. 访问: http://localhost:8082")
        print("3. 点击'身份证识别'功能卡片")
        print("4. 上传身份证图片进行识别")
        return True
    else:
        print("❌ 部分测试失败，请检查环境配置")
        print("\n解决方案:")
        print("1. 确保已安装PaddleOCR: pip install paddleocr")
        print("2. 检查网络连接（首次使用需下载模型）")
        print("3. 确保Python版本 >= 3.7")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
