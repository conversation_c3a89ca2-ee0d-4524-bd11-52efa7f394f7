#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证OCR环境安装脚本
专门为身份证识别优化的PaddleOCR环境配置
"""

import sys
import subprocess
import os
import platform

def run_command(command, description=""):
    """执行命令并处理错误"""
    print(f"正在执行: {description}")
    print(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"✓ {description} 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} 失败")
        print(f"错误: {e.stderr}")
        return False
    except Exception as e:
        print(f"✗ {description} 异常: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("错误: 需要 Python 3.7 或更高版本")
        return False
    
    print("✓ Python 版本符合要求")
    return True

def install_idcard_ocr_packages():
    """安装身份证OCR专用包"""
    packages = [
        "paddlepaddle",      # PaddlePaddle 框架
        "paddleocr",         # PaddleOCR
        "opencv-python",     # OpenCV
        "pillow",            # PIL
        "numpy",             # NumPy
        "scipy",             # SciPy (图像处理)
        "scikit-image",      # 图像处理工具
    ]
    
    print("开始安装身份证OCR专用依赖包...")
    
    # 升级 pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级 pip"):
        print("警告: pip 升级失败，继续安装其他包")
    
    # 安装包
    for package in packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"安装 {package}"):
            print(f"警告: {package} 安装失败")
            return False
    
    print("✓ 所有依赖包安装完成")
    return True

def test_idcard_ocr():
    """测试身份证OCR功能"""
    print("测试身份证OCR功能...")
    
    test_code = '''
import sys
try:
    from paddleocr import PaddleOCR
    import cv2
    import numpy as np
    from PIL import Image, ImageEnhance
    
    print("✓ 所有依赖包导入成功")
    
    # 初始化身份证专用OCR
    ocr = PaddleOCR(
        use_angle_cls=True,
        lang='ch',
        show_log=False,
        det_db_thresh=0.2,
        det_db_box_thresh=0.4,
        det_db_unclip_ratio=2.0
    )
    print("✓ 身份证OCR初始化成功")
    print("身份证OCR环境配置完成，可以正常使用")
    
except ImportError as e:
    print(f"✗ 依赖包导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ 身份证OCR测试失败: {e}")
    sys.exit(1)
'''
    
    return run_command(f'{sys.executable} -c "{test_code}"', "测试身份证OCR")

def create_test_idcard_script():
    """创建身份证测试脚本"""
    test_script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证OCR测试脚本
"""

import sys
import os

def test_idcard_ocr():
    try:
        from paddleocr import PaddleOCR
        import cv2
        import numpy as np
        from PIL import Image, ImageEnhance
        
        print("身份证OCR依赖包导入成功")
        
        # 创建身份证专用OCR实例
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            show_log=False,
            det_db_thresh=0.2,      # 身份证专用参数
            det_db_box_thresh=0.4,
            det_db_unclip_ratio=2.0,
            rec_batch_num=8,
            max_text_length=50
        )
        print("身份证OCR初始化成功")
        
        print("身份证OCR环境配置正确，可以正常使用")
        print("支持的功能:")
        print("- 身份证正面识别（姓名、性别、民族、出生日期、住址、身份证号）")
        print("- 身份证背面识别（签发机关、有效期限）")
        print("- 自动图像预处理和增强")
        print("- 结构化信息提取")
        print("- 身份证号码验证")
        
        return True
        
    except Exception as e:
        print(f"身份证OCR测试失败: {e}")
        return False

if __name__ == '__main__':
    if test_idcard_ocr():
        print("\\n✓ 身份证OCR测试通过")
        sys.exit(0)
    else:
        print("\\n✗ 身份证OCR测试失败")
        sys.exit(1)
'''
    
    try:
        with open('scripts/test_idcard_ocr.py', 'w', encoding='utf-8') as f:
            f.write(test_script_content)
        print("✓ 身份证OCR测试脚本创建成功: scripts/test_idcard_ocr.py")
        return True
    except Exception as e:
        print(f"✗ 身份证OCR测试脚本创建失败: {e}")
        return False

def create_sample_idcard_images():
    """创建示例身份证图像（用于测试）"""
    print("创建示例身份证图像...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        import os
        
        # 创建示例目录
        sample_dir = "samples/idcard"
        os.makedirs(sample_dir, exist_ok=True)
        
        # 创建身份证正面示例
        front_img = Image.new('RGB', (600, 380), color='lightblue')
        draw = ImageDraw.Draw(front_img)
        
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        # 添加正面信息
        front_text = [
            "姓名    张三",
            "性别    男        民族    汉族",
            "出生    1990年01月01日",
            "住址    北京市朝阳区某某街道某某号",
            "",
            "公民身份号码",
            "110101199001011234"
        ]
        
        y = 50
        for line in front_text:
            draw.text((50, y), line, fill='black', font=font)
            y += 35
        
        front_img.save(f"{sample_dir}/idcard_front_sample.png")
        
        # 创建身份证背面示例
        back_img = Image.new('RGB', (600, 380), color='lightgreen')
        draw = ImageDraw.Draw(back_img)
        
        back_text = [
            "",
            "签发机关    北京市公安局朝阳分局",
            "",
            "有效期限    2010.01.01-2030.01.01",
            "",
            "         中华人民共和国",
            "           居民身份证"
        ]
        
        y = 80
        for line in back_text:
            draw.text((50, y), line, fill='black', font=font)
            y += 40
        
        back_img.save(f"{sample_dir}/idcard_back_sample.png")
        
        print(f"✓ 示例身份证图像创建成功:")
        print(f"  - 正面: {sample_dir}/idcard_front_sample.png")
        print(f"  - 背面: {sample_dir}/idcard_back_sample.png")
        return True
        
    except Exception as e:
        print(f"✗ 示例图像创建失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("身份证OCR环境安装脚本")
    print("=" * 60)
    
    # 检查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 显示系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python 路径: {sys.executable}")
    
    # 安装依赖包
    if not install_idcard_ocr_packages():
        print("依赖包安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 测试身份证OCR
    if not test_idcard_ocr():
        print("身份证OCR测试失败，请检查安装")
        sys.exit(1)
    
    # 创建测试脚本
    create_test_idcard_script()
    
    # 创建示例图像
    create_sample_idcard_images()
    
    print("=" * 60)
    print("✓ 身份证OCR环境安装完成！")
    print("=" * 60)
    print("使用说明:")
    print("1. 运行 'python scripts/test_idcard_ocr.py' 测试环境")
    print("2. 运行 'python scripts/idcard_ocr.py <身份证图片路径>' 进行识别")
    print("3. 访问 'http://localhost:8080/idcard-ocr.html' 使用Web界面")
    print("4. 使用API接口:")
    print("   - POST /api/ocr/idcard/recognize - 通用身份证识别")
    print("   - POST /api/ocr/idcard/recognize/front - 正面识别")
    print("   - POST /api/ocr/idcard/recognize/back - 背面识别")
    print("   - POST /api/ocr/idcard - 快捷识别接口")
    print("5. 示例图像位置: samples/idcard/")

if __name__ == '__main__':
    main()
