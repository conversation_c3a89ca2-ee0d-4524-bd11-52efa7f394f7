#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR 测试脚本
验证 PaddleOCR 环境是否正确配置
"""

import sys
import os
import time

def test_imports():
    """测试必要的包导入"""
    print("测试包导入...")
    
    try:
        import cv2
        print("✓ OpenCV 导入成功")
    except ImportError as e:
        print(f"✗ OpenCV 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ NumPy 导入成功")
    except ImportError as e:
        print(f"✗ NumPy 导入失败: {e}")
        return False
    
    try:
        from PIL import Image
        print("✓ Pillow 导入成功")
    except ImportError as e:
        print(f"✗ Pillow 导入失败: {e}")
        return False
    
    try:
        from paddleocr import PaddleOCR
        print("✓ PaddleOCR 导入成功")
    except ImportError as e:
        print(f"✗ PaddleOCR 导入失败: {e}")
        print("请运行: pip install paddleocr")
        return False
    
    return True

def test_paddleocr_init():
    """测试 PaddleOCR 初始化"""
    print("\n测试 PaddleOCR 初始化...")
    
    try:
        from paddleocr import PaddleOCR
        
        # 初始化 OCR（第一次会下载模型）
        print("正在初始化 PaddleOCR（首次使用会下载模型，请耐心等待）...")
        start_time = time.time()
        
        ocr = PaddleOCR(
            use_textline_orientation=True,  # 替代 use_angle_cls
            lang='ch'
            # 移除 show_log 参数（已不支持）
            # 移除过时的检测参数，使用默认值
        )
        
        end_time = time.time()
        print(f"✓ PaddleOCR 初始化成功 (耗时: {end_time - start_time:.2f}秒)")
        return True
        
    except Exception as e:
        print(f"✗ PaddleOCR 初始化失败: {e}")
        return False

def create_test_image():
    """创建一个简单的测试图片"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import os
        
        # 创建一个简单的测试图片
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # 添加一些中文文字
        text = "这是一个测试文档\n用于验证OCR识别效果\n包含中文和数字123"
        
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            # 如果没有找到字体，使用默认字体
            font = ImageFont.load_default()
        
        # 绘制文字
        lines = text.split('\n')
        y = 30
        for line in lines:
            draw.text((20, y), line, fill='black', font=font)
            y += 40
        
        # 保存测试图片
        test_image_path = 'test_image.png'
        img.save(test_image_path)
        print(f"✓ 测试图片创建成功: {test_image_path}")
        return test_image_path
        
    except Exception as e:
        print(f"✗ 测试图片创建失败: {e}")
        return None

def test_ocr_recognition(image_path=None):
    """测试 OCR 识别功能"""
    if not image_path:
        print("\n创建测试图片...")
        image_path = create_test_image()
        if not image_path:
            return False
    
    print(f"\n测试 OCR 识别功能...")
    
    try:
        from paddleocr import PaddleOCR
        
        # 初始化 OCR
        ocr = PaddleOCR(use_textline_orientation=True, lang='ch')
        
        # 执行识别
        print(f"正在识别图片: {image_path}")
        start_time = time.time()
        result = ocr.predict(image_path)  # 使用新的predict方法
        end_time = time.time()
        
        print(f"✓ OCR 识别完成 (耗时: {end_time - start_time:.2f}秒)")
        
        # 显示结果
        if result and 'ocr' in result:
            print("\n识别结果:")
            ocr_results = result['ocr']
            for item in ocr_results:
                if 'text' in item:
                    text = item['text']
                    confidence = item.get('confidence', 0)
                    print(f"  文字: {text} (置信度: {confidence:.2f})")
        else:
            print("未识别到文字内容")
            print(f"返回结果格式: {type(result)}")
        
        # 清理测试图片
        if image_path == 'test_image.png' and os.path.exists(image_path):
            os.remove(image_path)
            print("✓ 测试图片已清理")
        
        return True
        
    except Exception as e:
        print(f"✗ OCR 识别测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("PaddleOCR 环境测试")
    print("=" * 50)
    
    # 测试包导入
    if not test_imports():
        print("\n包导入测试失败，请检查依赖安装")
        return False
    
    # 测试 PaddleOCR 初始化
    if not test_paddleocr_init():
        print("\nPaddleOCR 初始化测试失败")
        return False
    
    # 测试 OCR 识别
    if not test_ocr_recognition():
        print("\nOCR 识别测试失败")
        return False
    
    print("\n" + "=" * 50)
    print("✓ 所有测试通过！PaddleOCR 环境配置正确")
    print("=" * 50)
    print("\n可以在 Java 应用中正常使用 PaddleOCR 功能")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
