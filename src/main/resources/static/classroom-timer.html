<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课堂思考倒计时器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            overflow: hidden;
        }

        .container {
            text-align: center;
            max-width: 800px;
            width: 90%;
            padding: 20px;
        }

        .title {
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .timer-display {
            font-size: 8rem;
            font-weight: bold;
            margin: 40px 0;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
            font-family: 'Courier New', monospace;
            letter-spacing: 0.1em;
            transition: all 0.3s ease;
        }

        .timer-display.warning {
            color: #ff6b6b;
            animation: pulse 1s infinite;
        }

        .timer-display.finished {
            color: #ff4757;
            animation: flash 0.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes flash {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .preset-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }

        .preset-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .preset-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .custom-input {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .custom-input input {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 1.1rem;
            text-align: center;
            width: 100px;
            backdrop-filter: blur(10px);
        }

        .custom-input input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .control-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-width: 120px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .start-btn {
            background: rgba(46, 204, 113, 0.3);
            border-color: rgba(46, 204, 113, 0.5);
        }

        .pause-btn {
            background: rgba(241, 196, 15, 0.3);
            border-color: rgba(241, 196, 15, 0.5);
        }

        .reset-btn {
            background: rgba(231, 76, 60, 0.3);
            border-color: rgba(231, 76, 60, 0.5);
        }

        .status-text {
            font-size: 1.5rem;
            margin: 20px 0;
            opacity: 0.9;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2ecc71, #27ae60);
            border-radius: 4px;
            transition: width 0.1s ease;
        }

        @media (max-width: 768px) {
            .timer-display {
                font-size: 5rem;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .preset-buttons {
                gap: 10px;
            }
            
            .preset-btn {
                padding: 12px 20px;
                font-size: 1rem;
            }
            
            .control-btn {
                padding: 12px 20px;
                font-size: 1rem;
                min-width: 100px;
            }
        }

        @media (max-width: 480px) {
            .timer-display {
                font-size: 3.5rem;
            }
            
            .custom-input {
                flex-direction: column;
            }
            
            .control-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">课堂思考倒计时器</h1>
        
        <div class="timer-display" id="timerDisplay">00:00</div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        
        <div class="status-text" id="statusText">请选择倒计时时长</div>
        
        <div class="preset-buttons">
            <button class="preset-btn" onclick="setTimer(30)">30秒</button>
            <button class="preset-btn" onclick="setTimer(60)">1分钟</button>
            <button class="preset-btn" onclick="setTimer(120)">2分钟</button>
            <button class="preset-btn" onclick="setTimer(300)">5分钟</button>
            <button class="preset-btn" onclick="setTimer(600)">10分钟</button>
            <button class="preset-btn" onclick="setTimer(900)">15分钟</button>
        </div>
        
        <div class="custom-input">
            <span>自定义时长：</span>
            <input type="number" id="customMinutes" placeholder="分钟" min="0" max="59">
            <span>分</span>
            <input type="number" id="customSeconds" placeholder="秒" min="0" max="59">
            <span>秒</span>
            <button class="preset-btn" onclick="setCustomTimer()">设置</button>
        </div>
        
        <div class="control-buttons">
            <button class="control-btn start-btn" id="startBtn" onclick="startTimer()">开始</button>
            <button class="control-btn pause-btn" id="pauseBtn" onclick="pauseTimer()" disabled>暂停</button>
            <button class="control-btn reset-btn" id="resetBtn" onclick="resetTimer()" disabled>重置</button>
        </div>
    </div>

    <script>
        let timerInterval;
        let totalSeconds = 0;
        let remainingSeconds = 0;
        let isRunning = false;
        let isPaused = false;
        
        // 音频提示
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        function playBeep(frequency = 800, duration = 200) {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = frequency;
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration / 1000);
        }
        
        function setTimer(seconds) {
            if (isRunning) return;
            
            totalSeconds = seconds;
            remainingSeconds = seconds;
            updateDisplay();
            updateProgressBar();
            updateStatus('计时器已设置，点击开始按钮开始倒计时');
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('resetBtn').disabled = false;
        }
        
        function setCustomTimer() {
            if (isRunning) return;
            
            const minutes = parseInt(document.getElementById('customMinutes').value) || 0;
            const seconds = parseInt(document.getElementById('customSeconds').value) || 0;
            
            if (minutes === 0 && seconds === 0) {
                alert('请输入有效的时间！');
                return;
            }
            
            const totalTime = minutes * 60 + seconds;
            setTimer(totalTime);
            
            // 清空输入框
            document.getElementById('customMinutes').value = '';
            document.getElementById('customSeconds').value = '';
        }
        
        function startTimer() {
            if (remainingSeconds <= 0) return;
            
            isRunning = true;
            isPaused = false;
            
            document.getElementById('startBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = false;
            document.getElementById('resetBtn').disabled = false;
            
            updateStatus('倒计时进行中...');
            
            timerInterval = setInterval(() => {
                remainingSeconds--;
                updateDisplay();
                updateProgressBar();
                
                // 最后10秒警告
                if (remainingSeconds <= 10 && remainingSeconds > 0) {
                    document.getElementById('timerDisplay').className = 'timer-display warning';
                    playBeep(1000, 100);
                }
                
                // 倒计时结束
                if (remainingSeconds <= 0) {
                    finishTimer();
                }
            }, 1000);
        }
        
        function pauseTimer() {
            if (!isRunning) return;
            
            if (isPaused) {
                // 恢复
                startTimer();
                document.getElementById('pauseBtn').textContent = '暂停';
                updateStatus('倒计时继续进行中...');
            } else {
                // 暂停
                clearInterval(timerInterval);
                isRunning = false;
                isPaused = true;
                
                document.getElementById('startBtn').disabled = false;
                document.getElementById('pauseBtn').textContent = '继续';
                updateStatus('倒计时已暂停');
            }
        }
        
        function resetTimer() {
            clearInterval(timerInterval);
            isRunning = false;
            isPaused = false;
            remainingSeconds = totalSeconds;
            
            document.getElementById('timerDisplay').className = 'timer-display';
            document.getElementById('startBtn').disabled = totalSeconds <= 0;
            document.getElementById('pauseBtn').disabled = true;
            document.getElementById('pauseBtn').textContent = '暂停';
            document.getElementById('resetBtn').disabled = totalSeconds <= 0;
            
            updateDisplay();
            updateProgressBar();
            updateStatus(totalSeconds > 0 ? '计时器已重置' : '请选择倒计时时长');
        }
        
        function finishTimer() {
            clearInterval(timerInterval);
            isRunning = false;
            isPaused = false;
            remainingSeconds = 0;
            
            document.getElementById('timerDisplay').className = 'timer-display finished';
            document.getElementById('startBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = true;
            document.getElementById('resetBtn').disabled = false;
            
            updateDisplay();
            updateProgressBar();
            updateStatus('时间到！');
            
            // 播放结束提示音
            playBeep(1200, 300);
            setTimeout(() => playBeep(1200, 300), 400);
            setTimeout(() => playBeep(1200, 500), 800);
        }
        
        function updateDisplay() {
            const minutes = Math.floor(remainingSeconds / 60);
            const seconds = remainingSeconds % 60;
            const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timerDisplay').textContent = display;
        }
        
        function updateProgressBar() {
            if (totalSeconds <= 0) {
                document.getElementById('progressFill').style.width = '0%';
                return;
            }
            
            const progress = ((totalSeconds - remainingSeconds) / totalSeconds) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }
        
        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                if (isRunning) {
                    pauseTimer();
                } else if (remainingSeconds > 0) {
                    startTimer();
                }
            } else if (e.code === 'KeyR') {
                e.preventDefault();
                resetTimer();
            }
        });
        
        // 初始化
        updateDisplay();
        updateProgressBar();
    </script>
</body>
</html>
