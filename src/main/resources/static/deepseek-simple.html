<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek 聊天 - 简化版</title>

    <!-- KaTeX数学公式渲染 - 使用国内CDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.16.8/katex.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.16.8/katex.min.js"></script>

    <!-- 通用Markdown解析器 -->
    <link rel="stylesheet" href="css/markdown-styles.css">
    <script src="js/katex-fallback.js"></script>
    <script src="js/markdown-parser.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .model-selector {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }

        .model-selector label {
            font-weight: bold;
            margin-right: 10px;
        }

        .model-selector select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .model-description {
            padding: 12px 20px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            font-size: 14px;
            color: #1565c0;
            border-bottom: 1px solid #eee;
        }

        .chat-area {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .message.user .message-avatar {
            background: #667eea;
        }

        .message.assistant .message-avatar {
            background: #764ba2;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #667eea;
            color: white;
        }

        .message.assistant .message-content {
            background: #f1f3f4;
            color: #333;
        }

        .input-area {
            padding: 20px;
            display: flex;
            gap: 12px;
        }

        .input-area input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 24px;
            outline: none;
            font-size: 14px;
        }

        .input-area input:focus {
            border-color: #667eea;
        }

        .input-area button {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 24px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
            margin-left: 8px;
        }

        .input-area button:hover {
            background: #5a6fd8;
        }

        .input-area button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 12px 16px;
            margin: 16px;
            border-radius: 8px;
            border-left: 4px solid #c62828;
        }

        /* Markdown样式 */
        .message-content h1, .message-content h2, .message-content h3,
        .message-content h4, .message-content h5, .message-content h6 {
            margin: 12px 0 8px 0;
            font-weight: bold;
            line-height: 1.4;
        }

        .message-content h1 { font-size: 1.4em; color: #2c3e50; }
        .message-content h2 { font-size: 1.3em; color: #34495e; }
        .message-content h3 { font-size: 1.2em; color: #34495e; }
        .message-content h4 { font-size: 1.1em; color: #5d6d7e; }
        .message-content h5 { font-size: 1.05em; color: #5d6d7e; }
        .message-content h6 { font-size: 1.0em; color: #7b8a8b; }

        .message-content code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .message-content pre {
            background: rgba(0, 0, 0, 0.05);
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 8px 0;
        }

        .message-content pre code {
            background: none;
            padding: 0;
        }

        .message-content strong {
            font-weight: bold;
        }

        .message-content em {
            font-style: italic;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 DeepSeek 聊天</h1>
            <p>简化版</p>
        </div>
        
        <div class="model-selector">
            <label for="modelSelect">选择模型：</label>
            <select id="modelSelect" onchange="updateModelDescription()">
                <option value="deepseek-chat">DeepSeek Chat</option>
                <option value="deepseek-coder">DeepSeek Coder</option>
                <option value="deepseek-reasoner">DeepSeek Reasoner</option>
                <option value="deepseek-v2">DeepSeek V2</option>
                <option value="deepseek-vl">DeepSeek VL</option>
            </select>
        </div>

        <!-- 模型描述 -->
        <div id="modelDescription" class="model-description">
            <strong>DeepSeek Chat：</strong>通用对话模型，适合日常交流和问答
        </div>
        
        <div id="chatArea" class="chat-area">
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    您好！我是DeepSeek AI助手。有什么可以帮助您的吗？
                </div>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="输入您的问题..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()" id="sendBtn">发送</button>
            <button onclick="clearHistory()" id="clearBtn">清除历史</button>
        </div>
    </div>

    <script>
        let isLoading = false;
        let sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // 使用通用Markdown解析器（已在markdown-parser.js中定义）

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 更新模型描述
        function updateModelDescription() {
            const modelSelect = document.getElementById('modelSelect');
            const modelDescription = document.getElementById('modelDescription');
            const selectedModel = modelSelect.value;

            const descriptions = {
                'deepseek-chat': '<strong>DeepSeek Chat：</strong>通用对话模型，适合日常交流和问答',
                'deepseek-coder': '<strong>DeepSeek Coder：</strong>专业代码生成模型，适合编程任务',
                'deepseek-reasoner': '<strong>DeepSeek Reasoner：</strong>推理增强模型，适合复杂逻辑分析',
                'deepseek-v2': '<strong>DeepSeek V2：</strong>最新版本模型，性能全面提升',
                'deepseek-vl': '<strong>DeepSeek VL：</strong>视觉语言模型，支持图像理解和分析'
            };

            modelDescription.innerHTML = descriptions[selectedModel] || descriptions['deepseek-chat'];
        }

        function addMessage(content, isUser = false) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = isUser ? '我' : 'AI';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = isUser ? 'message-content' : 'message-content markdown-content';

            if (isUser) {
                contentDiv.textContent = content;
            } else {
                contentDiv.innerHTML = parseMarkdown(content);
            }
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            chatArea.appendChild(messageDiv);
            
            // 滚动到底部
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const clearBtn = document.getElementById('clearBtn');
            const modelSelect = document.getElementById('modelSelect');
            const message = input.value.trim();
            
            if (!message || isLoading) return;
            
            // 添加用户消息
            addMessage(message, true);
            input.value = '';
            
            // 设置加载状态
            isLoading = true;
            sendBtn.disabled = true;
            clearBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            
            try {
                const response = await fetch('/api/deepseek/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: modelSelect.value,
                        prompt: message,
                        sessionId: sessionId
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addMessage(data.text || data.content || '收到回复');
                } else {
                    addMessage(`❌ 请求失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('请求失败:', error);
                addMessage(`❌ 网络错误: ${error.message}`);
            } finally {
                isLoading = false;
                sendBtn.disabled = false;
                clearBtn.disabled = false;
                sendBtn.textContent = '发送';
            }
        }

        function clearHistory() {
            const chatArea = document.getElementById('chatArea');
            chatArea.innerHTML = `
                <div class="message assistant">
                    <div class="message-avatar">AI</div>
                    <div class="message-content">
                        历史记录已清除。有什么可以帮助您的吗？
                    </div>
                </div>
            `;
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            updateModelDescription(); // 初始化模型描述
        });
    </script>
</body>
</html>
