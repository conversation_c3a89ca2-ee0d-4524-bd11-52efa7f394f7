<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课堂分贝检测器</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/decibel-monitor.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .monitor-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            max-width: 800px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            color: #666;
            font-size: 16px;
            margin: 0;
        }

        .decibel-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 40px 20px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .decibel-value {
            font-size: 72px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .decibel-unit {
            font-size: 24px;
            color: #666;
            margin-left: 10px;
        }

        .status-text {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .level-indicator {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            position: relative;
            margin: 20px 0;
            overflow: hidden;
        }

        .level-bar {
            height: 100%;
            border-radius: 10px;
            transition: all 0.3s ease;
            position: relative;
        }

        .level-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .controls {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-start {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
        }

        .btn-start:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.6);
        }

        .btn-stop {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4);
        }

        .btn-stop:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 101, 101, 0.6);
        }

        .btn-calibrate {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(237, 137, 54, 0.4);
        }

        .btn-calibrate:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(237, 137, 54, 0.6);
        }

        .btn-settings {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-settings:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-disabled {
            background: #ccc !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
        }

        .thresholds {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .threshold-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .threshold-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .threshold-range {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .history-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .history-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .history-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        /* 状态颜色 */
        .status-quiet {
            color: #48bb78;
        }

        .status-normal {
            color: #667eea;
        }

        .status-active {
            color: #ed8936;
        }

        .status-loud {
            color: #f56565;
        }

        .level-quiet {
            background: linear-gradient(90deg, #48bb78, #68d391);
        }

        .level-normal {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .level-active {
            background: linear-gradient(90deg, #ed8936, #f6ad55);
        }

        .level-loud {
            background: linear-gradient(90deg, #f56565, #fc8181);
        }

        .permission-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }

        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .monitor-container {
                padding: 20px;
            }

            .decibel-value {
                font-size: 56px;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            .control-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }

            .thresholds {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="monitor-container">
        <div class="header">
            <h1>📊 课堂分贝检测器</h1>
            <p>实时监测课堂音量，激励学生积极朗读</p>
            <p style="font-size: 14px; color: #666; margin-top: 10px;">
                💡 首次使用建议：在安静环境下点击"校准"按钮进行基准校准
            </p>
        </div>

        <div id="permissionNotice" class="permission-notice" style="display: none;">
            <strong>需要麦克风权限</strong><br>
            请允许浏览器访问您的麦克风以开始分贝检测
        </div>

        <div id="errorMessage" class="error-message" style="display: none;"></div>

        <div class="decibel-display">
            <div class="decibel-value" id="decibelValue">
                --<span class="decibel-unit">dB</span>
            </div>
            <div class="status-text" id="statusText">等待开始检测</div>
            <div class="level-indicator">
                <div class="level-bar" id="levelBar" style="width: 0%;"></div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn btn-start" id="startBtn" onclick="startMonitoring()">
                🎤 开始检测
            </button>
            <button class="control-btn btn-stop btn-disabled" id="stopBtn" onclick="stopMonitoring()" disabled>
                ⏹️ 停止检测
            </button>
            <button class="control-btn btn-calibrate btn-disabled" id="calibrateBtn" onclick="startCalibration()" disabled>
                🎯 校准
            </button>
            <button class="control-btn btn-settings" onclick="showSettings()">
                ⚙️ 设置 
            </button>
        </div>

        <div class="thresholds">
            <div class="threshold-item">
                <div class="threshold-label">安静</div>
                <div class="threshold-range">< 40 dB</div>
            </div>
            <div class="threshold-item">
                <div class="threshold-label">正常</div>
                <div class="threshold-range">40-60 dB</div>
            </div>
            <div class="threshold-item">
                <div class="threshold-label">活跃</div>
                <div class="threshold-range">60-80 dB</div>
            </div>
            <div class="threshold-item">
                <div class="threshold-label">响亮</div>
                <div class="threshold-range">> 80 dB</div>
            </div>
        </div>

        <div class="history-section">
            <div class="history-title">📈 检测统计</div>
            <div class="history-stats">
                <div class="stat-item">
                    <div class="stat-value" id="avgDecibel">--</div>
                    <div class="stat-label">平均分贝</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="maxDecibel">--</div>
                    <div class="stat-label">最高分贝</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="activeTime">--</div>
                    <div class="stat-label">活跃时长</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalTime">--</div>
                    <div class="stat-label">总时长</div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/decibel-monitor.js"></script>
</body>
</html>
