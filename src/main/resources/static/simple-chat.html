<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百炼智能体 - 简单聊天</title>

    <!-- 本地Markdown解析器 -->
    <link rel="stylesheet" href="css/markdown-styles.css">
    <script src="js/katex-fallback.js"></script>
    <script src="js/markdown-parser.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .config-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .config-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .config-group label {
            font-size: 14px;
            white-space: nowrap;
        }

        .config-group input, .config-group select {
            padding: 6px 10px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
        }

        .app-id-input {
            width: 200px;
        }

        .mode-select {
            width: 120px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 20px auto;
            width: 100%;
            padding: 0 20px;
        }

        .messages {
            flex: 1;
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            overflow-y: auto;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 400px;
            max-height: calc(100vh - 250px);
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #667eea;
        }

        .message.assistant .message-avatar {
            background: #6c757d;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        /* Markdown样式 */
        .message-content h1, .message-content h2, .message-content h3,
        .message-content h4, .message-content h5, .message-content h6 {
            margin: 16px 0 8px 0;
            font-weight: bold;
            line-height: 1.25;
        }

        .message-content h1 { font-size: 1.5em; border-bottom: 1px solid #ddd; padding-bottom: 8px; }
        .message-content h2 { font-size: 1.3em; }
        .message-content h3 { font-size: 1.1em; }

        .message-content p {
            margin: 8px 0;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
        }

        .message-content blockquote {
            margin: 8px 0;
            padding: 8px 16px;
            background: rgba(0, 123, 255, 0.1);
            border-left: 4px solid #007bff;
            font-style: italic;
        }

        .message-content code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .message-content pre {
            background: rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .message-content pre code {
            background: none;
            padding: 0;
            border-radius: 0;
        }

        .message-content table {
            border-collapse: collapse;
            margin: 8px 0;
            width: 100%;
        }

        .message-content th, .message-content td {
            border: 1px solid rgba(0, 0, 0, 0.2);
            padding: 8px 12px;
            text-align: left;
        }

        .message-content th {
            background: rgba(0, 0, 0, 0.05);
            font-weight: bold;
        }

        .message-content a {
            color: #007bff;
            text-decoration: none;
        }

        .message-content a:hover {
            text-decoration: underline;
        }

        .message-content strong {
            font-weight: bold;
        }

        .message-content em {
            font-style: italic;
        }

        .message-content hr {
            border: none;
            border-top: 1px solid rgba(0, 0, 0, 0.2);
            margin: 16px 0;
        }

        /* 文件上传样式 */
        .file-upload-area {
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .file-upload-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin-bottom: 20px;
        }

        .file-upload-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .file-upload-header h3 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 1.2em;
        }

        .file-upload-header p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }

        .file-upload-zone {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .file-upload-zone:hover {
            border-color: #007bff;
            background: #f0f8ff;
        }

        .file-upload-zone.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .file-upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .file-upload-text {
            color: #666;
        }

        .file-upload-hint {
            font-size: 0.8em;
            color: #999;
            margin-top: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px 16px;
            margin-top: 16px;
        }

        .file-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .clear-file-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .clear-file-btn:hover {
            background: #c82333;
        }

        /* 模型描述样式 */
        .model-description {
            padding: 12px 20px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            font-size: 14px;
            color: #1565c0;
            margin-bottom: 20px;
        }

        .message.user .message-content {
            background: #667eea;
            color: white;
        }

        .message.assistant .message-content {
            background: #f1f3f4;
            color: #333;
        }

        .message-time {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        .input-area {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .input-tools {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .tool-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .tool-btn:hover {
            background: #f8f9fa;
            border-color: #667eea;
        }

        .input-row {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            resize: none;
            font-size: 16px;
            font-family: inherit;
            outline: none;
            min-height: 50px;
            max-height: 120px;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-btn {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background 0.3s;
            height: 50px;
        }

        .send-btn:hover:not(:disabled) {
            background: #5a6fd8;
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            font-style: italic;
        }

        .loading-dots {
            display: inline-flex;
            gap: 4px;
        }

        .loading-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #667eea;
            animation: loading 1.4s infinite ease-in-out;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .status-bar {
            padding: 10px 20px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin-bottom: 15px;
            border-radius: 4px;
            font-size: 14px;
        }

        .error-bar {
            background: #ffebee;
            border-left-color: #f44336;
            color: #c62828;
        }

        .success-bar {
            background: #e8f5e8;
            border-left-color: #4caf50;
            color: #2e7d32;
        }

        @media (max-width: 768px) {
            .config-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .config-group {
                justify-content: space-between;
            }
            
            .app-id-input {
                width: 100%;
            }
            
            .chat-container {
                margin: 10px auto;
                padding: 0 10px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 阿里百炼智能体</h1>
        <div class="config-row">
            <div class="config-group">
                <label>应用ID:</label>
                <input type="text" id="appId" class="app-id-input" value="3267ecc2febd4c37b0baf9a08957daaa" placeholder="请输入应用ID">
            </div>
            <div class="config-group">
                <label>对话模式:</label>
                <select id="chatMode" class="mode-select" onchange="toggleFileUpload()">
                    <option value="normal">普通对话</option>
                    <option value="stream">流式对话</option>
                    <option value="multi-turn">多轮对话</option>
                    <option value="knowledge">知识库检索</option>
                    <option value="document">文档对话</option>
                </select>
            </div>
            <div class="config-group">
                <label>选择模型:</label>
                <select id="modelSelect" class="mode-select" onchange="updateModelDescription()">
                    <option value="qwen-turbo">通义千问-Turbo</option>
                    <option value="qwen-plus">通义千问-Plus</option>
                    <option value="qwen-max">通义千问-Max</option>
                    <option value="qwen-max-longcontext">通义千问-Max-长文本</option>
                    <option value="qwen-vl-plus">通义千问-视觉Plus</option>
                    <option value="qwen-vl-max">通义千问-视觉Max</option>
                </select>
            </div>
        </div>

        <!-- 模型描述 -->
        <div id="modelDescription" class="model-description">
            <strong>通义千问-Turbo：</strong>速度快，适合日常对话和简单任务
        </div>
    </div>

    <!-- 文件上传区域 -->
    <div id="fileUploadArea" class="file-upload-area" style="display: none;">
        <div class="file-upload-container">
            <div class="file-upload-header">
                <h3>📄 文档对话</h3>
                <p>上传文档文件，AI将基于文档内容回答您的问题</p>
            </div>
            <div class="file-upload-zone" onclick="document.getElementById('fileInput').click()">
                <div class="file-upload-icon">📁</div>
                <div class="file-upload-text">
                    <div>点击选择文件或拖拽文件到此处</div>
                    <div class="file-upload-hint">支持图片、PDF、Word、Excel、PPT等文件，最大20MB</div>
                </div>
                <input type="file" id="fileInput" accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.md,.json,.csv,.xml,.html,.jpg,.jpeg,.png,.gif,.bmp,.webp,.svg" style="display: none;" onchange="handleFileSelect(event)">
            </div>
            <div id="fileInfo" class="file-info" style="display: none;">
                <div class="file-details">
                    <span id="fileName"></span>
                    <span id="fileSize"></span>
                </div>
                <button onclick="clearFile()" class="clear-file-btn">✕</button>
            </div>
        </div>
    </div>

    <div class="chat-container">
        <div class="messages" id="messages">
            <div id="statusBar" class="status-bar">
                正在检查服务状态...
            </div>
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div>
                    <div class="message-content">
                        您好！我是阿里百炼智能体，很高兴为您服务。请先配置您的应用ID，然后就可以开始对话了。
                    </div>
                    <div class="message-time" id="welcomeTime"></div>
                </div>
            </div>
        </div>

        <div class="input-area">
            <div class="input-tools">
                <button class="tool-btn" onclick="clearMessages()">🗑️ 清除对话</button>
                <button class="tool-btn" onclick="showHistory()">📋 查看历史</button>
                <button class="tool-btn" onclick="newSession()">🆕 新会话</button>
                <button class="tool-btn" onclick="testConnection()">🔗 测试连接</button>
            </div>
            <div class="input-row">
                <textarea 
                    id="messageInput" 
                    class="message-input" 
                    placeholder="请输入您的问题... (Ctrl+Enter发送)"
                    rows="1"
                ></textarea>
                <button id="sendBtn" class="send-btn" onclick="sendMessage()">发送</button>
                <button onclick="testStreamChat()" class="send-btn" style="margin-left: 10px; background: #ff9800;">测试流式</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量 - 使用相对路径避免跨域问题
        const API_BASE = window.location.origin;
        let sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        let isLoading = false;
        let selectedFile = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('welcomeTime').textContent = formatTime(Date.now());
            checkServiceStatus();

            // 自动调整输入框高度
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('input', autoResize);
            messageInput.addEventListener('keydown', handleKeyDown);

            // 绑定文件拖拽事件
            setupFileDragAndDrop();

            // 检查本地Markdown解析器是否加载成功
            setTimeout(() => {
                if (typeof parseMarkdown !== 'undefined') {
                    console.log('本地Markdown解析器加载成功');
                } else {
                    console.warn('本地Markdown解析器未加载');
                }
            }, 1000);
        });

        // 切换文件上传区域显示
        function toggleFileUpload() {
            const chatMode = document.getElementById('chatMode').value;
            const fileUploadArea = document.getElementById('fileUploadArea');

            if (chatMode === 'document') {
                fileUploadArea.style.display = 'block';
            } else {
                fileUploadArea.style.display = 'none';
                clearFile(); // 清除已选择的文件
            }
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                // 检查文件大小（20MB限制）
                if (file.size > 20 * 1024 * 1024) {
                    alert('文件大小不能超过20MB');
                    return;
                }

                selectedFile = file;
                showFileInfo(file);
            }
        }

        // 显示文件信息
        function showFileInfo(file) {
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');

            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'flex';
        }

        // 清除文件
        function clearFile() {
            selectedFile = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('fileInfo').style.display = 'none';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 设置文件拖拽功能
        function setupFileDragAndDrop() {
            const uploadZone = document.querySelector('.file-upload-zone');
            if (!uploadZone) return;

            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadZone.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadZone.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadZone.addEventListener(eventName, unhighlight, false);
            });

            function highlight(e) {
                uploadZone.classList.add('dragover');
            }

            function unhighlight(e) {
                uploadZone.classList.remove('dragover');
            }

            uploadZone.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                if (files.length > 0) {
                    const file = files[0];
                    document.getElementById('fileInput').files = files;
                    handleFileSelect({ target: { files: [file] } });
                }
            }
        }

        // 检查服务状态
        async function checkServiceStatus() {
            const statusBar = document.getElementById('statusBar');

            try {
                // 改为检查根路径或API路径
                const response = await fetch(`${API_BASE}/`);
                if (response.ok) {
                    statusBar.className = 'status-bar success-bar';
                    statusBar.textContent = '✅ 服务连接正常，可以开始对话';
                } else {
                    statusBar.className = 'status-bar error-bar';
                    statusBar.textContent = `❌ 服务响应异常 (状态码: ${response.status})`;
                }
            } catch (error) {
                statusBar.className = 'status-bar error-bar';
                statusBar.textContent = `❌ 无法连接到服务，请检查应用是否正常运行`;
            }
        }

        // 发送消息
        async function sendMessage() {
            const appId = document.getElementById('appId').value.trim();
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            const chatMode = document.getElementById('chatMode').value;
            const sendBtn = document.getElementById('sendBtn');

            if (!appId) {
                alert('请输入应用ID');
                return;
            }

            if (!message) {
                alert('请输入消息内容');
                return;
            }

            if (isLoading) {
                return;
            }

            // 添加用户消息
            addMessage('user', message);
            messageInput.value = '';
            autoResize.call(messageInput);

            // 设置加载状态
            isLoading = true;
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';

            // 添加加载提示
            const loadingId = addMessage('assistant', '', true);

            try {
                let endpoint;
                let requestData = {
                    appId: appId,
                    sessionId: sessionId
                };

                // 流式对话需要特殊处理
                if (chatMode === 'stream') {
                    handleStreamChat(message, loadingId);
                    return;
                }

                // 文档对话需要特殊处理
                if (chatMode === 'document') {
                    handleDocumentChat(message, loadingId);
                    return;
                }

                switch (chatMode) {
                    case 'multi-turn':
                        endpoint = `${API_BASE}/api/bailian/chat/multi-turn`;
                        requestData.messages = getConversationHistory();
                        break;
                    case 'knowledge':
                        endpoint = `${API_BASE}/api/bailian/knowledge/search`;
                        requestData.query = message;
                        break;
                    default:
                        endpoint = `${API_BASE}/api/bailian/chat`;
                        requestData.prompt = message;
                        requestData.incrementalOutput = false;
                }

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                // 移除加载提示
                removeMessage(loadingId);

                if (response.ok) {
                    // 添加AI回复
                    addMessage('assistant', data.text);

                    // 更新会话ID
                    if (data.sessionId) {
                        sessionId = data.sessionId;
                    }
                } else {
                    addMessage('assistant', `❌ 请求失败: ${data.error || '未知错误'}`, false, true);
                }

            } catch (error) {
                removeMessage(loadingId);
                addMessage('assistant', `❌ 网络错误: ${error.message}`, false, true);
            } finally {
                isLoading = false;
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
            }
        }

        // 添加消息到界面
        function addMessage(role, content, isLoading = false, isError = false) {
            const messages = document.getElementById('messages');
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.id = messageId;
            
            let avatarText = role === 'user' ? '我' : 'AI';
            let messageContent = content;
            
            if (isLoading) {
                messageContent = `
                    <div class="loading">
                        <div class="loading-dots">
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                        </div>
                        <span>正在思考中...</span>
                    </div>
                `;
            } else if (role === 'assistant' && content && !isError) {
                // 对AI回复进行Markdown渲染
                try {
                    if (typeof parseMarkdown !== 'undefined') {
                        messageContent = parseMarkdown(content);
                    }
                } catch (e) {
                    console.warn('Markdown解析失败，使用原始文本:', e);
                    messageContent = content;
                }
            }
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatarText}</div>
                <div>
                    <div class="message-content" ${isError ? 'style="background: #ffebee; color: #c62828;"' : ''}>
                        ${messageContent}
                    </div>
                    <div class="message-time">${formatTime(Date.now())}</div>
                </div>
            `;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;

            // 如果包含代码块，进行语法高亮
            if (messageContent.includes('<pre><code>')) {
                setTimeout(() => {
                    if (typeof Prism !== 'undefined') {
                        Prism.highlightAllUnder(messageDiv);
                    }
                }, 100);
            }

            return messageId;
        }

        // 更新消息内容（用于流式对话）
        function updateMessageContent(messageId, content, isComplete = false) {
            const messageElement = document.getElementById(messageId);
            if (!messageElement) return;

            const contentElement = messageElement.querySelector('.message-content');
            if (!contentElement) return;

            if (isComplete && typeof parseMarkdown !== 'undefined') {
                // 完整内容，进行Markdown渲染
                try {
                    contentElement.innerHTML = parseMarkdown(content);
                } catch (e) {
                    console.warn('Markdown解析失败，使用原始文本:', e);
                    contentElement.textContent = content;
                }
            } else {
                // 流式输出，暂时使用纯文本
                contentElement.textContent = content;
            }

            // 滚动到底部
            const messages = document.getElementById('messages');
            messages.scrollTop = messages.scrollHeight;
        }

        // 移除消息
        function removeMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                messageElement.remove();
            }
        }

        // 获取对话历史
        function getConversationHistory() {
            const messages = document.querySelectorAll('.message:not([id*="loading"])');
            const history = [];
            
            messages.forEach(msg => {
                if (msg.classList.contains('user')) {
                    history.push({
                        role: 'user',
                        content: msg.querySelector('.message-content').textContent.trim()
                    });
                } else if (msg.classList.contains('assistant')) {
                    const content = msg.querySelector('.message-content').textContent.trim();
                    if (content && !content.includes('正在思考中') && !content.includes('您好！我是')) {
                        history.push({
                            role: 'assistant',
                            content: content
                        });
                    }
                }
            });
            
            return history;
        }

        // 清除对话
        function clearMessages() {
            if (confirm('确定要清除所有对话记录吗？')) {
                const messages = document.getElementById('messages');
                const statusBar = document.getElementById('statusBar');
                messages.innerHTML = '';
                messages.appendChild(statusBar);
                
                // 重新添加欢迎消息
                addMessage('assistant', '对话记录已清除，您可以开始新的对话。');
            }
        }

        // 新会话
        function newSession() {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            clearMessages();
            addMessage('assistant', '新会话已创建，会话ID: ' + sessionId);
        }

        // 查看历史
        function showHistory() {
            const history = getConversationHistory();
            if (history.length === 0) {
                alert('暂无对话历史');
                return;
            }
            
            let historyText = '对话历史:\n\n';
            history.forEach((msg, index) => {
                historyText += `${index + 1}. ${msg.role === 'user' ? '用户' : 'AI'}: ${msg.content}\n\n`;
            });
            
            alert(historyText);
        }

        // 测试连接
        async function testConnection() {
            const statusBar = document.getElementById('statusBar');
            statusBar.className = 'status-bar';
            statusBar.textContent = '正在测试连接...';

            await checkServiceStatus();
        }

        // 格式化时间
        function formatTime(timestamp) {
            return new Date(timestamp).toLocaleTimeString();
        }

        // 自动调整输入框高度
        function autoResize() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        }

        // 处理键盘事件
        function handleKeyDown(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        }

        // 处理流式对话
        function handleStreamChat(message, loadingId) {
            const appId = document.getElementById('appId').value.trim();

            // 移除加载提示，添加AI消息容器
            removeMessage(loadingId);
            const assistantMessageId = addMessage('assistant', '正在思考...');
            const assistantMessageElement = document.getElementById(assistantMessageId);
            const contentElement = assistantMessageElement.querySelector('.message-content');

            let fullResponse = '';

            // 创建请求数据
            const requestData = {
                appId: appId,
                prompt: message,
                sessionId: sessionId
            };

            console.log('发送流式对话请求:', requestData);

            // 使用fetch发送POST请求启动流式对话
            fetch(`${API_BASE}/api/bailian/chat/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestData)
            }).then(response => {
                console.log('收到响应:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                // 清空初始文本
                contentElement.textContent = '';

                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let buffer = '';

                function readChunk() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            console.log('流式响应完成');

                            // 流式完成后，进行Markdown渲染
                            if (fullResponse && typeof parseMarkdown !== 'undefined') {
                                try {
                                    contentElement.innerHTML = parseMarkdown(fullResponse);
                                } catch (e) {
                                    console.warn('Markdown解析失败，保持原始文本:', e);
                                }
                            }

                            isLoading = false;
                            document.getElementById('sendBtn').disabled = false;
                            document.getElementById('sendBtn').textContent = '发送';
                            return;
                        }

                        // 解码数据块
                        const chunk = decoder.decode(value, { stream: true });
                        console.log('收到数据块:', chunk);

                        // 将新数据添加到缓冲区
                        buffer += chunk;

                        // 按双换行符分割事件
                        const events = buffer.split('\n\n');
                        buffer = events.pop() || ''; // 保留最后一个不完整的事件

                        for (const event of events) {
                            if (!event.trim()) continue;

                            const lines = event.split('\n');
                            let eventType = '';
                            let eventData = '';

                            for (const line of lines) {
                                if (line.startsWith('event:')) {
                                    eventType = line.substring(6).trim();
                                } else if (line.startsWith('data:')) {
                                    eventData = line.substring(5).trim();
                                }
                            }

                            console.log('事件类型:', eventType, '数据:', eventData);

                            if (eventData && eventData !== '') {
                                try {
                                    const data = JSON.parse(eventData);
                                    console.log('解析成功:', data);

                                    if (data.text) {
                                        // 直接追加文本
                                        fullResponse += data.text;
                                        contentElement.textContent = fullResponse;

                                        // 滚动到底部
                                        const messages = document.getElementById('messages');
                                        messages.scrollTop = messages.scrollHeight;
                                    }

                                    if (data.sessionId) {
                                        sessionId = data.sessionId;
                                    }
                                } catch (e) {
                                    console.error('JSON解析失败:', e, '数据:', eventData);
                                }
                            }
                        }

                        return readChunk();
                    });
                }

                return readChunk();
            }).catch(error => {
                console.error('流式对话失败:', error);
                contentElement.textContent = `❌ 流式对话失败: ${error.message}`;
                contentElement.style.background = '#ffebee';
                contentElement.style.color = '#c62828';

                isLoading = false;
                document.getElementById('sendBtn').disabled = false;
                document.getElementById('sendBtn').textContent = '发送';
            });
        }

        // 处理文档对话
        function handleDocumentChat(message, loadingId) {
            const appId = document.getElementById('appId').value.trim();

            if (!selectedFile) {
                removeMessage(loadingId);
                addMessage('assistant', '❌ 请先选择要分析的文档文件', false, true);
                isLoading = false;
                document.getElementById('sendBtn').disabled = false;
                document.getElementById('sendBtn').textContent = '发送';
                return;
            }

            // 创建FormData对象
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('appId', appId);
            formData.append('prompt', message);

            console.log('发送文档对话请求:', {
                fileName: selectedFile.name,
                fileSize: selectedFile.size,
                appId: appId,
                prompt: message
            });

            fetch(`${API_BASE}/api/bailian/file/process`, {
                method: 'POST',
                body: formData
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            }).then(data => {
                // 移除加载提示
                removeMessage(loadingId);

                if (data.code === 200) {
                    // 添加AI回复
                    addMessage('assistant', data.text);

                    // 更新会话ID
                    if (data.sessionId) {
                        sessionId = data.sessionId;
                    }
                } else {
                    addMessage('assistant', `❌ 文档处理失败: ${data.error || '未知错误'}`, false, true);
                }
            }).catch(error => {
                console.error('文档对话失败:', error);
                removeMessage(loadingId);
                addMessage('assistant', `❌ 文档对话失败: ${error.message}`, false, true);
            }).finally(() => {
                isLoading = false;
                document.getElementById('sendBtn').disabled = false;
                document.getElementById('sendBtn').textContent = '发送';
            });
        }

        // 测试流式对话
        function testStreamChat() {
            console.log('开始测试流式对话');

            // 添加测试消息
            const assistantMessageId = addMessage('assistant', '正在测试流式输出...');
            const assistantMessageElement = document.getElementById(assistantMessageId);
            const contentElement = assistantMessageElement.querySelector('.message-content');

            // 测试简单的流式输出
            fetch(`${API_BASE}/api/bailian/chat/stream/test`, {
                method: 'GET',
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                }
            }).then(response => {
                console.log('测试响应:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                contentElement.textContent = '';

                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');

                let buffer = '';

                function readTestChunk() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            console.log('测试流式响应完成');
                            return;
                        }

                        const chunk = decoder.decode(value, { stream: true });
                        console.log('测试数据块:', chunk);

                        // 将新数据添加到缓冲区
                        buffer += chunk;

                        // 按双换行符分割事件
                        const events = buffer.split('\n\n');
                        buffer = events.pop() || ''; // 保留最后一个不完整的事件

                        for (const event of events) {
                            if (!event.trim()) continue;

                            const lines = event.split('\n');
                            let eventType = '';
                            let eventData = '';

                            for (const line of lines) {
                                if (line.startsWith('event:')) {
                                    eventType = line.substring(6).trim();
                                } else if (line.startsWith('data:')) {
                                    eventData = line.substring(5).trim();
                                }
                            }

                            console.log('事件类型:', eventType, '数据:', eventData);

                            if (eventData && eventData !== '') {
                                try {
                                    const data = JSON.parse(eventData);
                                    console.log('测试解析成功:', data);

                                    if (data.text) {
                                        contentElement.textContent += data.text;

                                        // 滚动到底部
                                        const messages = document.getElementById('messages');
                                        messages.scrollTop = messages.scrollHeight;
                                    }
                                } catch (e) {
                                    console.error('测试JSON解析失败:', e, '数据:', eventData);
                                }
                            }
                        }

                        return readTestChunk();
                    });
                }

                return readTestChunk();
            }).catch(error => {
                console.error('测试流式对话失败:', error);
                contentElement.textContent = `❌ 测试失败: ${error.message}`;
            });
        }
    </script>
</body>
</html>
