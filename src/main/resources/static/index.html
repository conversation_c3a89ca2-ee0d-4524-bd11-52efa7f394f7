<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能客户端API系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .header {
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #333;
            font-size: 32px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
            margin: 0;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        
        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 14px;
        }
        
        /* 身份证识别模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 28px;
            cursor: pointer;
            color: #999;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background-color: #f0f0f0;
            color: #333;
        }

        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #5a67d8;
            background-color: #f7fafc;
        }

        .upload-area.dragover {
            border-color: #5a67d8;
            background-color: #edf2f7;
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #667eea;
        }

        .upload-text {
            color: #666;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .preview-section {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }

        .recognize-btn {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recognize-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
        }

        .recognize-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            display: none;
            margin-top: 20px;
        }

        .result-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }

        .result-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: #666;
        }

        .info-value {
            color: #333;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .header h1 {
                font-size: 24px;
            }

            .features {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能客户端API系统</h1>
            <p>集成多种AI服务的统一客户端平台</p>
        </div>
        
        <div class="features">
            <div class="feature-card" onclick="navigateToPage('simple-chat.html')">
                <div class="feature-icon">🧠</div>
                <div class="feature-title">阿里百炼智能体</div>
                <div class="feature-desc">
                    支持普通对话、流式响应、多轮对话、知识库检索和文件处理
                </div>
            </div>

            <div class="feature-card" onclick="navigateToPage('qwen-simple.html')">
                <div class="feature-icon">💬</div>
                <div class="feature-title">通义千问</div>
                <div class="feature-desc">
                    阿里云通义千问大语言模型，支持多种对话模式
                </div>
            </div>

            <div class="feature-card" onclick="navigateToPage('deepseek-simple.html')">
                <div class="feature-icon">🔍</div>
                <div class="feature-title">DeepSeek</div>
                <div class="feature-desc">
                    DeepSeek AI模型，专注于代码生成和技术问答
                </div>
            </div>
            
            <div class="feature-card" onclick="showIdCardOCR()">
                <div class="feature-icon">🆔</div>
                <div class="feature-title">身份证识别</div>
                <div class="feature-desc">
                    基于PaddleOCR的身份证智能识别，支持正面背面自动识别
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📚</div>
                <div class="feature-title">资源下载</div>
                <div class="feature-desc">
                    状元网资源下载管理，支持试卷、同步资源等
                </div>
            </div>
            
            <div class="feature-card" onclick="navigateToPage('word-match.html')">
                <div class="feature-icon">🎮</div>
                <div class="feature-title">单词消消乐</div>
                <div class="feature-desc">
                    趣味单词匹配游戏，支持多种难度级别和计时挑战
                </div>
            </div>

            <div class="feature-card" onclick="navigateToPage('decibel-monitor.html')">
                <div class="feature-icon">📊</div>
                <div class="feature-title">课堂分贝检测</div>
                <div class="feature-desc">
                    实时检测课堂音量分贝，激励学生积极朗读，提供可视化反馈
                </div>
            </div>
        </div>
        
<!--        <div class="actions">-->
<!--            <a href="simple-chat.html" class="action-btn btn-primary">-->
<!--                🚀 开始使用百炼智能体-->
<!--            </a>-->
<!--            <a href="/doc.html" class="action-btn btn-secondary">-->
<!--                📖 查看API文档-->
<!--            </a>-->
<!--        </div>-->
        
        <div class="footer">
            <p>© 2025 智能客户端API系统 | 基于Spring Boot + Vue.js构建</p>
            <p>支持的AI服务：阿里百炼、通义千问、DeepSeek</p>
        </div>
    </div>

    <!-- 身份证识别模态框 -->
    <div id="idCardModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">🆔 身份证识别</h2>
                <button class="close-btn" onclick="closeIdCardModal()">&times;</button>
            </div>

            <!-- 上传区域 -->
            <div class="upload-area" id="uploadArea" onclick="document.getElementById('idCardFileInput').click()">
                <div class="upload-icon">📷</div>
                <div class="upload-text">
                    <p>拖拽身份证图片到此处，或点击选择文件</p>
                    <p style="font-size: 14px; color: #999; margin-top: 10px;">
                        支持 JPG、PNG、BMP 格式，文件大小不超过 10MB
                    </p>
                </div>
                <input type="file" id="idCardFileInput" class="file-input" accept="image/*">
                <button class="upload-btn" type="button">选择身份证图片</button>
            </div>

            <!-- 图片预览 -->
            <div class="preview-section" id="previewSection">
                <img id="previewImage" class="preview-image" alt="预览图片">
                <br>
                <button class="recognize-btn" id="recognizeBtn" onclick="recognizeIdCard()">
                    🔍 开始识别
                </button>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loadingSection">
                <div class="spinner"></div>
                <p>正在识别身份证信息，请稍候...</p>
            </div>

            <!-- 结果展示 -->
            <div class="result-section" id="resultSection">
                <div class="result-card">
                    <div class="result-title">识别结果</div>
                    <div id="resultContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面导航函数，解决跨域问题
        function navigateToPage(pageName) {
            // 获取当前页面的协议、主机和端口
            const currentOrigin = window.location.origin;
            const currentPath = window.location.pathname;

            // 构建目标URL，保持相同的协议、主机和端口
            let targetUrl;
            if (currentPath.endsWith('/') || currentPath.endsWith('/index.html')) {
                // 如果当前在根目录或index.html，直接跳转到目标页面
                targetUrl = currentOrigin + '/' + pageName;
            } else {
                // 如果在其他路径，需要回到根目录再跳转
                const basePath = currentPath.substring(0, currentPath.lastIndexOf('/') + 1);
                targetUrl = currentOrigin + basePath + pageName;
            }

            console.log('导航到:', targetUrl);
            window.location.href = targetUrl;
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为特性卡片添加点击效果
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    // 添加点击动画
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
            
            // 检查服务状态
            checkServiceStatus();
        });
        
        async function checkServiceStatus() {
            try {
                const response = await fetch('/actuator/health');
                if (response.ok) {
                    console.log('服务状态正常');
                }
            } catch (error) {
                console.log('服务状态检查失败:', error);
            }
        }

        // 身份证识别相关功能
        let selectedFile = null;

        function showIdCardOCR() {
            document.getElementById('idCardModal').style.display = 'block';
            resetIdCardModal();
        }

        function closeIdCardModal() {
            document.getElementById('idCardModal').style.display = 'none';
            resetIdCardModal();
        }

        function resetIdCardModal() {
            selectedFile = null;
            document.getElementById('previewSection').style.display = 'none';
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('resultSection').style.display = 'none';
            document.getElementById('idCardFileInput').value = '';
        }

        // 文件选择处理
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('idCardFileInput');
            const uploadArea = document.getElementById('uploadArea');

            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelect);
            }

            if (uploadArea) {
                // 拖拽功能
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        handleFile(files[0]);
                    }
                });
            }
        });

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件');
                return;
            }

            // 验证文件大小 (10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('文件大小不能超过10MB');
                return;
            }

            selectedFile = file;

            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewImage = document.getElementById('previewImage');
                previewImage.src = e.target.result;
                document.getElementById('previewSection').style.display = 'block';
            };
            reader.readAsDataURL(file);
        }

        async function recognizeIdCard() {
            if (!selectedFile) {
                alert('请先选择身份证图片');
                return;
            }

            const recognizeBtn = document.getElementById('recognizeBtn');
            const loadingSection = document.getElementById('loadingSection');

            // 显示加载状态
            recognizeBtn.disabled = true;
            recognizeBtn.textContent = '识别中...';
            loadingSection.style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';

            try {
                const formData = new FormData();
                formData.append('image', selectedFile);

                const response = await fetch('/api/ocr/idcard/recognize', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayIdCardResult(result);
                } else {
                    displayError(result.message || '识别失败');
                }

            } catch (error) {
                console.error('识别错误:', error);
                displayError('识别过程中发生错误：' + error.message);
            } finally {
                // 恢复按钮状态
                recognizeBtn.disabled = false;
                recognizeBtn.textContent = '🔍 开始识别';
                loadingSection.style.display = 'none';
            }
        }

        function displayIdCardResult(result) {
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');

            resultSection.style.display = 'block';

            let html = '';

            if (result.structured_info) {
                const info = result.structured_info;

                // 基本信息
                if (info.name || info.id_number || info.gender) {
                    html += '<div class="result-title">基本信息</div>';
                    if (info.name) html += `<div class="info-item"><span class="info-label">姓名</span><span class="info-value">${info.name}</span></div>`;
                    if (info.id_number) html += `<div class="info-item"><span class="info-label">身份证号</span><span class="info-value">${info.id_number}</span></div>`;
                    if (info.gender) html += `<div class="info-item"><span class="info-label">性别</span><span class="info-value">${info.gender}</span></div>`;
                    if (info.nation) html += `<div class="info-item"><span class="info-label">民族</span><span class="info-value">${info.nation}</span></div>`;
                    if (info.birth_date) html += `<div class="info-item"><span class="info-label">出生日期</span><span class="info-value">${info.birth_date}</span></div>`;
                    if (info.age) html += `<div class="info-item"><span class="info-label">年龄</span><span class="info-value">${info.age}岁</span></div>`;
                }

                // 地址信息
                if (info.address) {
                    html += '<div class="result-title" style="margin-top: 20px;">地址信息</div>';
                    html += `<div class="info-item"><span class="info-label">住址</span><span class="info-value">${info.address}</span></div>`;
                }

                // 证件信息
                if (info.issuing_authority || info.valid_period) {
                    html += '<div class="result-title" style="margin-top: 20px;">证件信息</div>';
                    if (info.issuing_authority) html += `<div class="info-item"><span class="info-label">签发机关</span><span class="info-value">${info.issuing_authority}</span></div>`;
                    if (info.valid_period) html += `<div class="info-item"><span class="info-label">有效期限</span><span class="info-value">${info.valid_period}</span></div>`;
                }

                // 验证信息
                html += '<div class="result-title" style="margin-top: 20px;">验证信息</div>';
                html += `<div class="info-item"><span class="info-label">证件类型</span><span class="info-value">${getCardTypeName(result.card_type)}</span></div>`;
                if (info.id_valid !== undefined) html += `<div class="info-item"><span class="info-label">号码格式</span><span class="info-value">${info.id_valid ? '✅ 有效' : '❌ 无效'}</span></div>`;
                if (result.confidence) html += `<div class="info-item"><span class="info-label">识别置信度</span><span class="info-value">${result.confidence.toFixed(1)}%</span></div>`;
            } else {
                // 显示原始文本
                html += '<div class="result-title">识别结果</div>';
                if (result.raw_texts && result.raw_texts.length > 0) {
                    result.raw_texts.forEach(text => {
                        html += `<div style="padding: 5px 0; border-bottom: 1px solid #eee;">${text}</div>`;
                    });
                } else {
                    html += '<div style="color: #999;">未识别到有效信息</div>';
                }
            }

            resultContent.innerHTML = html;
        }

        function displayError(message) {
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');

            resultSection.style.display = 'block';
            resultContent.innerHTML = `
                <div class="result-title">识别失败</div>
                <div style="color: #e53e3e; padding: 10px; background: #fed7d7; border-radius: 5px;">
                    ${message}
                </div>
            `;
        }

        function getCardTypeName(cardType) {
            switch(cardType) {
                case 'front': return '身份证正面';
                case 'back': return '身份证背面';
                default: return '身份证';
            }
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('idCardModal');
            if (event.target === modal) {
                closeIdCardModal();
            }
        });
    </script>
</body>
</html>
