<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通义千问</title>

    <!-- KaTeX数学公式渲染 - 使用国内CDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.16.8/katex.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.16.8/katex.min.js"></script>

    <!-- 通用Markdown解析器 -->
    <link rel="stylesheet" href="css/markdown-styles.css">
    <script src="js/katex-fallback.js"></script>
    <script src="js/markdown-parser.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-area {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .message.user .message-avatar {
            background: #667eea;
        }

        .message.assistant .message-avatar {
            background: #764ba2;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        /* 为AI回复添加markdown-content类 */
        .message.assistant .message-content {
            /* 继承markdown-content样式 */
        }

        .message.user .message-content {
            background: #667eea;
            color: white;
        }

        .message.assistant .message-content {
            background: #f1f3f4;
            color: #333;
        }

        .input-area {
            padding: 20px;
            display: flex;
            gap: 12px;
        }

        .input-area input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 24px;
            outline: none;
            font-size: 14px;
        }

        .input-area input:focus {
            border-color: #667eea;
        }

        .input-area button {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 24px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .input-area button:hover {
            background: #5a6fd8;
        }

        .input-area button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 12px 16px;
            margin: 16px;
            border-radius: 8px;
            border-left: 4px solid #c62828;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        /* Markdown样式 */
        .message-content h1, .message-content h2, .message-content h3,
        .message-content h4, .message-content h5, .message-content h6 {
            margin: 12px 0 8px 0;
            font-weight: bold;
            line-height: 1.4;
        }

        .message-content h1 { font-size: 1.4em; color: #2c3e50; }
        .message-content h2 { font-size: 1.3em; color: #34495e; }
        .message-content h3 { font-size: 1.2em; color: #34495e; }
        .message-content h4 { font-size: 1.1em; color: #5d6d7e; }
        .message-content h5 { font-size: 1.05em; color: #5d6d7e; }
        .message-content h6 { font-size: 1.0em; color: #7b8a8b; }

        .message-content code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .message-content pre {
            background: rgba(0, 0, 0, 0.05);
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 8px 0;
        }

        .message-content pre code {
            background: none;
            padding: 0;
        }

        .message-content strong {
            font-weight: bold;
        }

        .message-content em {
            font-style: italic;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
        }

        /* 模式选择器样式 */
        .mode-selector {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
        }

        .selector-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mode-selector label {
            font-weight: bold;
            white-space: nowrap;
        }

        .mode-selector select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            min-width: 150px;
        }

        .model-description {
            padding: 12px 20px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            font-size: 14px;
            color: #1565c0;
            border-bottom: 1px solid #eee;
        }

        @media (max-width: 600px) {
            .mode-selector {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .selector-group {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .mode-selector select {
                min-width: auto;
                width: 100%;
            }
        }

        /* 文件上传样式 */
        .file-upload-area {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }

        .file-upload-zone {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 30px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .file-upload-zone:hover {
            border-color: #667eea;
            background: #f0f8ff;
        }

        .file-upload-zone.dragover {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .file-upload-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }

        .file-upload-text {
            color: #666;
        }

        .file-upload-hint {
            font-size: 0.8em;
            color: #999;
            margin-top: 6px;
        }

        .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 12px 16px;
            margin-top: 12px;
        }

        .file-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .clear-file-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .clear-file-btn:hover {
            background: #c82333;
        }

        /* 流式输出动画 */
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
            animation: typing 1.4s infinite ease-in-out;
            margin-left: 4px;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 通义千问</h1>
            <p>支持多种对话模式的智能助手</p>
        </div>

        <!-- 对话模式和模型选择 -->
        <div class="mode-selector">
            <div class="selector-group">
                <label for="chatMode">对话模式：</label>
                <select id="chatMode" onchange="toggleFileUpload()">
                    <option value="simple">简单对话</option>
                    <option value="multi-turn">多轮对话</option>
                    <option value="stream">流式输出</option>
                    <option value="document">文档分析</option>
                </select>
            </div>
            <div class="selector-group">
                <label for="modelSelect">选择模型：</label>
                <select id="modelSelect" onchange="updateModelDescription()">
                    <option value="qwen-turbo">通义千问-Turbo</option>
                    <option value="qwen-plus">通义千问-Plus</option>
                    <option value="qwen-max">通义千问-Max</option>
                    <option value="qwen-max-longcontext">通义千问-Max-长文本</option>
                    <option value="qwen-vl-plus">通义千问-视觉Plus</option>
                    <option value="qwen-vl-max">通义千问-视觉Max</option>
                    <option value="qwen-audio-turbo">通义千问-音频Turbo</option>
                    <option value="qwen-coder-turbo">通义千问-代码Turbo</option>
                </select>
            </div>
        </div>

        <!-- 模型描述 -->
        <div id="modelDescription" class="model-description">
            <strong>通义千问-Turbo：</strong>速度快，适合日常对话和简单任务
        </div>

        <!-- 文件上传区域 -->
        <div id="fileUploadArea" class="file-upload-area" style="display: none;">
            <div class="file-upload-zone" onclick="document.getElementById('fileInput').click()">
                <div class="file-upload-icon">📁</div>
                <div class="file-upload-text">
                    <div>点击选择文件或拖拽文件到此处</div>
                    <div class="file-upload-hint">支持图片、PDF、Word、Excel、PPT等文件，最大20MB</div>
                </div>
                <input type="file" id="fileInput" accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.md,.json,.csv,.xml,.html,.jpg,.jpeg,.png,.gif,.bmp,.webp,.svg" style="display: none;" onchange="handleFileSelect(event)">
            </div>
            <div id="fileInfo" class="file-info" style="display: none;">
                <div class="file-details">
                    <span id="fileName"></span>
                    <span id="fileSize"></span>
                </div>
                <button onclick="clearFile()" class="clear-file-btn">✕</button>
            </div>
        </div>
        
        <div id="chatArea" class="chat-area">
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    您好！我是通义千问AI助手。有什么可以帮助您的吗？
                </div>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="输入您的问题..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()" id="sendBtn">发送</button>
            <button onclick="clearHistory()" id="clearBtn">清除历史</button>
        </div>
    </div>

    <script>
        let isLoading = false;
        let sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        let selectedFile = null;
        let conversationHistory = [];

        // 使用通用Markdown解析器（已在markdown-parser.js中定义）

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 切换文件上传区域显示
        function toggleFileUpload() {
            const chatMode = document.getElementById('chatMode').value;
            const fileUploadArea = document.getElementById('fileUploadArea');

            if (chatMode === 'document') {
                fileUploadArea.style.display = 'block';
            } else {
                fileUploadArea.style.display = 'none';
                clearFile();
            }
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 20 * 1024 * 1024) {
                    alert('文件大小不能超过20MB');
                    return;
                }
                selectedFile = file;
                showFileInfo(file);
            }
        }

        // 显示文件信息
        function showFileInfo(file) {
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');

            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'flex';
        }

        // 清除文件
        function clearFile() {
            selectedFile = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('fileInfo').style.display = 'none';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 获取对话历史
        function getConversationHistory() {
            return conversationHistory.slice(-10); // 最近10轮对话
        }

        // 更新对话历史
        function updateConversationHistory(userMessage, assistantMessage) {
            conversationHistory.push({
                role: 'user',
                content: userMessage
            });
            conversationHistory.push({
                role: 'assistant',
                content: assistantMessage
            });
        }

        // 更新模型描述
        function updateModelDescription() {
            const modelSelect = document.getElementById('modelSelect');
            const modelDescription = document.getElementById('modelDescription');
            const selectedModel = modelSelect.value;

            const descriptions = {
                'qwen-turbo': '<strong>通义千问-Turbo：</strong>速度快，适合日常对话和简单任务',
                'qwen-plus': '<strong>通义千问-Plus：</strong>平衡性能和速度，适合复杂推理任务',
                'qwen-max': '<strong>通义千问-Max：</strong>最强性能，适合专业分析和创作任务',
                'qwen-max-longcontext': '<strong>通义千问-Max-长文本：</strong>支持超长文本处理，适合文档分析',
                'qwen-vl-plus': '<strong>通义千问-视觉Plus：</strong>支持图像理解，适合图片分析',
                'qwen-vl-max': '<strong>通义千问-视觉Max：</strong>最强视觉理解能力，适合复杂图像任务',
                'qwen-audio-turbo': '<strong>通义千问-音频Turbo：</strong>支持音频处理，适合语音任务',
                'qwen-coder-turbo': '<strong>通义千问-代码Turbo：</strong>专业代码生成和分析，适合编程任务'
            };

            modelDescription.innerHTML = descriptions[selectedModel] || descriptions['qwen-turbo'];
        }

        function addMessage(content, isUser = false) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = isUser ? '我' : 'AI';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = isUser ? 'message-content' : 'message-content markdown-content';

            if (isUser) {
                contentDiv.textContent = content;
            } else {
                contentDiv.innerHTML = parseMarkdown(content);
            }
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            chatArea.appendChild(messageDiv);
            
            // 滚动到底部
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const chatMode = document.getElementById('chatMode').value;
            const selectedModel = document.getElementById('modelSelect').value;
            const message = input.value.trim();

            if (!message || isLoading) return;

            // 添加用户消息
            addMessage(message, true);
            input.value = '';

            // 设置加载状态
            isLoading = true;
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';

            try {
                let response, data;

                switch (chatMode) {
                    case 'stream':
                        await handleStreamChat(message);
                        return;

                    case 'multi-turn':
                        response = await fetch('/api/bailian/chat/multi-turn', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                appId: '3267ecc2febd4c37b0baf9a08957daaa',
                                model: selectedModel,
                                messages: getConversationHistory().concat([{
                                    role: 'user',
                                    content: message
                                }]),
                                sessionId: sessionId
                            })
                        });
                        break;

                    case 'document':
                        if (!selectedFile) {
                            addMessage('❌ 请先选择要分析的文档文件');
                            return;
                        }
                        await handleDocumentChat(message);
                        return;

                    default: // simple
                        response = await fetch('/api/bailian/chat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                appId: '3267ecc2febd4c37b0baf9a08957daaa',
                                model: selectedModel,
                                prompt: message,
                                sessionId: sessionId
                            })
                        });
                        break;
                }

                data = await response.json();

                if (response.ok) {
                    const assistantMessage = data.text || data.content || '收到回复';
                    addMessage(assistantMessage);

                    // 更新对话历史（多轮对话模式）
                    if (chatMode === 'multi-turn') {
                        updateConversationHistory(message, assistantMessage);
                    }

                    // 更新会话ID
                    if (data.sessionId) {
                        sessionId = data.sessionId;
                    }
                } else {
                    addMessage(`❌ 请求失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('请求失败:', error);
                addMessage(`❌ 网络错误: ${error.message}`);
            } finally {
                isLoading = false;
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
            }
        }

        // 处理流式对话
        async function handleStreamChat(message) {
            const selectedModel = document.getElementById('modelSelect').value;
            const assistantMessageId = addMessage('正在思考...');
            const assistantMessageElement = document.querySelector(`[data-message-id="${assistantMessageId}"]`);
            const contentElement = assistantMessageElement.querySelector('.message-content');

            let fullResponse = '';

            try {
                const response = await fetch('/api/bailian/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        appId: '3267ecc2febd4c37b0baf9a08957daaa',
                        model: selectedModel,
                        prompt: message,
                        sessionId: sessionId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                contentElement.textContent = '';

                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();

                    if (done) {
                        // 流式完成后进行Markdown渲染
                        if (fullResponse) {
                            contentElement.innerHTML = parseMarkdown(fullResponse);
                        }
                        break;
                    }

                    buffer += decoder.decode(value, { stream: true });
                    const events = buffer.split('\n\n');
                    buffer = events.pop() || '';

                    for (const event of events) {
                        if (!event.trim()) continue;

                        const lines = event.split('\n');
                        let eventData = '';

                        for (const line of lines) {
                            if (line.startsWith('data:')) {
                                eventData = line.substring(5).trim();
                            }
                        }

                        if (eventData && eventData !== '') {
                            try {
                                const data = JSON.parse(eventData);
                                if (data.text) {
                                    fullResponse += data.text;
                                    contentElement.textContent = fullResponse;

                                    // 滚动到底部
                                    const chatArea = document.getElementById('chatArea');
                                    chatArea.scrollTop = chatArea.scrollHeight;
                                }

                                if (data.sessionId) {
                                    sessionId = data.sessionId;
                                }
                            } catch (e) {
                                console.error('解析SSE数据失败:', e);
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('流式对话失败:', error);
                contentElement.textContent = `❌ 流式对话失败: ${error.message}`;
            }
        }

        // 处理文档分析
        async function handleDocumentChat(message) {
            const selectedModel = document.getElementById('modelSelect').value;
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('appId', '3267ecc2febd4c37b0baf9a08957daaa');
            formData.append('model', selectedModel);
            formData.append('prompt', message);

            try {
                const response = await fetch('/api/bailian/file/process', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    addMessage(data.text || '文档分析完成');
                    if (data.sessionId) {
                        sessionId = data.sessionId;
                    }
                } else {
                    addMessage(`❌ 文档分析失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('文档分析失败:', error);
                addMessage(`❌ 文档分析失败: ${error.message}`);
            }
        }

        // 修改addMessage函数，支持消息ID
        function addMessage(content, isUser = false) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            messageDiv.setAttribute('data-message-id', messageId);

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = isUser ? '我' : 'AI';

            const contentDiv = document.createElement('div');
            contentDiv.className = isUser ? 'message-content' : 'message-content markdown-content';

            if (isUser) {
                contentDiv.textContent = content;
            } else {
                contentDiv.innerHTML = parseMarkdown(content);
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            chatArea.appendChild(messageDiv);

            // 滚动到底部
            chatArea.scrollTop = chatArea.scrollHeight;

            return messageId;
        }

        // 清除对话历史
        function clearHistory() {
            const chatArea = document.getElementById('chatArea');
            chatArea.innerHTML = `
                <div class="message assistant">
                    <div class="message-avatar">AI</div>
                    <div class="message-content">
                        您好！我是通义千问AI助手。有什么可以帮助您的吗？
                    </div>
                </div>
            `;
            conversationHistory = [];
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 设置文件拖拽功能
        function setupFileDragAndDrop() {
            const uploadZone = document.querySelector('.file-upload-zone');
            if (!uploadZone) return;

            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadZone.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadZone.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadZone.addEventListener(eventName, unhighlight, false);
            });

            function highlight(e) {
                uploadZone.classList.add('dragover');
            }

            function unhighlight(e) {
                uploadZone.classList.remove('dragover');
            }

            uploadZone.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                if (files.length > 0) {
                    const file = files[0];
                    document.getElementById('fileInput').files = files;
                    handleFileSelect({ target: { files: [file] } });
                }
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            setupFileDragAndDrop();
            updateModelDescription(); // 初始化模型描述
        });
    </script>
</body>
</html>
