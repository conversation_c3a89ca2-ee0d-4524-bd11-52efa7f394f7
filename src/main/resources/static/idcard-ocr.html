<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份证OCR识别 - gk-client-api</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #667eea;
            background: #f0f2ff;
        }

        .upload-section.dragover {
            border-color: #667eea;
            background: #e3f2fd;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .card-type-selector {
            margin: 20px 0;
        }

        .card-type-selector label {
            margin-right: 20px;
            font-weight: 500;
        }

        .card-type-selector input[type="radio"] {
            margin-right: 5px;
        }

        .preview-section {
            display: none;
            margin: 30px 0;
            text-align: center;
        }

        .preview-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .recognize-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .recognize-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }

        .recognize-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 30px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            display: none;
            margin-top: 30px;
        }

        .result-tabs {
            display: flex;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 15px 25px;
            font-size: 1.1em;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
            font-weight: 600;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .info-card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
        }

        .info-value {
            color: #212529;
            font-weight: 500;
        }

        .raw-text {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .confidence-bar {
            background: #dee2e6;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin-top: 10px;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
            transition: width 0.5s ease;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }

        .copy-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            margin-left: 10px;
        }

        .copy-btn:hover {
            background: #5a6268;
        }

        /* 进度条样式 */
        .progress-container {
            text-align: center;
            max-width: 500px;
            margin: 0 auto;
        }

        .progress-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .progress-header h3 {
            margin: 0 0 0 15px;
            color: #333;
            font-size: 1.2em;
        }

        .progress-bar-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            color: #666;
        }

        .steps-container {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
            padding: 0 20px;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }

        .step.active:not(:last-child)::after {
            background: #667eea;
        }

        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .step.active .step-icon {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .step.completed .step-icon {
            background: #28a745;
            color: white;
        }

        .step-text {
            font-size: 0.8em;
            color: #666;
            text-align: center;
            margin-top: 5px;
        }

        .step.active .step-text {
            color: #667eea;
            font-weight: 600;
        }

        .step.completed .step-text {
            color: #28a745;
        }

        #progressMessage {
            margin-top: 20px;
            color: #666;
            font-size: 0.95em;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .result-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🆔 身份证OCR识别</h1>
            <p>基于PaddleOCR的高精度身份证信息识别系统</p>
        </div>

        <div class="main-content">
            <!-- 上传区域 -->
            <div class="upload-section" id="uploadSection">
                <div class="upload-icon">📷</div>
                <div class="upload-text">
                    <p>拖拽身份证图片到此处，或点击按钮选择文件</p>
                    <p style="font-size: 0.9em; color: #999; margin-top: 10px;">
                        支持 JPG、PNG、BMP 格式，文件大小不超过 10MB
                    </p>
                </div>
                <input type="file" id="fileInput" class="file-input" accept="image/*">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择身份证图片
                </button>
                
                <div class="card-type-selector">
                    <label>识别类型：</label>
                    <label><input type="radio" name="cardType" value="auto" checked> 自动识别</label>
                    <label><input type="radio" name="cardType" value="front"> 正面</label>
                    <label><input type="radio" name="cardType" value="back"> 背面</label>
                </div>
            </div>

            <!-- 图片预览 -->
            <div class="preview-section" id="previewSection">
                <img id="previewImage" class="preview-image" alt="预览图片">
                <br>
                <button class="recognize-btn" id="recognizeBtn" onclick="recognizeIdCard()">
                    🔍 开始识别
                </button>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loadingSection">
                <div class="progress-container">
                    <div class="progress-header">
                        <div class="spinner"></div>
                        <h3 id="progressTitle">正在识别身份证</h3>
                    </div>

                    <!-- 进度条 -->
                    <div class="progress-bar-container">
                        <div class="progress-bar">
                            <div id="progressBar" class="progress-fill"></div>
                        </div>
                        <div class="progress-text">
                            <span id="progressPercent">0%</span>
                            <span id="progressTime">预计剩余时间: --</span>
                        </div>
                    </div>

                    <!-- 步骤指示器 -->
                    <div class="steps-container">
                        <div class="step" id="step1">
                            <div class="step-icon">📤</div>
                            <div class="step-text">上传图片</div>
                        </div>
                        <div class="step" id="step2">
                            <div class="step-icon">🔍</div>
                            <div class="step-text">识别文字</div>
                        </div>
                        <div class="step" id="step3">
                            <div class="step-icon">📝</div>
                            <div class="step-text">解析信息</div>
                        </div>
                        <div class="step" id="step4">
                            <div class="step-icon">✅</div>
                            <div class="step-text">完成识别</div>
                        </div>
                    </div>

                    <p id="progressMessage">正在准备识别...</p>
                </div>
            </div>

            <!-- 结果展示 -->
            <div class="result-section" id="resultSection">
                <div class="result-tabs">
                    <button class="tab-btn active" onclick="showTab('structured')">结构化信息</button>
                    <button class="tab-btn" onclick="showTab('raw')">原始文本</button>
                    <button class="tab-btn" onclick="showTab('details')">详细信息</button>
                </div>

                <div id="structuredTab" class="tab-content active">
                    <div class="info-grid" id="infoGrid">
                        <!-- 动态生成结构化信息 -->
                    </div>
                </div>

                <div id="rawTab" class="tab-content">
                    <h3>原始识别文本</h3>
                    <div class="raw-text" id="rawText"></div>
                </div>

                <div id="detailsTab" class="tab-content">
                    <div class="info-card">
                        <h3>识别详情</h3>
                        <div id="detailsInfo"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFile = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupFileUpload();
            setupDragAndDrop();
        });

        // 设置文件上传
        function setupFileUpload() {
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', handleFileSelect);
        }

        // 设置拖拽上传
        function setupDragAndDrop() {
            const uploadSection = document.getElementById('uploadSection');
            
            uploadSection.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadSection.classList.add('dragover');
            });
            
            uploadSection.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadSection.classList.remove('dragover');
            });
            
            uploadSection.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadSection.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        // 处理文件
        function handleFile(file) {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showError('请选择图片文件');
                return;
            }
            
            // 验证文件大小
            if (file.size > 10 * 1024 * 1024) {
                showError('文件大小不能超过10MB');
                return;
            }
            
            selectedFile = file;
            
            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewImage = document.getElementById('previewImage');
                previewImage.src = e.target.result;
                document.getElementById('previewSection').style.display = 'block';
                document.getElementById('resultSection').style.display = 'none';
            };
            reader.readAsDataURL(file);
        }

        // 进度控制变量
        let progressTimer = null;
        let startTime = null;

        // 识别身份证
        async function recognizeIdCard() {
            if (!selectedFile) {
                showError('请先选择身份证图片');
                return;
            }

            const recognizeBtn = document.getElementById('recognizeBtn');
            const loadingSection = document.getElementById('loadingSection');

            // 显示加载状态
            recognizeBtn.disabled = true;
            recognizeBtn.textContent = '识别中...';
            loadingSection.style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';

            // 开始进度控制
            startTime = Date.now();
            startProgressSimulation();

            try {
                // 步骤1：准备上传
                updateProgress(1, 10, '正在准备上传图片...');
                await sleep(200);

                const formData = new FormData();
                formData.append('image', selectedFile);

                // 步骤2：上传图片
                updateProgress(1, 25, '正在上传图片...');
                await sleep(300);

                // 获取选择的识别类型
                const cardType = document.querySelector('input[name="cardType"]:checked').value;
                let apiUrl = '/api/ocr/idcard/recognize';

                if (cardType === 'front') {
                    apiUrl = '/api/ocr/idcard/recognize/front';
                } else if (cardType === 'back') {
                    apiUrl = '/api/ocr/idcard/recognize/back';
                }

                // 步骤3：开始识别
                updateProgress(2, 40, '正在识别文字内容...');

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    body: formData
                });

                // 步骤4：解析结果
                updateProgress(3, 80, '正在解析识别结果...');
                await sleep(200);

                const result = await response.json();

                // 步骤5：完成
                updateProgress(4, 100, '识别完成！');
                await sleep(300);

                if (result.success) {
                    showSuccess('身份证识别成功！');
                    displayResult(result);
                } else {
                    showError('识别失败：' + result.message);
                }

            } catch (error) {
                console.error('识别错误:', error);
                showError('识别过程中发生错误：' + error.message);
            } finally {
                // 停止进度控制
                stopProgressSimulation();

                // 恢复按钮状态
                recognizeBtn.disabled = false;
                recognizeBtn.textContent = '🔍 开始识别';
                loadingSection.style.display = 'none';
            }
        }

        // 更新进度
        function updateProgress(step, percent, message) {
            // 更新进度条
            const progressBar = document.getElementById('progressBar');
            const progressPercent = document.getElementById('progressPercent');
            const progressMessage = document.getElementById('progressMessage');
            const progressTitle = document.getElementById('progressTitle');

            progressBar.style.width = percent + '%';
            progressPercent.textContent = percent + '%';
            progressMessage.textContent = message;

            // 更新步骤状态
            for (let i = 1; i <= 4; i++) {
                const stepElement = document.getElementById('step' + i);
                stepElement.classList.remove('active', 'completed');

                if (i < step) {
                    stepElement.classList.add('completed');
                } else if (i === step) {
                    stepElement.classList.add('active');
                }
            }

            // 更新预计剩余时间
            updateEstimatedTime(percent);
        }

        // 更新预计剩余时间
        function updateEstimatedTime(percent) {
            if (!startTime || percent <= 0) return;

            const elapsed = Date.now() - startTime;
            const estimated = (elapsed / percent) * (100 - percent);
            const remainingSeconds = Math.ceil(estimated / 1000);

            const progressTime = document.getElementById('progressTime');
            if (remainingSeconds > 0 && percent < 100) {
                progressTime.textContent = `预计剩余时间: ${remainingSeconds}秒`;
            } else {
                progressTime.textContent = '即将完成...';
            }
        }

        // 开始进度模拟
        function startProgressSimulation() {
            // 这里可以添加一些平滑的进度动画
        }

        // 停止进度模拟
        function stopProgressSimulation() {
            if (progressTimer) {
                clearInterval(progressTimer);
                progressTimer = null;
            }
        }

        // 辅助函数：延迟
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 显示结果
        function displayResult(result) {
            document.getElementById('resultSection').style.display = 'block';
            
            // 显示结构化信息
            displayStructuredInfo(result.structured_info, result.card_type, result.confidence);
            
            // 显示原始文本
            displayRawText(result.raw_texts);
            
            // 显示详细信息
            displayDetails(result);
        }

        // 显示结构化信息
        function displayStructuredInfo(structuredInfo, cardType, confidence) {
            const infoGrid = document.getElementById('infoGrid');
            infoGrid.innerHTML = '';
            
            // 基本信息卡片
            if (structuredInfo.name || structuredInfo.id_number || structuredInfo.gender) {
                const basicCard = createInfoCard('基本信息', [
                    { label: '姓名', value: structuredInfo.name },
                    { label: '身份证号', value: structuredInfo.id_number },
                    { label: '性别', value: structuredInfo.gender },
                    { label: '民族', value: structuredInfo.nation },
                    { label: '出生日期', value: structuredInfo.birth_date },
                    { label: '年龄', value: structuredInfo.age ? structuredInfo.age + '岁' : null }
                ]);
                infoGrid.appendChild(basicCard);
            }
            
            // 地址信息卡片
            if (structuredInfo.address) {
                const addressCard = createInfoCard('地址信息', [
                    { label: '住址', value: structuredInfo.address },
                    { label: '省份代码', value: structuredInfo.province_code },
                    { label: '城市代码', value: structuredInfo.city_code },
                    { label: '区县代码', value: structuredInfo.district_code }
                ]);
                infoGrid.appendChild(addressCard);
            }
            
            // 证件信息卡片
            if (structuredInfo.issuing_authority || structuredInfo.valid_period) {
                const certCard = createInfoCard('证件信息', [
                    { label: '签发机关', value: structuredInfo.issuing_authority },
                    { label: '有效期限', value: structuredInfo.valid_period },
                    { label: '有效期开始', value: structuredInfo.valid_from },
                    { label: '有效期结束', value: structuredInfo.valid_to }
                ]);
                infoGrid.appendChild(certCard);
            }
            
            // 验证信息卡片
            const validationCard = createInfoCard('验证信息', [
                { label: '证件类型', value: getCardTypeName(cardType) },
                { label: '号码格式', value: structuredInfo.id_valid ? '✅ 有效' : '❌ 无效' },
                { label: '性别一致性', value: structuredInfo.gender_consistent ? '✅ 一致' : '❌ 不一致' },
                { label: '识别置信度', value: confidence ? confidence.toFixed(1) + '%' : '未知' }
            ]);
            infoGrid.appendChild(validationCard);
            
            // 添加置信度条
            if (confidence) {
                const confidenceBar = document.createElement('div');
                confidenceBar.className = 'confidence-bar';
                confidenceBar.innerHTML = `<div class="confidence-fill" style="width: ${confidence}%"></div>`;
                validationCard.appendChild(confidenceBar);
            }
        }

        // 创建信息卡片
        function createInfoCard(title, items) {
            const card = document.createElement('div');
            card.className = 'info-card';
            
            const titleElement = document.createElement('h3');
            titleElement.textContent = title;
            card.appendChild(titleElement);
            
            items.forEach(item => {
                if (item.value !== null && item.value !== undefined && item.value !== '') {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'info-item';
                    itemElement.innerHTML = `
                        <span class="info-label">${item.label}:</span>
                        <span class="info-value">${item.value}</span>
                    `;
                    card.appendChild(itemElement);
                }
            });
            
            return card;
        }

        // 显示原始文本
        function displayRawText(rawTexts) {
            const rawTextElement = document.getElementById('rawText');
            if (rawTexts && rawTexts.length > 0) {
                rawTextElement.textContent = rawTexts.join('\n');
            } else {
                rawTextElement.textContent = '无原始文本数据';
            }
        }

        // 显示详细信息
        function displayDetails(result) {
            const detailsInfo = document.getElementById('detailsInfo');
            detailsInfo.innerHTML = '';
            
            const details = [
                { label: '识别引擎', value: result.engine },
                { label: '识别时间', value: new Date(result.timestamp).toLocaleString() },
                { label: '识别状态', value: result.success ? '✅ 成功' : '❌ 失败' },
                { label: '识别消息', value: result.message },
                { label: '文本行数', value: result.raw_texts ? result.raw_texts.length : 0 }
            ];
            
            details.forEach(detail => {
                if (detail.value !== null && detail.value !== undefined) {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'info-item';
                    itemElement.innerHTML = `
                        <span class="info-label">${detail.label}:</span>
                        <span class="info-value">${detail.value}</span>
                    `;
                    detailsInfo.appendChild(itemElement);
                }
            });
        }

        // 切换标签页
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有按钮的激活状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + 'Tab').classList.add('active');
            
            // 激活对应的按钮
            event.target.classList.add('active');
        }

        // 获取证件类型名称
        function getCardTypeName(cardType) {
            const typeMap = {
                'front': '身份证正面',
                'back': '身份证背面',
                'both': '正反面',
                'unknown': '未知类型'
            };
            return typeMap[cardType] || cardType;
        }

        // 显示错误消息
        function showError(message) {
            const existingError = document.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            
            const mainContent = document.querySelector('.main-content');
            mainContent.insertBefore(errorDiv, mainContent.firstChild);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        // 显示成功消息
        function showSuccess(message) {
            const existingSuccess = document.querySelector('.success-message');
            if (existingSuccess) {
                existingSuccess.remove();
            }
            
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.textContent = message;
            
            const mainContent = document.querySelector('.main-content');
            mainContent.insertBefore(successDiv, mainContent.firstChild);
            
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }
    </script>
</body>
</html>
