<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR文字识别</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            font-weight: 600;
        }

        .back-btn {
            padding: 10px 20px;
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #667eea;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .back-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .engine-selector {
            margin-bottom: 30px;
            text-align: center;
        }

        .engine-selector h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 18px;
        }

        .engine-options {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .engine-btn {
            padding: 12px 24px;
            border: 2px solid #ddd;
            background: white;
            color: #666;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .engine-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .engine-btn:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .engine-btn.active:hover {
            background: #5a6fd8;
            color: white;
        }

        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #667eea;
        }

        .upload-text {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 14px;
            color: #999;
        }

        .file-input {
            display: none;
        }

        .preview-area {
            display: none;
            margin-bottom: 30px;
        }

        .preview-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #667eea;
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-area {
            display: none;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .result-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .result-info {
            font-size: 14px;
            color: #666;
        }

        .result-text {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ddd;
            min-height: 200px;
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .result-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            max-width: 300px;
        }

        .message.success {
            background: #4caf50;
            color: white;
        }

        .message.error {
            background: #f44336;
            color: white;
        }

        .message.info {
            background: #2196f3;
            color: white;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .header {
                flex-direction: column;
                text-align: center;
            }

            .header h1 {
                font-size: 24px;
            }

            .upload-area {
                padding: 30px 20px;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 200px;
            }

            .result-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 OCR文字识别</h1>
            <button class="back-btn" onclick="goBack()">← 返回首页</button>
        </div>

        <div class="engine-selector">
            <h3>选择识别引擎：</h3>
            <div class="engine-options">
                <button class="engine-btn active" data-engine="smart">智能识别</button>
                <button class="engine-btn" data-engine="paddle">PaddleOCR</button>
                <button class="engine-btn" data-engine="tesseract">Tesseract</button>
            </div>
        </div>

        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📷</div>
            <div class="upload-text">点击或拖拽图片到此处</div>
            <div class="upload-hint">支持 JPG、PNG、BMP、GIF 格式，最大 10MB</div>
            <input type="file" class="file-input" id="fileInput" accept="image/*">
        </div>

        <div class="preview-area" id="previewArea">
            <img class="preview-image" id="previewImage" alt="预览图片">
        </div>

        <div class="controls">
            <button class="btn btn-primary" id="recognizeBtn" onclick="recognizeText()" disabled>开始识别</button>
            <button class="btn btn-secondary" id="clearBtn" onclick="clearAll()">清空重选</button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>正在识别中，请稍候...</div>
        </div>

        <div class="result-area" id="resultArea">
            <div class="result-header">
                <div class="result-title">识别结果</div>
                <div class="result-info" id="resultInfo"></div>
            </div>
            <div class="result-text" id="resultText"></div>
            <div class="result-actions">
                <button class="btn btn-secondary" onclick="copyResult()">复制文本</button>
                <button class="btn btn-secondary" onclick="downloadResult()">下载文本</button>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="message" id="message" style="display: none;"></div>

    <script>
        // 全局变量
        let selectedFile = null;
        let selectedEngine = 'smart';
        let recognitionResult = null;

        // 返回首页
        function goBack() {
            window.location.href = 'index.html';
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initEventListeners();
        });

        // 初始化事件监听器
        function initEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const engineBtns = document.querySelectorAll('.engine-btn');

            // 文件上传区域点击事件
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择事件
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // 引擎选择事件
            engineBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    selectEngine(btn.dataset.engine);
                });
            });
        }

        // 选择引擎
        function selectEngine(engine) {
            selectedEngine = engine;

            document.querySelectorAll('.engine-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.engine === engine) {
                    btn.classList.add('active');
                }
            });
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                processFile(file);
            }
        }

        // 处理拖拽悬停
        function handleDragOver(event) {
            event.preventDefault();
            document.getElementById('uploadArea').classList.add('dragover');
        }

        // 处理拖拽离开
        function handleDragLeave(event) {
            event.preventDefault();
            document.getElementById('uploadArea').classList.remove('dragover');
        }

        // 处理文件拖拽
        function handleDrop(event) {
            event.preventDefault();
            document.getElementById('uploadArea').classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        // 处理文件
        function processFile(file) {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showMessage('请选择图片文件', 'error');
                return;
            }

            // 验证文件大小 (10MB)
            if (file.size > 10 * 1024 * 1024) {
                showMessage('文件大小不能超过10MB', 'error');
                return;
            }

            selectedFile = file;

            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewImage = document.getElementById('previewImage');
                const previewArea = document.getElementById('previewArea');

                previewImage.src = e.target.result;
                previewArea.style.display = 'block';

                // 启用识别按钮
                document.getElementById('recognizeBtn').disabled = false;

                showMessage('图片上传成功', 'success');
            };
            reader.readAsDataURL(file);
        }

        // 开始识别
        async function recognizeText() {
            if (!selectedFile) {
                showMessage('请先选择图片', 'error');
                return;
            }

            // 显示加载状态
            showLoading(true);
            hideResult();

            try {
                const formData = new FormData();
                formData.append('image', selectedFile);

                // 根据选择的引擎确定API端点
                let endpoint;
                switch (selectedEngine) {
                    case 'paddle':
                        endpoint = '/api/ocr/paddle/recognize';
                        break;
                    case 'tesseract':
                        endpoint = '/api/ocr/tesseract/recognize';
                        break;
                    default:
                        endpoint = '/api/ocr/recognize';
                        formData.append('engine', 'tesseract'); // 默认首选Tesseract（真实识别）
                }

                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    displayResult(result);
                    showMessage('识别完成', 'success');
                } else {
                    showMessage(result.message || '识别失败', 'error');
                }

            } catch (error) {
                console.error('识别错误:', error);
                showMessage('识别失败: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            const loading = document.getElementById('loading');
            const recognizeBtn = document.getElementById('recognizeBtn');

            if (show) {
                loading.style.display = 'block';
                recognizeBtn.disabled = true;
                recognizeBtn.textContent = '识别中...';
            } else {
                loading.style.display = 'none';
                recognizeBtn.disabled = false;
                recognizeBtn.textContent = '开始识别';
            }
        }

        // 显示识别结果
        function displayResult(result) {
            recognitionResult = result;

            const resultArea = document.getElementById('resultArea');
            const resultInfo = document.getElementById('resultInfo');
            const resultText = document.getElementById('resultText');

            // 显示结果信息
            const engineName = getEngineName(result.engine);
            const textCount = result.texts ? result.texts.length : 0;
            const timestamp = new Date(result.timestamp).toLocaleString();

            resultInfo.textContent = `引擎: ${engineName} | 识别行数: ${textCount} | 时间: ${timestamp}`;

            // 显示识别文本
            const texts = result.texts || [];
            resultText.textContent = texts.join('\n');

            // 显示结果区域
            resultArea.style.display = 'block';

            // 滚动到结果区域
            resultArea.scrollIntoView({ behavior: 'smooth' });
        }

        // 获取引擎名称
        function getEngineName(engine) {
            const names = {
                'PaddleOCR': 'PaddleOCR',
                'Tesseract': 'Tesseract',
                'paddle': 'PaddleOCR',
                'tesseract': 'Tesseract'
            };
            return names[engine] || engine;
        }

        // 隐藏结果
        function hideResult() {
            document.getElementById('resultArea').style.display = 'none';
        }

        // 复制结果
        function copyResult() {
            if (!recognitionResult || !recognitionResult.texts) {
                showMessage('没有可复制的内容', 'error');
                return;
            }

            const text = recognitionResult.texts.join('\n');

            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showMessage('文本已复制到剪贴板', 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopy(text);
                });
            } else {
                fallbackCopy(text);
            }
        }

        // 备用复制方法
        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();

            try {
                document.execCommand('copy');
                showMessage('文本已复制到剪贴板', 'success');
            } catch (err) {
                showMessage('复制失败，请手动复制', 'error');
            }

            document.body.removeChild(textArea);
        }

        // 下载结果
        function downloadResult() {
            if (!recognitionResult || !recognitionResult.texts) {
                showMessage('没有可下载的内容', 'error');
                return;
            }

            const text = recognitionResult.texts.join('\n');
            const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `ocr_result_${new Date().getTime()}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);
            showMessage('文件下载已开始', 'success');
        }

        // 清空所有
        function clearAll() {
            selectedFile = null;
            recognitionResult = null;

            // 重置文件输入
            document.getElementById('fileInput').value = '';

            // 隐藏预览和结果
            document.getElementById('previewArea').style.display = 'none';
            hideResult();

            // 禁用识别按钮
            document.getElementById('recognizeBtn').disabled = true;

            showMessage('已清空，请重新选择图片', 'info');
        }

        // 显示消息
        function showMessage(text, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';

            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
