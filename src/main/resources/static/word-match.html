<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词消消乐</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            font-weight: 600;
        }

        .back-btn {
            padding: 10px 20px;
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #667eea;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .back-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .score, .timer, .level {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .timer.warning {
            color: #e74c3c;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .difficulty-selector {
            margin-bottom: 20px;
            text-align: center;
        }

        .difficulty-selector h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 18px;
        }

        .difficulty-options {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .difficulty-btn {
            padding: 12px 24px;
            border: 2px solid #ddd;
            background: white;
            color: #666;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .difficulty-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .difficulty-btn:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .difficulty-btn.active:hover {
            background: #5a6fd8;
            color: white;
        }

        .game-board {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 3px;
            margin-bottom: 20px;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 10px;
        }

        .cell {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border: 2px solid transparent;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }

        .cell:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .cell.selected {
            background: #2196f3;
            color: white;
            border-color: #1976d2;
        }

        .cell.matched {
            background: #4caf50;
            color: white;
            border-color: #388e3c;
        }

        .cell.hint {
            background: #ff9800;
            color: white;
            border-color: #f57c00;
            animation: highlight 0.5s ease;
        }

        @keyframes highlight {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #667eea;
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .word-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .word-list h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }

        .words {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .word-item {
            padding: 6px 12px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            color: #666;
        }

        .word-item.found {
            background: #4caf50;
            color: white;
            border-color: #388e3c;
        }

        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 1000;
            animation: fadeInOut 2s ease;
        }

        .message.success {
            background: #4caf50;
            color: white;
        }

        .message.error {
            background: #f44336;
            color: white;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .modal-content h2 {
            margin-bottom: 15px;
            color: #333;
            font-size: 24px;
        }

        .modal-content p {
            margin-bottom: 25px;
            color: #666;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .rules {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .rules h3 {
            margin-bottom: 10px;
            color: #1976d2;
            font-size: 16px;
        }

        .rules ul {
            list-style: none;
            color: #333;
        }

        .rules li {
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }

        .rules li:before {
            content: "•";
            color: #2196f3;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .header {
                flex-direction: column;
                text-align: center;
            }

            .header h1 {
                font-size: 24px;
            }

            .game-board {
                grid-template-columns: repeat(6, 1fr);
            }

            .cell {
                font-size: 14px;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 单词消消乐</h1>
            <button class="back-btn" onclick="goBack()">← 返回首页</button>
        </div>

        <div class="rules" id="rules">
            <h3>游戏规则：</h3>
            <ul>
                <li>在字母网格中找到隐藏的单词</li>
                <li>点击字母选择，连续选择组成单词</li>
                <li>单词可以是水平或垂直方向</li>
                <li>找到所有单词或时间用完游戏结束</li>
                <li>每个字母得10分，越长的单词得分越高</li>
            </ul>
        </div>

        <div class="difficulty-selector" id="difficultySelector">
            <h3>选择难度：</h3>
            <div class="difficulty-options">
                <button class="difficulty-btn active" data-level="easy">简单 (10个单词)</button>
                <button class="difficulty-btn" data-level="medium">中等 (15个单词)</button>
                <button class="difficulty-btn" data-level="hard">困难 (20个单词)</button>
            </div>
        </div>

        <div class="game-info" id="gameInfo" style="display: none;">
            <div class="score">得分: <span id="score">0</span></div>
            <div class="level">难度: <span id="currentLevel">简单</span></div>
            <div class="timer" id="timer">时间: 60秒</div>
        </div>

        <div class="game-board" id="gameBoard"></div>

        <div class="controls">
            <button class="btn btn-primary" id="startBtn" onclick="startGame()">开始游戏</button>
            <button class="btn btn-secondary" id="resetBtn" onclick="resetGame()" disabled>重新开始</button>
            <button class="btn btn-secondary" id="hintBtn" onclick="showHint()" disabled>提示 (<span id="hintCount">3</span>)</button>
        </div>

        <div class="word-list" id="wordListContainer" style="display: none;">
            <h3>目标单词 (<span id="foundCount">0</span>/<span id="totalCount">0</span>)：</h3>
            <div class="words" id="wordList"></div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="message" id="message" style="display: none;"></div>

    <!-- 游戏结束模态框 -->
    <div class="modal" id="gameOverModal" style="display: none;">
        <div class="modal-content">
            <h2 id="gameOverTitle">游戏结束</h2>
            <p id="gameOverMessage">你的得分是: 0</p>
            <div class="modal-buttons">
                <button class="btn btn-primary" onclick="restartGame()">再玩一次</button>
                <button class="btn btn-secondary" onclick="closeGameOver()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            isPlaying: false,
            score: 0,
            timeLeft: 60,
            timer: null,
            difficulty: 'easy',
            board: [],
            selectedCells: [],
            currentWords: [],
            foundWords: new Set(),
            hintCount: 3,
            boardSize: 8
        };

        // 单词库
        const wordBank = {
            easy: ['CAT', 'DOG', 'SUN', 'RUN', 'HOT', 'RED', 'BIG', 'NEW', 'YES', 'DAY'],
            medium: ['APPLE', 'BIRD', 'FISH', 'TREE', 'BOOK', 'DESK', 'DOOR', 'ROOM', 'TIME', 'WORK', 'PLAY', 'READ', 'WALK', 'TALK', 'SING'],
            hard: ['WATER', 'EARTH', 'SMILE', 'LAUGH', 'SLEEP', 'DREAM', 'HAPPY', 'PEACE', 'MUSIC', 'SPORT', 'STORY', 'PHONE', 'EMAIL', 'LIGHT', 'SOUND', 'VOICE', 'HEART', 'WORLD', 'POWER', 'SPACE']
        };

        // 返回首页
        function goBack() {
            if (gameState.isPlaying) {
                if (confirm('游戏正在进行中，确定要返回首页吗？')) {
                    window.location.href = 'index.html';
                }
            } else {
                window.location.href = 'index.html';
            }
        }

        // 选择难度
        function selectDifficulty(level) {
            if (gameState.isPlaying) return;

            gameState.difficulty = level;

            // 更新按钮状态
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.level === level) {
                    btn.classList.add('active');
                }
            });
        }

        // 初始化游戏板
        function initBoard() {
            const board = Array(gameState.boardSize).fill().map(() =>
                Array(gameState.boardSize).fill().map(() => ({
                    letter: '',
                    isWordPart: false,
                    wordIndex: -1,
                    matched: false
                }))
            );

            const words = wordBank[gameState.difficulty];
            gameState.currentWords = [...words];
            const placedWords = [];

            // 尝试放置每个单词
            words.forEach((word, wordIndex) => {
                let placed = false;
                let attempts = 0;
                const maxAttempts = 100;

                while (!placed && attempts < maxAttempts) {
                    const direction = Math.random() < 0.5 ? 'horizontal' : 'vertical';
                    const startRow = Math.floor(Math.random() * gameState.boardSize);
                    const startCol = Math.floor(Math.random() * gameState.boardSize);

                    if (canPlaceWord(board, word, startRow, startCol, direction)) {
                        placeWord(board, word, startRow, startCol, direction, wordIndex);
                        placedWords.push(word);
                        placed = true;
                    }
                    attempts++;
                }

                if (!placed) {
                    console.warn(`无法放置单词: ${word}`);
                }
            });

            // 填充剩余空格
            for (let i = 0; i < gameState.boardSize; i++) {
                for (let j = 0; j < gameState.boardSize; j++) {
                    if (board[i][j].letter === '') {
                        board[i][j].letter = getRandomLetter();
                    }
                }
            }

            gameState.board = board;
            gameState.currentWords = placedWords;
            renderBoard();
            updateWordList();
        }

        // 检查是否可以放置单词
        function canPlaceWord(board, word, startRow, startCol, direction) {
            if (direction === 'horizontal') {
                if (startCol + word.length > gameState.boardSize) return false;
                for (let i = 0; i < word.length; i++) {
                    if (board[startRow][startCol + i].letter !== '') return false;
                }
            } else {
                if (startRow + word.length > gameState.boardSize) return false;
                for (let i = 0; i < word.length; i++) {
                    if (board[startRow + i][startCol].letter !== '') return false;
                }
            }
            return true;
        }

        // 放置单词
        function placeWord(board, word, startRow, startCol, direction, wordIndex) {
            if (direction === 'horizontal') {
                for (let i = 0; i < word.length; i++) {
                    board[startRow][startCol + i].letter = word[i];
                    board[startRow][startCol + i].isWordPart = true;
                    board[startRow][startCol + i].wordIndex = wordIndex;
                }
            } else {
                for (let i = 0; i < word.length; i++) {
                    board[startRow + i][startCol].letter = word[i];
                    board[startRow + i][startCol].isWordPart = true;
                    board[startRow + i][startCol].wordIndex = wordIndex;
                }
            }
        }

        // 获取随机字母
        function getRandomLetter() {
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            return letters[Math.floor(Math.random() * letters.length)];
        }

        // 渲染游戏板
        function renderBoard() {
            const boardElement = document.getElementById('gameBoard');
            boardElement.innerHTML = '';

            for (let i = 0; i < gameState.boardSize; i++) {
                for (let j = 0; j < gameState.boardSize; j++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.textContent = gameState.board[i][j].letter;
                    cell.dataset.row = i;
                    cell.dataset.col = j;
                    cell.addEventListener('click', () => selectCell(i, j));
                    boardElement.appendChild(cell);
                }
            }
        }

        // 选择单元格
        function selectCell(row, col) {
            if (!gameState.isPlaying) return;
            if (gameState.board[row][col].matched) return;

            const cellIndex = gameState.selectedCells.findIndex(
                cell => cell.row === row && cell.col === col
            );

            if (cellIndex === -1) {
                gameState.selectedCells.push({ row, col });
            } else {
                gameState.selectedCells.splice(cellIndex, 1);
            }

            updateCellDisplay();

            // 检查是否形成单词
            if (gameState.selectedCells.length >= 3) {
                checkForWord();
            }
        }

        // 更新单元格显示
        function updateCellDisplay() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach((cell, index) => {
                const row = Math.floor(index / gameState.boardSize);
                const col = index % gameState.boardSize;

                cell.classList.remove('selected', 'matched', 'hint');

                if (gameState.board[row][col].matched) {
                    cell.classList.add('matched');
                } else if (gameState.selectedCells.some(c => c.row === row && c.col === col)) {
                    cell.classList.add('selected');
                }
            });
        }

        // 检查单词
        function checkForWord() {
            const selectedLetters = gameState.selectedCells.map(
                cell => gameState.board[cell.row][cell.col].letter
            ).join('');

            // 检查是否匹配任何目标单词
            const matchedWord = gameState.currentWords.find(word =>
                word === selectedLetters && !gameState.foundWords.has(word)
            );

            if (matchedWord) {
                // 找到单词
                gameState.foundWords.add(matchedWord);
                gameState.score += matchedWord.length * 10;

                // 标记单元格为已匹配
                gameState.selectedCells.forEach(cell => {
                    gameState.board[cell.row][cell.col].matched = true;
                });

                showMessage(`找到单词: ${matchedWord} (+${matchedWord.length * 10}分)`, 'success');
                updateScore();
                updateWordList();

                // 检查是否完成所有单词
                if (gameState.foundWords.size === gameState.currentWords.length) {
                    endGame(true);
                }
            } else {
                showMessage('未找到匹配的单词', 'error');
            }

            // 清空选择
            gameState.selectedCells = [];
            updateCellDisplay();
        }

        // 显示消息
        function showMessage(text, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';

            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 2000);
        }

        // 更新分数
        function updateScore() {
            document.getElementById('score').textContent = gameState.score;
        }

        // 更新单词列表
        function updateWordList() {
            const wordListEl = document.getElementById('wordList');
            const foundCountEl = document.getElementById('foundCount');
            const totalCountEl = document.getElementById('totalCount');

            wordListEl.innerHTML = '';
            gameState.currentWords.forEach(word => {
                const wordEl = document.createElement('span');
                wordEl.className = 'word-item';
                wordEl.textContent = word;

                if (gameState.foundWords.has(word)) {
                    wordEl.classList.add('found');
                }

                wordListEl.appendChild(wordEl);
            });

            foundCountEl.textContent = gameState.foundWords.size;
            totalCountEl.textContent = gameState.currentWords.length;
        }

        // 开始游戏
        function startGame() {
            gameState.isPlaying = true;
            gameState.score = 0;
            gameState.timeLeft = 60;
            gameState.foundWords.clear();
            gameState.selectedCells = [];
            gameState.hintCount = 3;

            // 更新UI
            document.getElementById('rules').style.display = 'none';
            document.getElementById('difficultySelector').style.display = 'none';
            document.getElementById('gameInfo').style.display = 'flex';
            document.getElementById('wordListContainer').style.display = 'block';
            document.getElementById('startBtn').disabled = true;
            document.getElementById('resetBtn').disabled = false;
            document.getElementById('hintBtn').disabled = false;

            // 更新当前难度显示
            const levelNames = { easy: '简单', medium: '中等', hard: '困难' };
            document.getElementById('currentLevel').textContent = levelNames[gameState.difficulty];

            // 初始化游戏
            initBoard();
            updateScore();
            updateHintCount();
            startTimer();
        }

        // 重置游戏
        function resetGame() {
            if (gameState.timer) {
                clearInterval(gameState.timer);
            }

            gameState.isPlaying = false;

            // 重置UI
            document.getElementById('rules').style.display = 'block';
            document.getElementById('difficultySelector').style.display = 'block';
            document.getElementById('gameInfo').style.display = 'none';
            document.getElementById('wordListContainer').style.display = 'none';
            document.getElementById('startBtn').disabled = false;
            document.getElementById('resetBtn').disabled = true;
            document.getElementById('hintBtn').disabled = true;

            // 清空游戏板
            document.getElementById('gameBoard').innerHTML = '';
        }

        // 开始计时器
        function startTimer() {
            if (gameState.timer) {
                clearInterval(gameState.timer);
            }

            gameState.timer = setInterval(() => {
                gameState.timeLeft--;
                updateTimer();

                if (gameState.timeLeft <= 0) {
                    endGame(false);
                }
            }, 1000);
        }

        // 更新计时器显示
        function updateTimer() {
            const timerEl = document.getElementById('timer');
            timerEl.textContent = `时间: ${gameState.timeLeft}秒`;

            if (gameState.timeLeft <= 10) {
                timerEl.classList.add('warning');
            } else {
                timerEl.classList.remove('warning');
            }
        }

        // 显示提示
        function showHint() {
            if (gameState.hintCount <= 0 || !gameState.isPlaying) return;

            const remainingWords = gameState.currentWords.filter(word => !gameState.foundWords.has(word));
            if (remainingWords.length === 0) return;

            const randomWord = remainingWords[Math.floor(Math.random() * remainingWords.length)];
            gameState.hintCount--;
            updateHintCount();

            // 在板上找到并高亮显示单词
            highlightWordOnBoard(randomWord);
            showMessage(`提示: 寻找单词 "${randomWord}"`, 'success');
        }

        // 在板上高亮显示单词
        function highlightWordOnBoard(word) {
            // 查找单词在板上的位置
            for (let i = 0; i < gameState.boardSize; i++) {
                for (let j = 0; j < gameState.boardSize; j++) {
                    // 检查水平方向
                    if (j + word.length <= gameState.boardSize) {
                        let match = true;
                        for (let k = 0; k < word.length; k++) {
                            if (gameState.board[i][j + k].letter !== word[k]) {
                                match = false;
                                break;
                            }
                        }
                        if (match) {
                            highlightCells(i, j, word.length, 'horizontal');
                            return;
                        }
                    }

                    // 检查垂直方向
                    if (i + word.length <= gameState.boardSize) {
                        let match = true;
                        for (let k = 0; k < word.length; k++) {
                            if (gameState.board[i + k][j].letter !== word[k]) {
                                match = false;
                                break;
                            }
                        }
                        if (match) {
                            highlightCells(i, j, word.length, 'vertical');
                            return;
                        }
                    }
                }
            }
        }

        // 高亮显示单元格
        function highlightCells(startRow, startCol, length, direction) {
            const cells = document.querySelectorAll('.cell');

            for (let i = 0; i < length; i++) {
                const row = direction === 'horizontal' ? startRow : startRow + i;
                const col = direction === 'horizontal' ? startCol + i : startCol;
                const cellIndex = row * gameState.boardSize + col;

                if (cells[cellIndex]) {
                    cells[cellIndex].classList.add('hint');
                }
            }

            // 2秒后移除高亮
            setTimeout(() => {
                cells.forEach(cell => cell.classList.remove('hint'));
            }, 2000);
        }

        // 更新提示次数显示
        function updateHintCount() {
            document.getElementById('hintCount').textContent = gameState.hintCount;
            document.getElementById('hintBtn').disabled = gameState.hintCount <= 0 || !gameState.isPlaying;
        }

        // 结束游戏
        function endGame(isComplete) {
            gameState.isPlaying = false;

            if (gameState.timer) {
                clearInterval(gameState.timer);
            }

            const modal = document.getElementById('gameOverModal');
            const title = document.getElementById('gameOverTitle');
            const message = document.getElementById('gameOverMessage');

            if (isComplete) {
                title.textContent = '🎉 恭喜完成！';
                message.textContent = `你找到了所有单词！\n最终得分: ${gameState.score}分\n用时: ${60 - gameState.timeLeft}秒`;
            } else {
                title.textContent = '⏰ 时间到！';
                message.textContent = `游戏结束！\n最终得分: ${gameState.score}分\n找到单词: ${gameState.foundWords.size}/${gameState.currentWords.length}个`;
            }

            modal.style.display = 'flex';
        }

        // 重新开始游戏
        function restartGame() {
            closeGameOver();
            resetGame();
            setTimeout(() => startGame(), 100);
        }

        // 关闭游戏结束对话框
        function closeGameOver() {
            document.getElementById('gameOverModal').style.display = 'none';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定难度选择事件
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    selectDifficulty(btn.dataset.level);
                });
            });

            // 初始化游戏板（空白状态）
            const boardElement = document.getElementById('gameBoard');
            for (let i = 0; i < gameState.boardSize * gameState.boardSize; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.textContent = '';
                boardElement.appendChild(cell);
            }
        });
    </script>
</body>
</html>