.word-match-game {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

.game-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.difficulty-selector {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.difficulty-selector h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.difficulty-options {
  display: flex;
  gap: 10px;
}

.difficulty-options button {
  flex: 1;
  padding: 8px 16px;
  font-size: 14px;
  background-color: #fff;
  color: #666;
  border: 1px solid #ddd;
}

.difficulty-options button.active {
  background-color: #2196f3;
  color: white;
  border-color: #2196f3;
}

.game-board {
  display: grid;
  gap: 5px;
  margin-bottom: 20px;
}

.board-row {
  display: flex;
  gap: 5px;
}

.cell {
  width: 50px;
  height: 50px;
  border: 2px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  background-color: #fff;
  transition: all 0.3s ease;
  position: relative;
}

.cell.selected {
  background-color: #e3f2fd;
  border-color: #2196f3;
  transform: scale(1.1);
}

.cell.matched {
  background-color: #c8e6c9;
  border-color: #4caf50;
  cursor: not-allowed;
}

.cell.hidden {
  animation: disappear 0.5s ease forwards;
}

@keyframes disappear {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }

  100% {
    opacity: 0;
    transform: scale(0);
  }
}

.controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 20px;
}

button {
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  transition: all 0.3s ease;
}

button:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.word-list {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.word-list h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.words {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.word-item {
  padding: 4px 8px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.word-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  animation: slideDown 0.3s ease, fadeOut 0.3s ease 1.7s;
  z-index: 1000;
}

.message.success {
  background-color: #4caf50;
}

.message.error {
  background-color: #f44336;
}

@keyframes slideDown {
  from {
    transform: translate(-50%, -100%);
    opacity: 0;
  }

  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.timer.warning {
  color: #f44336;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

.cell.hint {
  background-color: #fff3cd;
  border-color: #ffc107;
  animation: hintPulse 1s infinite;
}

@keyframes hintPulse {
  0% {
    background-color: #fff3cd;
  }

  50% {
    background-color: #ffe69c;
  }

  100% {
    background-color: #fff3cd;
  }
}

.word-item.hint {
  background-color: #fff3cd;
  border-color: #ffc107;
  animation: hintPulse 1s infinite;
}

.game-over-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-content h2 {
  margin-top: 0;
  color: #2196f3;
}

.modal-content p {
  margin: 20px 0;
  white-space: pre-line;
}

.modal-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.modal-buttons button {
  min-width: 100px;
}