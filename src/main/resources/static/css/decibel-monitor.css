/* 分贝检测器专用样式 */

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
    50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
    100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* 分贝值动画 */
.decibel-value.active {
    animation: pulse 1s ease-in-out infinite;
}

.decibel-display.monitoring {
    animation: glow 2s ease-in-out infinite;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 1.5s ease-in-out infinite;
}

.status-indicator.quiet {
    background-color: #48bb78;
}

.status-indicator.normal {
    background-color: #667eea;
}

.status-indicator.active {
    background-color: #ed8936;
}

.status-indicator.loud {
    background-color: #f56565;
}

/* 进度条增强效果 */
.level-bar {
    position: relative;
    overflow: hidden;
}

.level-bar.animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: slide 2s infinite;
}

@keyframes slide {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 按钮增强效果 */
.control-btn {
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.control-btn:active::before {
    width: 300px;
    height: 300px;
}

/* 统计卡片动画 */
.stat-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-value {
    transition: color 0.3s ease;
}

.stat-value.updated {
    animation: bounce 0.6s ease;
    color: #667eea;
}

/* 阈值卡片增强 */
.threshold-item {
    position: relative;
    transition: all 0.3s ease;
}

.threshold-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.1) 100%);
    border-radius: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.threshold-item:hover::before {
    opacity: 1;
}

.threshold-item.active {
    border: 2px solid #667eea;
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

/* 响应式增强 */
@media (max-width: 480px) {
    .decibel-value {
        font-size: 48px;
    }
    
    .status-text {
        font-size: 16px;
    }
    
    .control-btn {
        padding: 12px 20px;
        font-size: 14px;
    }
    
    .threshold-item {
        padding: 10px;
    }
    
    .stat-value {
        font-size: 20px;
    }
}

/* 加载状态增强 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    z-index: 10;
}

.loading-spinner-enhanced {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid #48bb78;
}

.notification.warning {
    border-left: 4px solid #ed8936;
}

.notification.error {
    border-left: 4px solid #f56565;
}

/* 历史图表区域 */
.chart-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

/* 设置面板 */
.settings-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    z-index: 1000;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    backdrop-filter: blur(5px);
}

.setting-item {
    margin-bottom: 20px;
}

.setting-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.setting-input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.setting-input:focus {
    outline: none;
    border-color: #667eea;
}

.setting-description {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .monitor-container {
        background: #2d3748;
        color: white;
    }
    
    .decibel-display {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    }
    
    .threshold-item,
    .history-section {
        background: #4a5568;
        color: white;
    }
    
    .setting-input {
        background: #4a5568;
        border-color: #718096;
        color: white;
    }
}

/* 可访问性增强 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .control-btn {
        border: 2px solid currentColor;
    }
    
    .level-bar {
        border: 1px solid #000;
    }
    
    .threshold-item {
        border: 1px solid #666;
    }
}
