/* 通用样式 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式工具类 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.col {
    flex: 1;
    padding: 0 10px;
}

/* 文本工具类 */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-primary {
    color: #409eff;
}

.text-success {
    color: #67c23a;
}

.text-warning {
    color: #e6a23c;
}

.text-danger {
    color: #f56c6c;
}

.text-info {
    color: #909399;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 8px; }
.m-2 { margin: 16px; }
.m-3 { margin: 24px; }
.m-4 { margin: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

/* 显示/隐藏工具类 */
.d-none {
    display: none;
}

.d-block {
    display: block;
}

.d-flex {
    display: flex;
}

.d-inline {
    display: inline;
}

.d-inline-block {
    display: inline-block;
}

/* Flex工具类 */
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-around {
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.flex-column {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-1 {
    flex: 1;
}

/* 阴影工具类 */
.shadow-sm {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.shadow {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.shadow-lg {
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

/* 圆角工具类 */
.rounded {
    border-radius: 4px;
}

.rounded-lg {
    border-radius: 8px;
}

.rounded-xl {
    border-radius: 12px;
}

.rounded-full {
    border-radius: 50%;
}

/* 边框工具类 */
.border {
    border: 1px solid #dcdfe6;
}

.border-top {
    border-top: 1px solid #dcdfe6;
}

.border-bottom {
    border-bottom: 1px solid #dcdfe6;
}

.border-left {
    border-left: 1px solid #dcdfe6;
}

.border-right {
    border-right: 1px solid #dcdfe6;
}

/* 背景工具类 */
.bg-white {
    background-color: #ffffff;
}

.bg-light {
    background-color: #f8f9fa;
}

.bg-dark {
    background-color: #343a40;
}

.bg-primary {
    background-color: #409eff;
}

.bg-success {
    background-color: #67c23a;
}

.bg-warning {
    background-color: #e6a23c;
}

.bg-danger {
    background-color: #f56c6c;
}

/* 动画工具类 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 响应式断点 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .row {
        margin: 0 -5px;
    }
    
    .col {
        padding: 0 5px;
    }
    
    /* 移动端隐藏 */
    .d-md-none {
        display: none;
    }
    
    /* 移动端显示 */
    .d-md-block {
        display: block;
    }
}

@media (min-width: 769px) {
    /* 桌面端隐藏 */
    .d-lg-none {
        display: none;
    }
    
    /* 桌面端显示 */
    .d-lg-block {
        display: block;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}
