/**
 * 通用Markdown样式
 * 适用于所有聊天页面的Markdown内容渲染
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

/* 基础容器样式 */
.markdown-content {
    line-height: 1.6;
    color: #333;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* 标题样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin: 16px 0 12px 0;
    font-weight: bold;
    line-height: 1.4;
}

.markdown-content h1 {
    font-size: 1.4em;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.markdown-content h2 {
    font-size: 1.3em;
    color: #34495e;
    border-bottom: 1px solid #bdc3c7;
    padding-bottom: 6px;
}

.markdown-content h3 {
    font-size: 1.2em;
    color: #34495e;
}

.markdown-content h4 {
    font-size: 1.1em;
    color: #5d6d7e;
}

.markdown-content h5 {
    font-size: 1.05em;
    color: #5d6d7e;
}

.markdown-content h6 {
    font-size: 1.0em;
    color: #7b8a8b;
}

/* 段落样式 */
.markdown-content p {
    margin: 12px 0;
    line-height: 1.6;
}

/* 代码样式 */
.markdown-content code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    color: #e74c3c;
}

.markdown-content pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    overflow-x: auto;
    margin: 16px 0;
    line-height: 1.4;
}

.markdown-content pre code {
    background: none;
    padding: 0;
    border-radius: 0;
    color: #333;
    font-size: 0.9em;
}

/* 列表样式 */
.markdown-content ul,
.markdown-content ol {
    margin: 12px 0;
    padding-left: 24px;
}

.markdown-content li {
    margin: 6px 0;
    line-height: 1.5;
}

.markdown-content ul li {
    list-style-type: disc;
}

.markdown-content ol li {
    list-style-type: decimal;
}

/* 嵌套列表 */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
    margin: 4px 0;
}

/* 链接样式 */
.markdown-content a {
    color: #3498db;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.3s ease;
}

.markdown-content a:hover {
    color: #2980b9;
    border-bottom-color: #2980b9;
}

/* 强调样式 */
.markdown-content strong {
    font-weight: bold;
    color: #2c3e50;
}

.markdown-content em {
    font-style: italic;
    color: #34495e;
}

/* 表格样式 */
.markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;
    border: 1px solid #ddd;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
}

.markdown-content th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
}

.markdown-content tr:nth-child(even) {
    background-color: #f9f9f9;
}

.markdown-content tr:hover {
    background-color: #f5f5f5;
}

/* 引用样式 */
.markdown-content blockquote {
    border-left: 4px solid #3498db;
    margin: 16px 0;
    padding: 12px 16px;
    background-color: #f8f9fa;
    color: #555;
    font-style: italic;
}

.markdown-content blockquote p {
    margin: 0;
}

/* 分割线样式 */
.markdown-content hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, transparent, #bdc3c7, transparent);
    margin: 24px 0;
}

/* 数学公式样式 */
.markdown-content .katex {
    font-size: 1.1em;
}

.markdown-content .katex-display {
    margin: 16px 0;
    text-align: center;
}

/* 数学公式备用样式 */
.markdown-content .math-fallback {
    font-family: 'Times New Roman', serif;
    font-style: italic;
    color: #2c3e50;
    background: rgba(52, 152, 219, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.markdown-content .math-fallback-block {
    font-family: 'Times New Roman', serif;
    font-style: italic;
    color: #2c3e50;
    background: rgba(52, 152, 219, 0.1);
    padding: 12px;
    border-radius: 6px;
    border: 1px solid rgba(52, 152, 219, 0.2);
    text-align: center;
    margin: 16px 0;
    font-size: 1.1em;
}

.markdown-content .math-fallback::before,
.markdown-content .math-fallback-block::before {
    content: "📐 ";
    opacity: 0.6;
    font-size: 0.8em;
}

/* 代码高亮样式 */
.markdown-content .hljs {
    background: #f8f9fa !important;
    color: #333 !important;
}

.markdown-content .hljs-comment {
    color: #998;
    font-style: italic;
}

.markdown-content .hljs-keyword {
    color: #333;
    font-weight: bold;
}

.markdown-content .hljs-string {
    color: #d14;
}

.markdown-content .hljs-number {
    color: #099;
}

.markdown-content .hljs-function {
    color: #900;
    font-weight: bold;
}

.markdown-content .hljs-variable {
    color: #008080;
}

.markdown-content .hljs-tag {
    color: #000080;
}

.markdown-content .hljs-attribute {
    color: #008080;
}

.markdown-content .hljs-value {
    color: #d14;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .markdown-content {
        font-size: 14px;
    }
    
    .markdown-content h1 {
        font-size: 1.3em;
    }
    
    .markdown-content h2 {
        font-size: 1.2em;
    }
    
    .markdown-content h3 {
        font-size: 1.1em;
    }
    
    .markdown-content pre {
        padding: 12px;
        font-size: 0.8em;
    }
    
    .markdown-content table {
        font-size: 0.9em;
    }
    
    .markdown-content th,
    .markdown-content td {
        padding: 6px 8px;
    }
}

/* 打印样式 */
@media print {
    .markdown-content {
        color: #000;
    }
    
    .markdown-content a {
        color: #000;
        text-decoration: underline;
    }
    
    .markdown-content pre {
        border: 1px solid #000;
        background: #fff;
    }
}
