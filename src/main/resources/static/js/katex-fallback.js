/**
 * KaTeX 备用方案
 * 当KaTeX库加载失败时，提供基础的数学公式渲染
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 检查KaTeX是否加载成功
function checkKaTeXLoaded() {
    return typeof katex !== 'undefined';
}

// 处理LaTeX环境
function processLaTeXEnvironments(formula) {
    let processed = formula;

    // 处理 cases 环境（分段函数）
    processed = processed.replace(/\\begin\{cases\}([\s\S]*?)\\end\{cases\}/g, function(match, content) {
        // 分割每一行
        const lines = content.split('\\\\').map(line => line.trim()).filter(line => line);
        const caseItems = lines.map(line => `<div class="case-item">${line}</div>`).join('');
        return `<div class="cases-container">
            <div class="cases-brace">{</div>
            <div class="cases-content">${caseItems}</div>
        </div>`;
    });

    // 处理 align 环境（对齐方程组）
    processed = processed.replace(/\\begin\{align\*?\}([\s\S]*?)\\end\{align\*?\}/g, function(match, content) {
        const lines = content.split('\\\\').map(line => line.trim()).filter(line => line);
        const alignItems = lines.map(line => `<div class="align-item">${line}</div>`).join('');
        return `<div class="align-container">${alignItems}</div>`;
    });

    // 处理 matrix 环境
    processed = processed.replace(/\\begin\{(p|b|v|V|B)matrix\}([\s\S]*?)\\end\{(p|b|v|V|B)matrix\}/g, function(match, type, content) {
        const rows = content.split('\\\\').map(row => row.trim()).filter(row => row);
        const matrixRows = rows.map(row => {
            const cells = row.split('&').map(cell => `<td>${cell.trim()}</td>`).join('');
            return `<tr>${cells}</tr>`;
        }).join('');

        const brackets = {
            'p': ['(', ')'],
            'b': ['[', ']'],
            'v': ['|', '|'],
            'V': ['‖', '‖'],
            'B': ['{', '}']
        };

        const [leftBracket, rightBracket] = brackets[type] || ['', ''];

        return `<div class="matrix-container">
            <span class="matrix-bracket-left">${leftBracket}</span>
            <table class="matrix-table">${matrixRows}</table>
            <span class="matrix-bracket-right">${rightBracket}</span>
        </div>`;
    });

    // 处理 array 环境
    processed = processed.replace(/\\begin\{array\}\{[^}]*\}([\s\S]*?)\\end\{array\}/g, function(match, content) {
        const rows = content.split('\\\\').map(row => row.trim()).filter(row => row);
        const arrayRows = rows.map(row => {
            const cells = row.split('&').map(cell => `<td>${cell.trim()}</td>`).join('');
            return `<tr>${cells}</tr>`;
        }).join('');
        return `<table class="array-table">${arrayRows}</table>`;
    });

    return processed;
}

// 导出函数到全局作用域
window.processLaTeXEnvironments = processLaTeXEnvironments;

// 简单的数学公式渲染备用方案
function renderMathFallback(formula, displayMode = false) {
    // 基础的数学符号替换
    let rendered = formula
        // 希腊字母
        .replace(/\\alpha/g, 'α')
        .replace(/\\beta/g, 'β')
        .replace(/\\gamma/g, 'γ')
        .replace(/\\delta/g, 'δ')
        .replace(/\\epsilon/g, 'ε')
        .replace(/\\pi/g, 'π')
        .replace(/\\sigma/g, 'σ')
        .replace(/\\theta/g, 'θ')
        .replace(/\\lambda/g, 'λ')
        .replace(/\\mu/g, 'μ')
        .replace(/\\phi/g, 'φ')
        .replace(/\\psi/g, 'ψ')
        .replace(/\\omega/g, 'ω')
        
        // 数学运算符
        .replace(/\\times/g, '×')
        .replace(/\\div/g, '÷')
        .replace(/\\pm/g, '±')
        .replace(/\\mp/g, '∓')
        .replace(/\\cdot/g, '·')
        .replace(/\\ast/g, '∗')
        .replace(/\\star/g, '⋆')
        .replace(/\\circ/g, '∘')
        .replace(/\\bullet/g, '•')
        
        // 关系符号
        .replace(/\\leq/g, '≤')
        .replace(/\\geq/g, '≥')
        .replace(/\\neq/g, '≠')
        .replace(/\\approx/g, '≈')
        .replace(/\\equiv/g, '≡')
        .replace(/\\sim/g, '∼')
        .replace(/\\propto/g, '∝')
        
        // 集合符号
        .replace(/\\in/g, '∈')
        .replace(/\\notin/g, '∉')
        .replace(/\\subset/g, '⊂')
        .replace(/\\supset/g, '⊃')
        .replace(/\\subseteq/g, '⊆')
        .replace(/\\supseteq/g, '⊇')
        .replace(/\\cup/g, '∪')
        .replace(/\\cap/g, '∩')
        .replace(/\\emptyset/g, '∅')
        
        // 逻辑符号
        .replace(/\\land/g, '∧')
        .replace(/\\lor/g, '∨')
        .replace(/\\neg/g, '¬')
        .replace(/\\exists/g, '∃')
        .replace(/\\forall/g, '∀')
        
        // 箭头
        .replace(/\\rightarrow/g, '→')
        .replace(/\\leftarrow/g, '←')
        .replace(/\\leftrightarrow/g, '↔')
        .replace(/\\Rightarrow/g, '⇒')
        .replace(/\\Leftarrow/g, '⇐')
        .replace(/\\Leftrightarrow/g, '⇔')
        
        // 积分和求和
        .replace(/\\int/g, '∫')
        .replace(/\\sum/g, '∑')
        .replace(/\\prod/g, '∏')
        .replace(/\\lim/g, 'lim')
        
        // 根号和分数（简化处理）
        .replace(/\\sqrt\{([^}]+)\}/g, '√($1)')
        .replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '($1)/($2)')

        // 上下标（简化处理）
        .replace(/\^(\w+)/g, '<sup>$1</sup>')
        .replace(/_(\w+)/g, '<sub>$1</sub>')
        .replace(/\^{([^}]+)}/g, '<sup>$1</sup>')
        .replace(/_{([^}]+)}/g, '<sub>$1</sub>')

        // 括号
        .replace(/\\left\(/g, '(')
        .replace(/\\right\)/g, ')')
        .replace(/\\left\[/g, '[')
        .replace(/\\right\]/g, ']')
        .replace(/\\left\{/g, '{')
        .replace(/\\right\}/g, '}')

        // 空格
        .replace(/\\,/g, ' ')
        .replace(/\\;/g, '  ')
        .replace(/\\quad/g, '    ')
        .replace(/\\qquad/g, '        ');

    // 处理复杂的LaTeX环境
    rendered = processLaTeXEnvironments(rendered);

    // 创建渲染元素
    const className = displayMode ? 'math-display-fallback' : 'math-inline-fallback';
    return `<span class="${className}">${rendered}</span>`;
}

// 备用渲染函数
window.renderMathWithFallback = function(formula, displayMode = false) {
    if (checkKaTeXLoaded()) {
        try {
            return katex.renderToString(formula, {
                displayMode: displayMode,
                throwOnError: false
            });
        } catch (e) {
            console.warn('KaTeX渲染失败，使用备用方案:', formula, e);
            return renderMathFallback(formula, displayMode);
        }
    } else {
        return renderMathFallback(formula, displayMode);
    }
};

// 添加备用样式
const fallbackStyles = `
<style>
.math-inline-fallback {
    font-family: 'Times New Roman', serif;
    font-style: italic;
    color: #2c3e50;
    background: rgba(52, 152, 219, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.math-display-fallback {
    font-family: 'Times New Roman', serif;
    font-style: italic;
    color: #2c3e50;
    background: rgba(52, 152, 219, 0.1);
    padding: 12px;
    border-radius: 6px;
    border: 1px solid rgba(52, 152, 219, 0.2);
    display: block;
    text-align: center;
    margin: 16px 0;
    font-size: 1.1em;
}

.math-display-fallback::before {
    content: "📐 ";
    opacity: 0.6;
}

.math-inline-fallback::before {
    content: "📐 ";
    opacity: 0.6;
    font-size: 0.8em;
}

/* Cases环境样式 */
.cases-container {
    display: flex;
    align-items: center;
    margin: 8px 0;
}

.cases-brace {
    font-size: 2em;
    margin-right: 8px;
    color: #2c3e50;
}

.cases-content {
    display: flex;
    flex-direction: column;
}

.case-item {
    margin: 2px 0;
    padding: 2px 0;
}

/* Align环境样式 */
.align-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 8px 0;
}

.align-item {
    margin: 4px 0;
    text-align: center;
}

/* Matrix环境样式 */
.matrix-container {
    display: inline-flex;
    align-items: center;
    margin: 4px;
}

.matrix-bracket-left,
.matrix-bracket-right {
    font-size: 1.5em;
    margin: 0 4px;
    color: #2c3e50;
}

.matrix-table,
.array-table {
    border-collapse: collapse;
    margin: 0 4px;
}

.matrix-table td,
.array-table td {
    padding: 4px 8px;
    text-align: center;
    border: none;
}

.matrix-table tr,
.array-table tr {
    border: none;
}
</style>
`;

// 注入样式
if (document.head) {
    document.head.insertAdjacentHTML('beforeend', fallbackStyles);
} else {
    document.addEventListener('DOMContentLoaded', function() {
        document.head.insertAdjacentHTML('beforeend', fallbackStyles);
    });
}
