/**
 * 通用Markdown解析器
 * 支持标题、代码块、数学公式、列表等格式
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class MarkdownParser {
    constructor(options = {}) {
        this.options = {
            // 是否启用数学公式渲染
            enableMath: options.enableMath !== false,
            // 是否启用代码高亮
            enableCodeHighlight: options.enableCodeHighlight !== false,
            // 是否启用表格
            enableTable: options.enableTable !== false,
            // 自定义渲染器
            customRenderers: options.customRenderers || {},
            ...options
        };
    }

    /**
     * 解析Markdown文本为HTML
     * @param {string} text - Markdown文本
     * @returns {string} - 解析后的HTML
     */
    parse(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        let html = text;
        
        // 保护特殊内容，避免被其他规则影响
        const protectedContent = [];
        let contentIndex = 0;

        // 1. 保护数学公式
        if (this.options.enableMath) {
            html = this._protectMathFormulas(html, protectedContent, contentIndex);
            contentIndex = protectedContent.length;
        }

        // 2. 保护代码块
        html = this._protectCodeBlocks(html, protectedContent, contentIndex);
        contentIndex = protectedContent.length;

        // 3. 保护行内代码
        html = this._protectInlineCode(html, protectedContent, contentIndex);
        contentIndex = protectedContent.length;

        // 4. 处理标题
        html = this._parseHeaders(html);

        // 5. 处理表格
        if (this.options.enableTable) {
            html = this._parseTables(html);
        }

        // 6. 处理列表
        html = this._parseLists(html);

        // 7. 处理链接
        html = this._parseLinks(html);

        // 8. 处理粗体和斜体
        html = this._parseEmphasis(html);

        // 9. 处理换行
        html = this._parseLineBreaks(html);

        // 10. 恢复保护的内容
        html = this._restoreProtectedContent(html, protectedContent);

        return html;
    }

    /**
     * 保护数学公式
     */
    _protectMathFormulas(html, protectedContent, startIndex) {
        let index = startIndex;

        // 行内数学公式 \( ... \)
        html = html.replace(/\\\((.*?)\\\)/g, (match, formula) => {
            const placeholder = `__PROTECTED_${index}__`;
            protectedContent[index] = {
                type: 'math-inline',
                content: formula.trim(),
                placeholder: placeholder
            };
            index++;
            return placeholder;
        });

        // 块级数学公式 \[ ... \]
        html = html.replace(/\\\[(.*?)\\\]/gs, (match, formula) => {
            const placeholder = `__PROTECTED_${index}__`;
            protectedContent[index] = {
                type: 'math-block',
                content: formula.trim(),
                placeholder: placeholder
            };
            index++;
            return placeholder;
        });

        // $$ ... $$ 块级公式
        html = html.replace(/\$\$(.*?)\$\$/gs, (match, formula) => {
            const placeholder = `__PROTECTED_${index}__`;
            protectedContent[index] = {
                type: 'math-block',
                content: formula.trim(),
                placeholder: placeholder
            };
            index++;
            return placeholder;
        });

        // $ ... $ 行内公式
        html = html.replace(/\$([^$\n]+)\$/g, (match, formula) => {
            const placeholder = `__PROTECTED_${index}__`;
            protectedContent[index] = {
                type: 'math-inline',
                content: formula.trim(),
                placeholder: placeholder
            };
            index++;
            return placeholder;
        });

        // LaTeX环境 \begin{...} ... \end{...}
        html = html.replace(/\\begin\{([^}]+)\}([\s\S]*?)\\end\{\1\}/g, (match, envName, content) => {
            console.log('LaTeX环境匹配:', { match, envName, content });
            const placeholder = `__PROTECTED_${index}__`;
            protectedContent[index] = {
                type: 'math-environment',
                content: match, // 保存完整的环境内容
                placeholder: placeholder,
                environment: envName
            };
            index++;
            return placeholder;
        });

        return html;
    }

    /**
     * 保护代码块
     */
    _protectCodeBlocks(html, protectedContent, startIndex) {
        let index = startIndex;

        html = html.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, lang, code) => {
            const placeholder = `__PROTECTED_${index}__`;
            protectedContent[index] = {
                type: 'code-block',
                content: code.trim(),
                language: lang || '',
                placeholder: placeholder
            };
            index++;
            return placeholder;
        });

        return html;
    }

    /**
     * 保护行内代码
     */
    _protectInlineCode(html, protectedContent, startIndex) {
        let index = startIndex;

        html = html.replace(/`([^`]+)`/g, (match, code) => {
            const placeholder = `__PROTECTED_${index}__`;
            protectedContent[index] = {
                type: 'code-inline',
                content: code,
                placeholder: placeholder
            };
            index++;
            return placeholder;
        });

        return html;
    }

    /**
     * 解析标题
     */
    _parseHeaders(html) {
        // 从6级到1级，避免匹配冲突
        html = html.replace(/^###### (.*$)/gim, '<h6>$1</h6>');
        html = html.replace(/^##### (.*$)/gim, '<h5>$1</h5>');
        html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>');
        html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
        html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
        html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
        return html;
    }

    /**
     * 解析表格
     */
    _parseTables(html) {
        // 简单的表格解析
        const tableRegex = /^\|(.+)\|\s*\n\|[-\s|:]+\|\s*\n((?:\|.+\|\s*\n?)*)/gm;
        
        html = html.replace(tableRegex, (match, header, rows) => {
            const headerCells = header.split('|').map(cell => cell.trim()).filter(cell => cell);
            const headerHtml = headerCells.map(cell => `<th>${cell}</th>`).join('');
            
            const rowsHtml = rows.trim().split('\n').map(row => {
                const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell);
                return `<tr>${cells.map(cell => `<td>${cell}</td>`).join('')}</tr>`;
            }).join('');
            
            return `<table><thead><tr>${headerHtml}</tr></thead><tbody>${rowsHtml}</tbody></table>`;
        });
        
        return html;
    }

    /**
     * 解析列表
     */
    _parseLists(html) {
        // 无序列表
        html = html.replace(/^\* (.*$)/gim, '<li>$1</li>');
        html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
        
        // 有序列表
        html = html.replace(/^\d+\. (.*$)/gim, '<li>$1</li>');
        
        return html;
    }

    /**
     * 解析链接
     */
    _parseLinks(html) {
        // [文本](链接)
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
        return html;
    }

    /**
     * 解析粗体和斜体
     */
    _parseEmphasis(html) {
        // 粗体
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        // 斜体
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
        return html;
    }

    /**
     * 解析换行
     */
    _parseLineBreaks(html) {
        return html.replace(/\n/g, '<br>');
    }

    /**
     * 恢复保护的内容
     */
    _restoreProtectedContent(html, protectedContent) {
        protectedContent.forEach((item, index) => {
            if (!item) return;

            let restoredContent = '';

            switch (item.type) {
                case 'math-inline':
                    restoredContent = this._renderMathInline(item.content);
                    break;
                case 'math-block':
                    restoredContent = this._renderMathBlock(item.content);
                    break;
                case 'math-environment':
                    restoredContent = this._renderMathEnvironment(item.content);
                    break;
                case 'code-block':
                    restoredContent = this._renderCodeBlock(item.content, item.language);
                    break;
                case 'code-inline':
                    restoredContent = this._renderCodeInline(item.content);
                    break;
                default:
                    restoredContent = item.content;
            }

            html = html.replace(item.placeholder, restoredContent);
        });

        return html;
    }

    /**
     * 渲染行内数学公式
     */
    _renderMathInline(formula) {
        if (typeof renderMathWithFallback !== 'undefined') {
            return renderMathWithFallback(formula, false);
        } else if (typeof katex !== 'undefined') {
            try {
                return katex.renderToString(formula, {
                    displayMode: false,
                    throwOnError: false
                });
            } catch (e) {
                console.warn('数学公式渲染失败:', formula, e);
            }
        }
        return `<span class="math-fallback">\\(${formula}\\)</span>`;
    }

    /**
     * 渲染块级数学公式
     */
    _renderMathBlock(formula) {
        if (typeof renderMathWithFallback !== 'undefined') {
            return renderMathWithFallback(formula, true);
        } else if (typeof katex !== 'undefined') {
            try {
                return katex.renderToString(formula, {
                    displayMode: true,
                    throwOnError: false
                });
            } catch (e) {
                console.warn('数学公式渲染失败:', formula, e);
            }
        }
        return `<div class="math-fallback-block">\\[${formula}\\]</div>`;
    }

    /**
     * 渲染LaTeX环境
     */
    _renderMathEnvironment(latexContent) {
        console.log('渲染LaTeX环境:', latexContent);
        console.log('renderMathWithFallback可用:', typeof renderMathWithFallback !== 'undefined');
        console.log('processLaTeXEnvironments可用:', typeof processLaTeXEnvironments !== 'undefined');

        if (typeof renderMathWithFallback !== 'undefined') {
            const result = renderMathWithFallback(latexContent, true);
            console.log('renderMathWithFallback结果:', result);
            return result;
        } else if (typeof katex !== 'undefined') {
            try {
                const result = katex.renderToString(latexContent, {
                    displayMode: true,
                    throwOnError: false
                });
                console.log('KaTeX结果:', result);
                return result;
            } catch (e) {
                console.warn('LaTeX环境渲染失败:', latexContent, e);
            }
        }
        // 使用备用方案处理LaTeX环境
        if (typeof processLaTeXEnvironments !== 'undefined') {
            const processed = processLaTeXEnvironments(latexContent);
            const result = `<div class="math-fallback-block">${processed}</div>`;
            console.log('备用方案结果:', result);
            return result;
        }
        console.log('使用原始内容');
        return `<div class="math-fallback-block">${latexContent}</div>`;
    }

    /**
     * 渲染代码块
     */
    _renderCodeBlock(code, language) {
        const escapedCode = this._escapeHtml(code);
        const langClass = language ? ` class="language-${language}"` : '';
        return `<pre><code${langClass}>${escapedCode}</code></pre>`;
    }

    /**
     * 渲染行内代码
     */
    _renderCodeInline(code) {
        return `<code>${this._escapeHtml(code)}</code>`;
    }

    /**
     * HTML转义
     */
    _escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 创建默认实例
const markdownParser = new MarkdownParser();

// 导出便捷方法
window.parseMarkdown = function(text, options) {
    try {
        if (options) {
            const parser = new MarkdownParser(options);
            return parser.parse(text);
        }
        return markdownParser.parse(text);
    } catch (e) {
        console.error('parseMarkdown错误:', e);
        console.error('输入文本:', text);
        // 返回原始文本作为备用方案
        return text;
    }
};

// 导出类
window.MarkdownParser = MarkdownParser;
