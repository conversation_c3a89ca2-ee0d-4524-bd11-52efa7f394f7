Vue.component('word-match-game', {
  template: `
    <div class="word-match-game">
      <div class="game-header">
        <h2>单词消消乐</h2>
        <div class="score">得分: {{ score }}</div>
        <div class="timer" :class="{ 'warning': timeLeft <= 10 }">时间: {{ timeLeft }}秒</div>
      </div>

      <div class="difficulty-selector" v-if="!isPlaying">
        <h3>选择难度：</h3>
        <div class="difficulty-options">
          <button 
            v-for="(level, index) in difficultyLevels" 
            :key="index"
            :class="{ active: selectedDifficulty === level.value }"
            @click="selectDifficulty(level.value)">
            {{ level.label }}
          </button>
        </div>
      </div>
      
      <div class="game-board">
        <div v-for="(row, rowIndex) in board" 
             :key="'row-' + rowIndex" 
             class="board-row">
          <div v-for="(cell, colIndex) in row" 
               :key="'cell-' + rowIndex + '-' + colIndex"
               :class="['cell', { 
                 'selected': isSelected(rowIndex, colIndex),
                 'matched': cell.matched,
                 'hidden': cell.matched,
                 'hint': cell.hint
               }]"
               @click="selectCell(rowIndex, colIndex)">
            {{ cell.matched ? '' : cell.letter }}
          </div>
        </div>
      </div>

      <div class="controls">
        <button @click="startGame" :disabled="isPlaying">开始游戏</button>
        <button @click="resetGame">重新开始</button>
        <button @click="showHint" :disabled="!isPlaying || hintCount <= 0">
          提示 (剩余: {{ hintCount }})
        </button>
      </div>

      <div class="word-list">
        <h3>可匹配的单词 ({{ remainingWords.length }}/{{ totalWords }}个)：</h3>
        <div class="words">
          <span v-for="(word, index) in remainingWords" 
                :key="'word-' + index" 
                class="word-item"
                :class="{ 'hint': word.hint }">{{ word }}</span>
        </div>
      </div>

      <div v-if="showMessage" :class="['message', messageType]">
        {{ message }}
      </div>

      <div v-if="showGameOver" class="game-over-modal">
        <div class="modal-content">
          <h2>{{ gameOverTitle }}</h2>
          <p>{{ gameOverMessage }}</p>
          <div class="modal-buttons">
            <button @click="restartGame">再玩一次</button>
            <button @click="closeGameOver">关闭</button>
          </div>
        </div>
      </div>
    </div>
  `,
  data() {
    return {
      board: [],
      selectedCells: [],
      score: 0,
      timeLeft: 60,
      isPlaying: false,
      timer: null,
      selectedDifficulty: 'easy',
      difficultyLevels: [
        { label: '简单', value: 'easy', wordCount: 10 },
        { label: '中等', value: 'medium', wordCount: 20 },
        { label: '困难', value: 'hard', wordCount: 30 }
      ],
      allWords: {
        three: [
          'CAT', 'DOG', 'SUN', 'RUN', 'HOT', 'COLD', 'RED', 'BLUE', 'GREEN',
          'BIG', 'SMALL', 'NEW', 'OLD', 'BAD', 'GOOD', 'YES', 'NO', 'ONE',
          'TWO', 'SIX', 'TEN', 'DAY', 'NIGHT', 'MAN', 'WOMAN', 'BOY', 'GIRL'
        ],
        four: [
          'APPLE', 'BIRD', 'FISH', 'TREE', 'BOOK', 'DESK', 'CHAIR', 'DOOR',
          'WINDOW', 'HOUSE', 'ROOM', 'TIME', 'LIFE', 'WORK', 'PLAY', 'STUDY',
          'READ', 'WRITE', 'TALK', 'WALK', 'JUMP', 'SWIM', 'SING', 'DANCE'
        ],
        five: [
          'WATER', 'EARTH', 'SMILE', 'LAUGH', 'SLEEP', 'DREAM', 'HAPPY', 'SAD',
          'ANGRY', 'SMILE', 'PEACE', 'LOVE', 'HOPE', 'FAITH', 'TRUTH', 'BEAUTY',
          'MUSIC', 'SPORT', 'GAMES', 'STORY', 'MOVIE', 'PHONE', 'EMAIL', 'INTERNET'
        ]
      },
      matchedWords: new Set(),
      showMessage: false,
      message: '',
      messageType: 'success',
      showGameOver: false,
      gameOverTitle: '',
      gameOverMessage: '',
      hintCount: 3,
      cachedWords: null,
      lastDifficulty: null
    }
  },
  computed: {
    currentWords() {
      if (this.cachedWords && this.lastDifficulty === this.selectedDifficulty) {
        return this.cachedWords;
      }

      const level = this.difficultyLevels.find(l => l.value === this.selectedDifficulty);
      const wordCount = level ? level.wordCount : 10;

      const allWords = [
        ...this.allWords.three,
        ...this.allWords.four,
        ...this.allWords.five
      ];

      this.cachedWords = this.shuffleArray(allWords).slice(0, wordCount);
      this.lastDifficulty = this.selectedDifficulty;
      return this.cachedWords;
    },
    remainingWords() {
      return this.currentWords.filter(word => !this.matchedWords.has(word));
    },
    totalWords() {
      return this.currentWords.length;
    }
  },
  methods: {
    showMatchMessage(text, type = 'success') {
      this.message = text;
      this.messageType = type;
      this.showMessage = true;
      setTimeout(() => {
        this.showMessage = false;
      }, 2000);
    },

    selectDifficulty(level) {
      this.selectedDifficulty = level;
      this.matchedWords.clear();
      this.cachedWords = null;
    },

    shuffleArray(array) {
      const newArray = [...array];
      for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
      }
      return newArray;
    },

    initBoard() {
      const size = 8;
      this.board = Array(size).fill().map(() =>
        Array(size).fill().map(() => ({
          letter: '',
          matched: false,
          hint: false
        }))
      );

      const words = this.currentWords;
      const directions = ['horizontal', 'vertical'];

      words.forEach(word => {
        let placed = false;
        let attempts = 0;
        const maxAttempts = 100;

        while (!placed && attempts < maxAttempts) {
          const direction = directions[Math.floor(Math.random() * directions.length)];
          const startRow = Math.floor(Math.random() * size);
          const startCol = Math.floor(Math.random() * size);

          if (this.canPlaceWord(word, startRow, startCol, direction)) {
            this.placeWord(word, startRow, startCol, direction);
            placed = true;
          }
          attempts++;
        }

        if (!placed) {
          console.warn(`无法放置单词: ${word}`);
        }
      });

      // 填充剩余空格
      for (let i = 0; i < size; i++) {
        for (let j = 0; j < size; j++) {
          if (this.board[i][j].letter === '') {
            this.board[i][j].letter = this.getRandomLetter();
          }
        }
      }
    },

    canPlaceWord(word, startRow, startCol, direction) {
      const size = this.board.length;

      if (direction === 'horizontal') {
        if (startCol + word.length > size) return false;
        for (let i = 0; i < word.length; i++) {
          if (this.board[startRow][startCol + i].letter !== '') return false;
        }
      } else {
        if (startRow + word.length > size) return false;
        for (let i = 0; i < word.length; i++) {
          if (this.board[startRow + i][startCol].letter !== '') return false;
        }
      }

      return true;
    },

    placeWord(word, startRow, startCol, direction) {
      if (direction === 'horizontal') {
        for (let i = 0; i < word.length; i++) {
          this.board[startRow][startCol + i].letter = word[i];
        }
      } else {
        for (let i = 0; i < word.length; i++) {
          this.board[startRow + i][startCol].letter = word[i];
        }
      }
    },

    getRandomLetter() {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      return letters[Math.floor(Math.random() * letters.length)];
    },

    selectCell(row, col) {
      if (!this.isPlaying) return;

      const cell = this.board[row][col];
      if (cell.matched) return;

      const cellIndex = this.selectedCells.findIndex(
        c => c.row === row && c.col === col
      );

      if (cellIndex === -1) {
        this.selectedCells.push({ row, col });
      } else {
        this.selectedCells.splice(cellIndex, 1);
      }

      if (this.selectedCells.length >= 3) {
        this.checkWord();
      }
    },

    isSelected(row, col) {
      return this.selectedCells.some(
        cell => cell.row === row && cell.col === col
      );
    },

    checkWord() {
      const word = this.selectedCells
        .map(cell => this.board[cell.row][cell.col].letter)
        .join('');

      const possibleWords = this.currentWords.filter(w => w.length === word.length);

      if (possibleWords.includes(word)) {
        if (this.matchedWords.has(word)) {
          this.showMatchMessage('这个单词已经匹配过了！', 'error');
        } else {
          this.score += word.length * 10;
          this.matchedWords.add(word);
          this.selectedCells.forEach(cell => {
            this.board[cell.row][cell.col].matched = true;
          });
          this.showMatchMessage(`匹配成功！+${word.length * 10}分 (${word})`);

          if (this.matchedWords.size === this.currentWords.length) {
            this.endGame(true);
          }
        }
      } else {
        this.showMatchMessage('未找到匹配的单词！', 'error');
      }

      this.selectedCells = [];
    },

    showHint() {
      if (this.hintCount <= 0) return;

      const remainingWords = this.remainingWords;
      if (remainingWords.length === 0) return;

      const randomWord = remainingWords[Math.floor(Math.random() * remainingWords.length)];
      this.hintCount--;

      // 在板上高亮显示提示单词
      for (let i = 0; i < this.board.length; i++) {
        for (let j = 0; j < this.board[i].length; j++) {
          if (this.board[i][j].letter === randomWord[0]) {
            // 检查水平和垂直方向
            if (this.checkWordAtPosition(randomWord, i, j, 'horizontal') ||
              this.checkWordAtPosition(randomWord, i, j, 'vertical')) {
              this.showMatchMessage(`提示：找到单词 ${randomWord}`, 'success');
              return;
            }
          }
        }
      }
    },

    checkWordAtPosition(word, row, col, direction) {
      const size = this.board.length;
      let found = true;

      if (direction === 'horizontal') {
        if (col + word.length > size) return false;
        for (let i = 0; i < word.length; i++) {
          if (this.board[row][col + i].letter !== word[i]) {
            found = false;
            break;
          }
        }
      } else {
        if (row + word.length > size) return false;
        for (let i = 0; i < word.length; i++) {
          if (this.board[row + i][col].letter !== word[i]) {
            found = false;
            break;
          }
        }
      }

      if (found) {
        // 高亮显示单词
        if (direction === 'horizontal') {
          for (let i = 0; i < word.length; i++) {
            this.board[row][col + i].hint = true;
          }
        } else {
          for (let i = 0; i < word.length; i++) {
            this.board[row + i][col].hint = true;
          }
        }
        setTimeout(() => {
          if (direction === 'horizontal') {
            for (let i = 0; i < word.length; i++) {
              this.board[row][col + i].hint = false;
            }
          } else {
            for (let i = 0; i < word.length; i++) {
              this.board[row + i][col].hint = false;
            }
          }
        }, 2000);
      }

      return found;
    },

    startGame() {
      this.isPlaying = true;
      this.score = 0;
      this.timeLeft = 60;
      this.matchedWords.clear();
      this.hintCount = 3;
      this.initBoard();

      if (this.timer) {
        clearInterval(this.timer);
      }

      this.timer = setInterval(() => {
        if (this.timeLeft > 0) {
          this.timeLeft--;
        } else {
          this.endGame(false);
        }
      }, 1000);
    },

    resetGame() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.endGame(false);
      this.initBoard();
    },

    endGame(isComplete) {
      this.isPlaying = false;
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }

      this.gameOverTitle = isComplete ? '恭喜！' : '游戏结束！';
      this.gameOverMessage = isComplete
        ? `你完成了所有单词！\n最终得分: ${this.score}`
        : `你的得分是: ${this.score}\n已匹配单词: ${this.matchedWords.size}/${this.totalWords}`;

      this.showGameOver = true;
    },

    restartGame() {
      this.showGameOver = false;
      this.startGame();
    },

    closeGameOver() {
      this.showGameOver = false;
    }
  },

  created() {
    this.initBoard();
  },

  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }
}); 