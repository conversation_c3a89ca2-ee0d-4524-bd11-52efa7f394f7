<template>
  <div class="word-match-game">
    <div class="game-header">
      <h2>单词消消乐</h2>
      <div class="score">得分: {{ score }}</div>
      <div class="timer">时间: {{ timeLeft }}秒</div>
    </div>
    
    <div class="game-board">
      <div v-for="(row, rowIndex) in board" 
           :key="rowIndex" 
           class="board-row">
        <div v-for="(cell, colIndex) in row" 
             :key="colIndex"
             :class="['cell', { 'selected': isSelected(rowIndex, colIndex) }]"
             @click="selectCell(rowIndex, colIndex)">
          {{ cell.letter }}
        </div>
      </div>
    </div>

    <div class="controls">
      <button @click="startGame" :disabled="isPlaying">开始游戏</button>
      <button @click="resetGame">重新开始</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WordMatchGame',
  data() {
    return {
      board: [],
      selectedCells: [],
      score: 0,
      timeLeft: 60,
      isPlaying: false,
      timer: null,
      words: [
        'APPLE', 'BANANA', 'CAT', 'DOG', 'ELEPHANT',
        'FISH', 'GIRAFFE', 'HOUSE', 'ICE', 'JUICE',
        'KITE', 'LION', 'MONKEY', 'NEST', 'ORANGE'
      ]
    }
  },
  methods: {
    initBoard() {
      const size = 8;
      this.board = Array(size).fill().map(() => 
        Array(size).fill().map(() => ({
          letter: this.getRandomLetter(),
          matched: false
        }))
      );
    },
    getRandomLetter() {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      return letters[Math.floor(Math.random() * letters.length)];
    },
    selectCell(row, col) {
      if (!this.isPlaying) return;
      
      const cell = this.board[row][col];
      if (cell.matched) return;

      const cellIndex = this.selectedCells.findIndex(
        c => c.row === row && c.col === col
      );

      if (cellIndex === -1) {
        this.selectedCells.push({ row, col });
      } else {
        this.selectedCells.splice(cellIndex, 1);
      }

      if (this.selectedCells.length === 3) {
        this.checkWord();
      }
    },
    isSelected(row, col) {
      return this.selectedCells.some(
        cell => cell.row === row && cell.col === col
      );
    },
    checkWord() {
      const word = this.selectedCells
        .map(cell => this.board[cell.row][cell.col].letter)
        .join('');

      if (this.words.includes(word)) {
        this.score += 10;
        this.selectedCells.forEach(cell => {
          this.board[cell.row][cell.col].matched = true;
        });
      }

      this.selectedCells = [];
    },
    startGame() {
      this.isPlaying = true;
      this.score = 0;
      this.timeLeft = 60;
      this.initBoard();
      this.timer = setInterval(() => {
        if (this.timeLeft > 0) {
          this.timeLeft--;
        } else {
          this.endGame();
        }
      }, 1000);
    },
    resetGame() {
      this.endGame();
      this.initBoard();
    },
    endGame() {
      this.isPlaying = false;
      clearInterval(this.timer);
      alert(`游戏结束！你的得分是: ${this.score}`);
    }
  },
  created() {
    this.initBoard();
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
}
</script>

<style scoped>
.word-match-game {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.game-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.game-board {
  display: grid;
  gap: 5px;
  margin-bottom: 20px;
}

.board-row {
  display: flex;
  gap: 5px;
}

.cell {
  width: 50px;
  height: 50px;
  border: 2px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  background-color: #fff;
  transition: all 0.3s;
}

.cell.selected {
  background-color: #e3f2fd;
  border-color: #2196f3;
}

.cell.matched {
  background-color: #c8e6c9;
  border-color: #4caf50;
}

.controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}

button {
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
</style> 