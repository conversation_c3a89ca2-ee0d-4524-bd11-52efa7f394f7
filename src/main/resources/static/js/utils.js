/**
 * 通用工具函数库
 */

// API基础配置
const API_CONFIG = {
    baseURL: '/api',
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json'
    }
};

// HTTP请求工具
const http = {
    // GET请求
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        try {
            const response = await fetch(fullUrl, {
                method: 'GET',
                headers: API_CONFIG.headers
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    },

    // POST请求
    async post(url, data = {}) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: API_CONFIG.headers,
                body: JSON.stringify(data)
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    },

    // PUT请求
    async put(url, data = {}) {
        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: API_CONFIG.headers,
                body: JSON.stringify(data)
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    },

    // DELETE请求
    async delete(url) {
        try {
            const response = await fetch(url, {
                method: 'DELETE',
                headers: API_CONFIG.headers
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    },

    // 文件上传
    async upload(url, formData) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    },

    // 处理响应
    async handleResponse(response) {
        if (!response.ok) {
            const error = await response.json().catch(() => ({ message: '请求失败' }));
            throw new Error(error.message || `HTTP ${response.status}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        }
        return await response.text();
    },

    // 处理错误
    handleError(error) {
        console.error('HTTP请求错误:', error);
        return error;
    }
};

// 本地存储工具
const storage = {
    // 设置数据
    set(key, value, expiry = null) {
        const data = {
            value: value,
            expiry: expiry ? Date.now() + expiry : null
        };
        localStorage.setItem(key, JSON.stringify(data));
    },

    // 获取数据
    get(key) {
        const item = localStorage.getItem(key);
        if (!item) return null;

        try {
            const data = JSON.parse(item);
            
            // 检查是否过期
            if (data.expiry && Date.now() > data.expiry) {
                localStorage.removeItem(key);
                return null;
            }
            
            return data.value;
        } catch (error) {
            console.error('解析存储数据失败:', error);
            return null;
        }
    },

    // 删除数据
    remove(key) {
        localStorage.removeItem(key);
    },

    // 清空所有数据
    clear() {
        localStorage.clear();
    },

    // 获取所有键
    keys() {
        return Object.keys(localStorage);
    }
};

// 日期时间工具
const dateUtils = {
    // 格式化日期
    format(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    // 相对时间
    relative(date) {
        const now = new Date();
        const target = new Date(date);
        const diff = now - target;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days}天前`;
        if (hours > 0) return `${hours}小时前`;
        if (minutes > 0) return `${minutes}分钟前`;
        return '刚刚';
    },

    // 是否是今天
    isToday(date) {
        const today = new Date();
        const target = new Date(date);
        return today.toDateString() === target.toDateString();
    }
};

// 字符串工具
const stringUtils = {
    // 截断字符串
    truncate(str, length = 50, suffix = '...') {
        if (str.length <= length) return str;
        return str.substring(0, length) + suffix;
    },

    // 首字母大写
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    },

    // 驼峰转下划线
    camelToSnake(str) {
        return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    },

    // 下划线转驼峰
    snakeToCamel(str) {
        return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
    },

    // 生成随机字符串
    random(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    // 生成UUID
    uuid() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
};

// 文件工具
const fileUtils = {
    // 格式化文件大小
    formatSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 获取文件扩展名
    getExtension(filename) {
        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
    },

    // 获取文件名（不含扩展名）
    getBasename(filename) {
        return filename.substring(0, filename.lastIndexOf('.'));
    },

    // 检查文件类型
    isImage(filename) {
        const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        const ext = this.getExtension(filename).toLowerCase();
        return imageExts.includes(ext);
    },

    isDocument(filename) {
        const docExts = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
        const ext = this.getExtension(filename).toLowerCase();
        return docExts.includes(ext);
    },

    // 读取文件内容
    async readAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    },

    // 读取文件为Base64
    async readAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }
};

// 验证工具
const validator = {
    // 邮箱验证
    email(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    // 手机号验证
    phone(phone) {
        const re = /^1[3-9]\d{9}$/;
        return re.test(phone);
    },

    // URL验证
    url(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    // 身份证验证
    idCard(idCard) {
        const re = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        return re.test(idCard);
    },

    // 密码强度验证
    passwordStrength(password) {
        let score = 0;
        if (password.length >= 8) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/\d/.test(password)) score++;
        if (/[^a-zA-Z\d]/.test(password)) score++;
        
        if (score < 2) return 'weak';
        if (score < 4) return 'medium';
        return 'strong';
    }
};

// DOM工具
const domUtils = {
    // 查询元素
    $(selector) {
        return document.querySelector(selector);
    },

    // 查询所有元素
    $$(selector) {
        return document.querySelectorAll(selector);
    },

    // 添加类名
    addClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.classList.add(className);
        }
    },

    // 移除类名
    removeClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.classList.remove(className);
        }
    },

    // 切换类名
    toggleClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.classList.toggle(className);
        }
    },

    // 设置样式
    setStyle(element, styles) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            Object.assign(element.style, styles);
        }
    },

    // 滚动到元素
    scrollTo(element, behavior = 'smooth') {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.scrollIntoView({ behavior });
        }
    }
};

// 防抖函数
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 导出工具对象
window.utils = {
    http,
    storage,
    dateUtils,
    stringUtils,
    fileUtils,
    validator,
    domUtils,
    debounce,
    throttle
};
