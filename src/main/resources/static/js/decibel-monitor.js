/**
 * 课堂分贝检测器
 * 使用Web Audio API实时检测音频分贝值
 */

class DecibelMonitor {
    constructor() {
        this.audioContext = null;
        this.microphone = null;
        this.analyser = null;
        this.dataArray = null;
        this.isMonitoring = false;
        this.animationId = null;

        // 会话管理
        this.sessionId = null;
        this.recordBuffer = []; // 缓存分贝数据，批量发送
        this.lastSyncTime = 0;
        this.syncInterval = 5000; // 5秒同步一次

        // 统计数据
        this.stats = {
            startTime: null,
            totalTime: 0,
            activeTime: 0,
            maxDecibel: 0,
            decibelHistory: [],
            lastActiveCheck: null
        };

        // 阈值设置
        this.thresholds = {
            quiet: 40,
            normal: 60,
            active: 80
        };
        
        // DOM元素
        this.elements = {
            decibelValue: document.getElementById('decibelValue'),
            statusText: document.getElementById('statusText'),
            levelBar: document.getElementById('levelBar'),
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            calibrateBtn: document.getElementById('calibrateBtn'),
            permissionNotice: document.getElementById('permissionNotice'),
            errorMessage: document.getElementById('errorMessage'),
            avgDecibel: document.getElementById('avgDecibel'),
            maxDecibel: document.getElementById('maxDecibel'),
            activeTime: document.getElementById('activeTime'),
            totalTime: document.getElementById('totalTime')
        };
        
        this.init();
    }
    
    init() {
        // 检查浏览器支持
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            this.showError('您的浏览器不支持麦克风访问功能');
            return;
        }
        
        // 初始化音频上下文
        try {
            window.AudioContext = window.AudioContext || window.webkitAudioContext;
            this.audioContext = new AudioContext();
        } catch (e) {
            this.showError('无法初始化音频上下文：' + e.message);
        }
    }
    
    async requestMicrophonePermission() {
        try {
            this.hideError();
            this.elements.permissionNotice.style.display = 'block';
            
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false
                }
            });
            
            this.elements.permissionNotice.style.display = 'none';
            return stream;
        } catch (error) {
            this.elements.permissionNotice.style.display = 'none';
            
            let errorMessage = '无法访问麦克风：';
            switch (error.name) {
                case 'NotAllowedError':
                    errorMessage += '用户拒绝了麦克风权限';
                    break;
                case 'NotFoundError':
                    errorMessage += '未找到麦克风设备';
                    break;
                case 'NotReadableError':
                    errorMessage += '麦克风被其他应用占用';
                    break;
                default:
                    errorMessage += error.message;
            }
            
            this.showError(errorMessage);
            throw error;
        }
    }
    
    setupAudioProcessing(stream) {
        // 创建音频源
        this.microphone = this.audioContext.createMediaStreamSource(stream);

        // 创建分析器
        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = 2048; // 增加FFT大小以提高精度
        this.analyser.smoothingTimeConstant = 0.3; // 降低平滑度以提高响应速度
        this.analyser.minDecibels = -90; // 设置最小分贝值
        this.analyser.maxDecibels = -10; // 设置最大分贝值

        // 连接音频节点
        this.microphone.connect(this.analyser);

        // 创建数据数组 - 使用时域数据进行RMS计算
        this.bufferLength = this.analyser.fftSize;
        this.dataArray = new Float32Array(this.bufferLength);

        // 校准参数
        this.calibrationOffset = 0; // 校准偏移量
        this.noiseFloor = -60; // 噪声底限（dB）
        this.isCalibrating = false; // 是否正在校准
        this.calibrationSamples = []; // 校准样本
    }
    
    calculateDecibel() {
        // 获取时域数据（波形数据）
        this.analyser.getFloatTimeDomainData(this.dataArray);

        // 计算RMS值（均方根）
        let sum = 0;
        for (let i = 0; i < this.dataArray.length; i++) {
            sum += this.dataArray[i] * this.dataArray[i];
        }
        const rms = Math.sqrt(sum / this.dataArray.length);

        // 转换为分贝值
        // 使用标准公式：dB = 20 * log10(rms / reference)
        // 参考值设为1.0（满量程），这样0dB对应满量程
        let webAudioDB = 20 * Math.log10(rms);

        // 应用校准和映射
        // 将Web Audio API的dB范围映射到实际环境分贝
        let decibel = this.mapToEnvironmentalDB(webAudioDB);

        // 应用校准偏移
        decibel += this.calibrationOffset;

        // 调试信息（每100次输出一次，避免控制台刷屏）
        if (this.debugCounter === undefined) this.debugCounter = 0;
        this.debugCounter++;
        if (this.debugCounter % 100 === 0) {
            console.log(`调试信息 - RMS: ${rms.toFixed(6)}, WebAudio dB: ${webAudioDB.toFixed(1)}, 映射后: ${(decibel - this.calibrationOffset).toFixed(1)}, 最终: ${decibel.toFixed(1)}`);
        }

        // 如果正在校准，收集样本
        if (this.isCalibrating) {
            this.calibrationSamples.push(decibel);
        }

        // 限制分贝范围
        decibel = Math.max(15, Math.min(120, decibel));

        return Math.round(decibel * 10) / 10; // 保留一位小数
    }

    /**
     * 将Web Audio API的分贝值映射到环境分贝值
     * Web Audio API: -Infinity 到 0 dB
     * 环境分贝: 20 到 120 dB
     */
    mapToEnvironmentalDB(webAudioDB) {
        // 处理静音情况
        if (!isFinite(webAudioDB) || webAudioDB < -60) {
            return 20 + Math.random() * 5; // 静音时返回20-25dB（模拟环境噪声）
        }

        // 线性映射：-60dB -> 20dB, -10dB -> 85dB, 0dB -> 100dB
        // 使用分段线性映射以获得更真实的响应
        if (webAudioDB < -40) {
            // 静音到轻声：-60dB ~ -40dB -> 20dB ~ 35dB
            return 20 + (webAudioDB + 60) * 0.75;
        } else if (webAudioDB < -20) {
            // 轻声到正常：-40dB ~ -20dB -> 35dB ~ 60dB
            return 35 + (webAudioDB + 40) * 1.25;
        } else if (webAudioDB < -5) {
            // 正常到大声：-20dB ~ -5dB -> 60dB ~ 85dB
            return 60 + (webAudioDB + 20) * 1.67;
        } else {
            // 大声到很大声：-5dB ~ 0dB -> 85dB ~ 100dB
            return 85 + (webAudioDB + 5) * 3;
        }
    }
    
    updateDisplay(decibel) {
        // 更新分贝值显示
        this.elements.decibelValue.innerHTML = `${decibel}<span class="decibel-unit">dB</span>`;

        // 确定状态和颜色
        let status, statusClass, levelClass;
        if (decibel < this.thresholds.quiet) {
            status = '太安静了，请大声朗读！';
            statusClass = 'status-quiet';
            levelClass = 'level-quiet';
        } else if (decibel < this.thresholds.normal) {
            status = '音量正常，继续保持！';
            statusClass = 'status-normal';
            levelClass = 'level-normal';
        } else if (decibel < this.thresholds.active) {
            status = '很好！朗读很积极！';
            statusClass = 'status-active';
            levelClass = 'level-active';
        } else {
            status = '声音太大了，请适当降低音量';
            statusClass = 'status-loud';
            levelClass = 'level-loud';
        }

        // 更新状态文本
        this.elements.statusText.textContent = status;
        this.elements.statusText.className = `status-text ${statusClass}`;

        // 更新进度条
        const percentage = Math.min(100, (decibel / 100) * 100);
        this.elements.levelBar.style.width = `${percentage}%`;
        this.elements.levelBar.className = `level-bar ${levelClass}`;

        // 更新统计数据
        this.updateStats(decibel);

        // 缓存数据用于同步到服务器
        this.bufferDataForSync(decibel);
    }
    
    updateStats(decibel) {
        const now = Date.now();
        
        // 记录分贝历史
        this.stats.decibelHistory.push(decibel);
        
        // 更新最大分贝
        if (decibel > this.stats.maxDecibel) {
            this.stats.maxDecibel = decibel;
        }
        
        // 计算活跃时间（分贝大于正常阈值的时间）
        if (decibel >= this.thresholds.normal) {
            if (this.stats.lastActiveCheck) {
                this.stats.activeTime += now - this.stats.lastActiveCheck;
            }
        }
        this.stats.lastActiveCheck = now;
        
        // 计算总时间
        if (this.stats.startTime) {
            this.stats.totalTime = now - this.stats.startTime;
        }
        
        // 更新显示
        this.updateStatsDisplay();
    }
    
    updateStatsDisplay() {
        // 计算平均分贝
        const avgDecibel = this.stats.decibelHistory.length > 0 
            ? Math.round(this.stats.decibelHistory.reduce((a, b) => a + b, 0) / this.stats.decibelHistory.length)
            : 0;
        
        // 格式化时间
        const formatTime = (ms) => {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        };
        
        // 更新统计显示
        this.elements.avgDecibel.textContent = avgDecibel > 0 ? `${avgDecibel}dB` : '--';
        this.elements.maxDecibel.textContent = this.stats.maxDecibel > 0 ? `${this.stats.maxDecibel}dB` : '--';
        this.elements.activeTime.textContent = this.stats.totalTime > 0 ? formatTime(this.stats.activeTime) : '--';
        this.elements.totalTime.textContent = this.stats.totalTime > 0 ? formatTime(this.stats.totalTime) : '--';
    }
    
    animate() {
        if (!this.isMonitoring) return;
        
        const decibel = this.calculateDecibel();
        this.updateDisplay(decibel);
        
        this.animationId = requestAnimationFrame(() => this.animate());
    }
    
    async start() {
        try {
            // 创建新的监测会话
            await this.createSession();

            // 请求麦克风权限
            const stream = await this.requestMicrophonePermission();

            // 恢复音频上下文（某些浏览器需要用户交互）
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            // 设置音频处理
            this.setupAudioProcessing(stream);

            // 重置统计数据
            this.resetStats();

            // 开始监测
            this.isMonitoring = true;
            this.animate();

            // 更新UI
            this.updateButtonStates();

        } catch (error) {
            console.error('启动监测失败:', error);
        }
    }
    
    async stop() {
        this.isMonitoring = false;

        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }

        if (this.microphone) {
            this.microphone.disconnect();
            this.microphone = null;
        }

        if (this.analyser) {
            this.analyser.disconnect();
            this.analyser = null;
        }

        // 停止媒体流
        if (this.microphone && this.microphone.mediaStream) {
            this.microphone.mediaStream.getTracks().forEach(track => track.stop());
        }

        // 同步剩余数据并结束会话
        if (this.sessionId) {
            await this.syncDataToServer();
            await this.endSession();
        }

        // 更新UI
        this.updateButtonStates();
        this.elements.statusText.textContent = '检测已停止';
        this.elements.statusText.className = 'status-text';
        this.elements.decibelValue.innerHTML = '--<span class="decibel-unit">dB</span>';
        this.elements.levelBar.style.width = '0%';
    }
    
    resetStats() {
        this.stats = {
            startTime: Date.now(),
            totalTime: 0,
            activeTime: 0,
            maxDecibel: 0,
            decibelHistory: [],
            lastActiveCheck: Date.now()
        };
        this.updateStatsDisplay();
    }
    
    updateButtonStates() {
        if (this.isMonitoring) {
            this.elements.startBtn.disabled = true;
            this.elements.startBtn.classList.add('btn-disabled');
            this.elements.stopBtn.disabled = false;
            this.elements.stopBtn.classList.remove('btn-disabled');
            this.elements.calibrateBtn.disabled = false;
            this.elements.calibrateBtn.classList.remove('btn-disabled');
        } else {
            this.elements.startBtn.disabled = false;
            this.elements.startBtn.classList.remove('btn-disabled');
            this.elements.stopBtn.disabled = true;
            this.elements.stopBtn.classList.add('btn-disabled');
            this.elements.calibrateBtn.disabled = true;
            this.elements.calibrateBtn.classList.add('btn-disabled');
        }
    }
    
    showError(message) {
        this.elements.errorMessage.textContent = message;
        this.elements.errorMessage.style.display = 'block';
    }
    
    hideError() {
        this.elements.errorMessage.style.display = 'none';
    }

    /**
     * 开始校准
     * 在安静环境下运行，将当前读数校准到25dB
     */
    async startCalibration() {
        if (!this.isMonitoring) {
            this.showError('请先开始监测再进行校准');
            return;
        }

        this.isCalibrating = true;
        this.calibrationSamples = [];

        // 显示校准提示
        this.elements.statusText.textContent = '正在校准中，请保持安静...';
        this.elements.statusText.className = 'status-text';

        // 收集3秒的样本数据
        setTimeout(() => {
            this.finishCalibration();
        }, 3000);
    }

    /**
     * 完成校准
     */
    finishCalibration() {
        if (this.calibrationSamples.length === 0) {
            this.showError('校准失败：未收集到足够的样本数据');
            this.isCalibrating = false;
            return;
        }

        // 计算平均值
        const average = this.calibrationSamples.reduce((a, b) => a + b, 0) / this.calibrationSamples.length;

        // 设置校准偏移，使安静环境显示25dB
        this.calibrationOffset = 25 - average;

        this.isCalibrating = false;

        // 显示校准结果
        this.elements.statusText.textContent = `校准完成！偏移量：${this.calibrationOffset.toFixed(1)}dB`;
        this.elements.statusText.className = 'status-text status-normal';

        console.log(`校准完成：平均值=${average.toFixed(1)}dB，偏移量=${this.calibrationOffset.toFixed(1)}dB`);

        // 3秒后恢复正常显示
        setTimeout(() => {
            if (this.isMonitoring) {
                this.elements.statusText.textContent = '监测中...';
            }
        }, 3000);
    }

    /**
     * 重置校准
     */
    resetCalibration() {
        this.calibrationOffset = 0;
        this.isCalibrating = false;
        this.calibrationSamples = [];
        console.log('校准已重置');
    }
}

// 全局实例
let decibelMonitor;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    decibelMonitor = new DecibelMonitor();
});

// 全局函数供HTML调用
function startMonitoring() {
    if (decibelMonitor) {
        decibelMonitor.start();
    }
}

function stopMonitoring() {
    if (decibelMonitor) {
        decibelMonitor.stop();
    }
}

function startCalibration() {
    if (decibelMonitor) {
        decibelMonitor.startCalibration();
    }
}

function showSettings() {
    const currentOffset = decibelMonitor ? decibelMonitor.calibrationOffset.toFixed(1) : '0.0';
    alert(`设置信息：\n\n当前阈值：\n安静: < 40dB\n正常: 40-60dB\n活跃: 60-80dB\n响亮: > 80dB\n\n校准偏移量: ${currentOffset}dB\n\n使用说明：\n1. 在安静环境下点击"校准"按钮\n2. 保持3秒钟安静\n3. 系统会自动调整基准值`);
}

// 扩展DecibelMonitor类，添加服务器同步功能
DecibelMonitor.prototype.createSession = async function() {
    try {
        const response = await fetch('/api/classroom/decibel/session/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                thresholds: this.thresholds
            })
        });

        const result = await response.json();
        if (result.success) {
            this.sessionId = result.sessionId;
            console.log('创建监测会话成功:', this.sessionId);
        } else {
            console.warn('创建会话失败，将使用本地模式');
        }
    } catch (error) {
        console.warn('无法连接到服务器，将使用本地模式:', error);
    }
};

DecibelMonitor.prototype.bufferDataForSync = function(decibel) {
    if (!this.sessionId) return;

    const now = Date.now();
    this.recordBuffer.push({
        decibel: decibel,
        timestamp: now
    });

    // 定期同步数据
    if (now - this.lastSyncTime > this.syncInterval) {
        this.syncDataToServer();
        this.lastSyncTime = now;
    }
};

DecibelMonitor.prototype.syncDataToServer = async function() {
    if (!this.sessionId || this.recordBuffer.length === 0) return;

    try {
        const response = await fetch(`/api/classroom/decibel/session/${this.sessionId}/batch-record`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: this.recordBuffer
            })
        });

        const result = await response.json();
        if (result.success) {
            console.log(`同步 ${this.recordBuffer.length} 条数据到服务器`);
            this.recordBuffer = []; // 清空缓存
        }
    } catch (error) {
        console.warn('数据同步失败:', error);
    }
};

DecibelMonitor.prototype.endSession = async function() {
    if (!this.sessionId) return;

    try {
        const response = await fetch(`/api/classroom/decibel/session/${this.sessionId}/end`, {
            method: 'POST'
        });

        const result = await response.json();
        if (result.success) {
            console.log('会话结束成功:', result);
            this.sessionId = null;
        }
    } catch (error) {
        console.warn('结束会话失败:', error);
    }
};

DecibelMonitor.prototype.loadSessionStats = async function() {
    if (!this.sessionId) return;

    try {
        const response = await fetch(`/api/classroom/decibel/session/${this.sessionId}/stats`);
        const result = await response.json();

        if (result.success) {
            // 可以用服务器数据更新本地统计
            console.log('服务器统计数据:', result);
        }
    } catch (error) {
        console.warn('获取服务器统计失败:', error);
    }
};
