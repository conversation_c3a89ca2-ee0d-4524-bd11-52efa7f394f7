<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.api.wenxuan.dao.ZhYuanDao">
    <select id="selectBookMap" resultType="com.xygk.api.wenxuan.dto.BookMapTest">
        SELECT a.book_id as bookId, b.name as bookName, a.zy_book_id as zyBookId, c.name as zyBookName
        from tb_zy_mapping_book a
                 inner JOIN tb_books_info b on a.book_id = b.id
                 inner JOIN tb_zy_textbook c on a.zy_book_id = c.id
      where a.book_id=b.id  ORDER BY a.book_id asc
    </select>
    <insert id="insertBookMap">
        INSERT INTO tb_book_map
        (bookId, bookPid, bookName, chapterId, chapterPid, chapterName, zy_chapterId, zy_chapterPid, zy_chapterName,
        level)
        VALUES (#{bookId}, #{bookPid}, #{bookName}, #{chapterId}, #{chapterPid}, #{chapterName}, #{zyChapterId},
        #{zyChapterPid}, #{zyChapterName}, #{level})
    </insert>
    <!--文轩书籍信息-->
    <select id="selectWenXuanBook" resultType="com.xygk.api.wenxuan.dto.WenXuanBook">
        SELECT a.code, a.name, a.parent_id as parentId, a.level
        FROM tb_books_catalog a
        WHERE a.book_id = #{bookId}
        and a.level = '1'
    </select>
    <!--状元网书籍信息-->
    <select id="selectZyBookList" resultType="com.xygk.api.wenxuan.dto.ZyTextbook">
        select a.id, a.pId, a.name
        from tb_zy_textbook a
        where a.pId = #{bookId}
    </select>
    <select id="selectBookChild" resultType="com.xygk.api.wenxuan.dto.WenXuanBook">
        SELECT a.code, a.name, a.parent_id as parentId,a.level
        FROM tb_books_catalog a
        WHERE a.parent_id = #{chapterId}
    </select>
</mapper>