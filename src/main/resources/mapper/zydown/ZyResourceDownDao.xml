<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.api.zydown.dao.ZyResourceDownDao">
    <update id="updateResourceDown">
        UPDATE tb_zy_resource_down
        set is_download=#{state}
        where id = #{id}
    </update>

    <update id="updateDownload">
        UPDATE tb_zy_resource_down
        set downloaded_quantity=(downloaded_quantity + 1),
            is_download        = (CASE download_required - downloaded_quantity WHEN 0 THEN 1 else 0 END)
        where id = #{id}
    </update>
</mapper>