<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.api.zydown.dao.ZyResourcesPageDao">
    <!--查询待下载的试卷资源-->
    <select id="selectPaperList" resultType="com.xygk.api.zydown.dto.ResourceInfo">
        SELECT *
        FROM (
                 SELECT a.resources_id                                                                 AS resourcesId,
                        a.subject_name                                                                 AS subjectName,
                        (CASE a.period WHEN 1 THEN '小学' WHEN 2 THEN '初中' WHEN 3 THEN '高中' END)         AS periodName,
                        e.name                                                                         AS paperTypeName,
                        (CASE a.rank WHEN 2 THEN '普通' WHEN 3 THEN '特供' WHEN 4 THEN '精品' ELSE '免费' END) AS rankName,
                        a.year,
                        c.dictLabel                                                                    AS gradeName,
                        a.title,
                        a.province,
                        a.down,
                        a.look,
                        a.update_time,
                        a.type_name
                 FROM tb_zy_resources_page a
                          INNER JOIN tb_zy_paper_type e ON a.zy_resource_type = e.id
                          INNER JOIN tb_zy_grade c ON a.grade = CONVERT(c.dictValue USING utf8) COLLATE utf8_unicode_ci
                 WHERE
                     a.resource_type = 2
                   and a.download_status in(0,2)
                 ORDER BY
                     field ( a.province, 2277 ) DESC,
                     a.rank DESC,
                     a.update_time DESC,
                     a.down DESC,
                     a.look DESC
             ) d LIMIT 10
    </select>

    <update id="updateDownloadStatus">
        update tb_zy_resources_page a
        set a.download_status=#{status}
        where a.resources_id = #{resourcesId}
    </update>

    <select id="selectSpecialList" resultType="com.xygk.api.zydown.dto.ResourceInfo">
        SELECT *
        FROM (
                 SELECT a.resources_id                                                                 as resourcesId,
                        a.subject_name                                                                 as subjectName,
                        (CASE a.period WHEN 1 THEN '小学' WHEN 2 THEN '初中' WHEN 3 THEN '高中' END)         AS periodName,
                        b.name                                                                         AS examTypeName,
                        e.name                                                                         AS resourcesTypeName,
                        (CASE a.rank WHEN 2 THEN '普通' WHEN 3 THEN '特供' WHEN 4 THEN '精品' ELSE '免费' END) AS rankName,
                        a.year,
                        c.dictLabel                                                                    AS gradeName,
                        a.title,
                        a.province,
                        a.down,
                        a.look,
                        a.update_time,
                        a.type_name
                 FROM tb_zy_resources_page a
                          INNER JOIN tb_zy_examtype b ON a.exam_type = b.id
                          INNER JOIN tb_zy_resources_type e ON a.zy_resource_type = e.id
                          INNER JOIN tb_zy_grade c ON a.grade = CONVERT(c.dictValue USING utf8) COLLATE utf8_unicode_ci
                 WHERE
                     a.resource_type = 3
                   and a.download_status in(0,2)
                 ORDER BY
                     field ( a.province, 2277 ) DESC,
                     a.rank DESC,
                     a.update_time DESC,
                     a.down DESC,
                     a.look DESC
             ) d LIMIT 10
    </select>

    <select id="selectSynchronousResource" resultType="com.xygk.api.zydown.dto.ResourceInfo">
        select *
        from (
                 SELECT a.resources_id                                                                 AS resourcesId,
                        e.name                                                                         AS resourcesTypeName,
                        (CASE a.rank WHEN 2 THEN '普通' WHEN 3 THEN '特供' WHEN 4 THEN '精品' ELSE '免费' END) AS rankName,
                        a.year,
                        a.title,
                        a.wx_catalog_code                                                              AS catalogCode,
                        a.zy_book_id                                                                   as zyBookId,
                        a.type_name,
                        c.book_id                                                                      AS wxBookId
                 FROM tb_zy_resources_page a
                          INNER JOIN tb_zy_resources_type e
                                     ON a.zy_resource_type = e.id
                          INNER JOIN tb_zy_mapping_book c ON a.zy_book_id = c.zy_book_id
                 WHERE a.resource_type = 1
                   AND a.download_status in (0, 2)
                   and a.wx_catalog_code = #{catalogCode}
                 ORDER BY a.rank DESC,
                          a.update_time DESC,
                          a.down DESC,
                          a.look DESC) a limit #{limitTotal}
    </select>
    <select id="selectByResourcesId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(0)
        FROM tb_zy_resources_page a
        where a.resources_id = #{resourceId}
    </select>

    <select id="getListByBooKId" resultType="com.xygk.api.zydown.dto.WinShareCatalogTree">
        SELECT b.id,
               b.code,
               b.name,
               b.parent_id  as parentId,
               b.level,
               b.parent_ids as parentIds
        FROM tb_books_info a
                 INNER JOIN tb_books_catalog b ON a.id = b.book_id
        WHERE a.id = #{wxBookId}
        ORDER BY b.sort
    </select>

    <select id="selectCatalog" resultType="com.xygk.api.zydown.dto.ZyResourceDownInfo">
        SELECT a.id,
               a.book_id             as bookId,
               a.catalog_code        as catalogCode,
               a.download_required   as downloadRequired,
               a.downloaded_quantity as downloadedQuantity
        FROM tb_zy_resource_down a
        WHERE a.downloaded_quantity &lt; a.download_required
          and a.is_download = 0 LIMIT 1
    </select>
</mapper>