<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.api.zydown.dao.ZyMappingUserDao">
    <select id="listAll" resultType="com.xygk.api.zydown.entity.UserInfo">
        SELECT a.id,
               a.member_id                      AS memberId,
               FLOOR(
                       10 + RAND() * (20 - 10)) AS downloadsTotal
        FROM tb_zy_mapping_user a
    </select>
</mapper>