<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.api.zydown.dao.ZyMappingBookDao">
    <select id="selectListByZyId" resultType="com.xygk.api.zydown.dto.Catalog">
        SELECT a.id            AS bookId,
               a.NAME          AS bookName,
               b.zy_book_id    AS zyBookId,
               d.zy_catalog_id AS catalogId,
               d.catalog_id    AS catalogCode
        FROM tb_books_info a
                 INNER JOIN tb_zy_mapping_book b ON a.id = b.book_id
                 INNER JOIN tb_books_catalog c ON a.id = c.book_id
                 INNER JOIN tb_zy_mapping_catalog d ON c.code = CONVERT(d.catalog_id USING utf8) COLLATE utf8_unicode_ci
       <!--where a.id =1819 and d.zy_catalog_id=5994-->
                <!--where a.id =1762 and d.zy_catalog_id &gt;=52470 and d.zy_catalog_id &lt;=52503-->
                <!--where a.id =1758 and d.zy_catalog_id &gt;=72149 and d.zy_catalog_id &lt;=72157-->
        <!--                where a.id =1917 and d.zy_catalog_id &gt;=7671 and d.zy_catalog_id &lt;=7677-->
               <!-- where a.id =1018 and d.zy_catalog_id &gt;=78230 and d.zy_catalog_id &lt;=52374-->
        ORDER BY
            a.id, d.zy_catalog_id
    </select>

    <select id="getBookInfoList" resultType="com.xygk.api.zydown.dto.BookInfo">
        SELECT m.id             AS bookId,
               m.phase_name     AS phaseName,
               m.subject_name   AS subjectName,
               m.edition_name   AS editionName,
               m.grade_name     AS gradeName,
               m.fascicule_name AS fasciculeName,
               n.id             AS catalogId,
               n.code           AS catalogCode,
               n.parent_id      AS parentId,
               n.name           as catalogName,
               n.level
        FROM tb_books_info m
                 INNER JOIN tb_books_catalog n
                            ON m.id = n.book_id
                 INNER JOIN tb_zy_mapping_book b ON M.id = b.book_id
                 INNER JOIN tb_zy_mapping_catalog d ON n.code = CONVERT(d.catalog_id USING utf8) COLLATE utf8_unicode_ci
        WHERE b.zy_book_id=#{zyBookId}
    </select>

    <select id="getWinShareCatalogTreeList" resultType="com.xygk.api.zydown.dto.WinShareCatalogTree">
        SELECT a.code, a.name, a.parent_id as parentId, a.level
        from tb_books_catalog a
        WHERE a.code &lt;&gt; '0'
    </select>

    <select id="getBookInfoById" resultType="com.xygk.api.zydown.dto.BookInfo">
        SELECT a.phase_name     AS phaseName,
               a.subject_name   AS subjectName,
               a.edition_name   AS editionName,
               a.grade_name     AS gradeName,
               a.fascicule_name AS fasciculeName
        FROM tb_books_info a
        WHERE a.id = #{wxBookId}
    </select>
</mapper>