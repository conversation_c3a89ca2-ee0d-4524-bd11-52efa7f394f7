server:
  undertow:
    # 指定工作者线程的 I/0 线程数，默认为 2 或者 CPU 的个数
    threads:
      io: 8
      # 指定工作者线程个数,默认为 I/O线程个数的8倍
      worker: 256
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 内存大于 128 MB 时，bufferSize 为 16 KB 减去 20 字节，这 20 字节用于协议头
    buffer-size: 16364
    # 是否分配的直接内存(NIO直接分配的堆外内存)
    direct-buffers: true
  port: 8082
  servlet:
    context-path: /
    session:
      cookie:
        http-only: true
#knife4j配置
knife4j:
  enable: true
  basic:
    enable: false
    username: admin
    password: xygk@123
  setting:
    enableFooter: false

spring:
  # 环境 dev|test|prod
  profiles:
    active: test
  messages:
    encoding: UTF-8
    basename: i18n/messages
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true


#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: io.renren.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型
      id-type: ASSIGN_ID
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
#资源下载路径
resource:
  path: E:\资源下载
aliyun:
  qwen:
    api-key: sk-e64b6e3ee0c847139e4ff765a340f205
    api-endpoint: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions

deepseek:
  api-key: ***********************************
  api-endpoint: https://api.deepseek.com/chat/completions
  mock-mode: true  # 启用模拟模式，避免余额不足错误

bailian:
  api-key: sk-e64b6e3ee0c847139e4ff765a340f205
  default-app-id: 3267ecc2febd4c37b0baf9a08957daaa
  timeout: 30000
  max-retries: 3
  incremental-output: true
  stream-buffer-size: 1024
  session-timeout: 30

# PaddleOCR配置
paddle:
  ocr:
    enable: true   # 启用PaddleOCR（使用Python版本，更稳定）
    mock-mode: false  # 禁用模拟模式，启用真实的PaddleOCR识别
    model-dir: models/ocr
    detection-model: ch_PP-OCRv4_det_infer
    recognition-model: ch_PP-OCRv4_rec_infer
    classification-model: ch_ppocr_mobile_v2.0_cls_infer
    max-image-size: 10485760  # 10MB
    supported-formats: jpg,jpeg,png,bmp,gif
    # Python环境配置
    python-path: python  # Python解释器路径
    script-path: scripts/paddle_ocr.py  # 优化的OCR脚本路径
    timeout: 15  # OCR识别超时时间（秒）- 优化后减少等待时间
    # 模型配置
    use-gpu: false  # 使用CPU模式（兼容性更好）
    enable-mkldnn: true  # 启用MKLDNN加速
    cpu-threads: 4  # CPU线程数
    # 识别参数优化
    det-db-thresh: 0.3      # 文本检测阈值
    det-db-box-thresh: 0.5  # 文本框阈值
    det-db-unclip-ratio: 1.6 # 文本框扩展比例
    rec-batch-num: 6        # 识别批次大小



# Tesseract OCR配置
tesseract:
  ocr:
    enable: true
    data-path: tessdata
    language: chi_sim  # 仅使用中文，提高准确性
    timeout: 20000     # 增加超时时间
    # OCR引擎配置
    ocr-engine-mode: 1  # 使用LSTM OCR引擎
    page-seg-mode: 6    # 单一文本块，更适合文档
    # 字符白名单（限制为中文和常用符号，减少乱码）
    char-whitelist: ""  # 不限制字符，但会在后处理中清理
    # 预处理选项
    dpi: 300           # 设置DPI
    scale-factor: 2.0  # 增加缩放，提高清晰度
    # 性能优化
    chinese-only: true      # 启用中文专用模式
    aggressive-cleaning: true  # 启用激进的乱码清理
    min-confidence: 50      # 降低最小置信度阈值
    enable-preprocessing: true  # 启用图像预处理

# 身份证OCR专用配置
idcard:
  ocr:
    enable: true
    script-path: scripts/idcard_ocr.py  # 身份证专用OCR脚本
    timeout: 10  # 识别超时时间（秒）- 优化后减少等待时间
    enable-face-detection: true   # 启用人脸检测
    enable-emblem-detection: true # 启用国徽检测
    # 图像质量要求
    min-width: 300    # 最小图像宽度
    min-height: 200   # 最小图像高度
    max-file-size: 10485760  # 最大文件大小 10MB
    # 支持的图像格式
    supported-formats: jpg,jpeg,png,bmp,gif
    # 云端OCR配置（可实现3秒内识别）
    use-cloud: false  # 是否使用云端OCR
    cloud-provider: baidu  # 云端OCR提供商：baidu, tencent, aliyun



