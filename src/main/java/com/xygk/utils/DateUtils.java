package com.xygk.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2023/3/3 16:15
 */
public class DateUtils {
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN_GMT = "EEE, dd MMM yyyy hh:mm:ss 'GMT'";

    /**
     * 日期格式化
     *
     * @param date 日期
     * @return 返回GMT格式时间
     */
    public static String formatGmt(Date date) {
        if (date != null) {
            DateFormat df = new SimpleDateFormat(DATE_TIME_PATTERN_GMT, Locale.US);
            df.setTimeZone(TimeZone.getTimeZone("GMT"));
            return df.format(date);
        }
        return null;
    }
}
