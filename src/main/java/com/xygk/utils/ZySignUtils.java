package com.xygk.utils;


import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Date;
import java.util.Formatter;

/**
 * 状元网签名工具类
 *
 * <AUTHOR>
 * @date 2023/3/8 11:23
 */
public class ZySignUtils {
    private static final String HMAC_SHA1 = "HmacSHA1";
    public static final String ACCESS_KEY_ID = "2022123022151197865";
    public static final String ACCESS_KEY_SECRET = "8acaf98f2ac78e0e0ab366cf92ef7789";
    public static final String THIRD_PARTY_ID = "2022123036282742245";
    public static final String THIRD_PARTY_NAME = "新华文轩";
    public static final String THIRD_SCHOOL_ID = "477397";
    public static final String REDIS_KEY_USER="ZYW_DOWNLOAD_USER";

    /**
     * 生成签名
     *
     * @param httpMethod  请求方法
     * @param httpUrl     请求地址
     * @param requestDate 请求时间
     * @return 签名字符串
     */
    public static String generatedSign(String httpMethod, String httpUrl, Date requestDate) {
        String authorization;
        //待签名字符串
        String stringToSign = httpMethod + "\n"
                + "application/x-www-form-urlencoded;charset=UTF-8" + "\n"
                + ZyDateUtils.format(requestDate) + "\n"
                + httpUrl;
        //HMAC-SHA1加密
        String encrypt = encrypt(stringToSign);
        //加密字符串Base64编码
        Base64.Encoder encoder = Base64.getEncoder();
        String base64Str = encoder.encodeToString(encrypt.getBytes(StandardCharsets.UTF_8));
        //最终签名字符串
        authorization = "ROP " + ACCESS_KEY_ID + ":" + base64Str;
        return authorization;
    }

    /**
     * HMAC-SHA1加密
     *
     * @param data 待加密字符串
     * @return 加密字符串
     */
    private static String encrypt(String data) {
        Mac mac = null;
        try {
            SecretKeySpec secret = new SecretKeySpec(ACCESS_KEY_SECRET.getBytes(), HMAC_SHA1);
            mac = Mac.getInstance(HMAC_SHA1);
            mac.init(secret);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return toHexString(mac != null ? mac.doFinal(data.getBytes()) : new byte[0]);
    }

    private static String toHexString(byte[] bytes) {
        try (Formatter formatter = new Formatter()) {
            for (byte b : bytes) {
                formatter.format("%02x", b);
            }
            return formatter.toString();
        }
    }
}
