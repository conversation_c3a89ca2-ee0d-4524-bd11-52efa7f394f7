package com.xygk.utils;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * 状元网http请求工具类
 *
 * <AUTHOR>
 * @date 2023/3/8 11:32
 */
public class ZyHttpRequestUtils {
    private static final Logger logger = LoggerFactory.getLogger(ZyHttpRequestUtils.class);

    /**
     * 接口地址(正式地址)1-16
     */
    private static final String HTTP_REQUEST_URI = "http://iweb.zhuangyuan123.com";

    /**
     * 接口地址(正式地址)17-18
     */
    private static final String HTTP_SCHOOL_URI = "http://school.zhuangyuan123.com";
//    /**
//     * 接口地址(测试地址)
//     */
//    private static final String HTTP_REQUEST_URI = "http://web1.zhuangyuan123.com";
    /**
     * 响应成功状态码
     */
    private static final Integer HTTP_CODE_SUCCESS = 200;

    /**
     * get请求
     *
     * @param httpVerb 请求路径
     * @return String(返回结果)
     */
    public static String get(String httpVerb) {
        String result = "";
        Date date = new Date();
        try {
            result = HttpRequest.get(HTTP_REQUEST_URI + httpVerb)
                    .header("AccessKey", ZySignUtils.ACCESS_KEY_ID)
                    .header("Date", ZyDateUtils.format(date))
                    .header("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
                    .header("Authorization", ZySignUtils.generatedSign("GET", httpVerb, date))
                    .timeout(40000)
                    .execute()
                    .body();
            JSONObject jsonObject = JSON.parseObject(result);
            Integer code = jsonObject.getInteger("code");
            if (HTTP_CODE_SUCCESS.equals(code)) {
                return jsonObject.getString("data");
            }
        } catch (Exception e) {
            logger.error("请求路径[" + httpVerb + "]的接口调用失败" + result + ":" + e);
            e.printStackTrace();
        }
        return result;
    }


    /**
     * post请求
     *
     * @param httpVerb 请求路径
     * @return String(返回结果)
     */
    public static String post(String httpVerb) {
        String result = "";
        Date date = new Date();
        try {
            result = HttpRequest.post(HTTP_REQUEST_URI + httpVerb)
                    .header("AccessKey", ZySignUtils.ACCESS_KEY_ID)
                    .header("Date", ZyDateUtils.format(date))
                    .header("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
                    .header("Authorization", ZySignUtils.generatedSign("POST", httpVerb, date))
                    .timeout(40000)
                    .execute()
                    .body();
            JSONObject jsonObject = JSON.parseObject(result);
            Integer code = jsonObject.getInteger("code");
            if (HTTP_CODE_SUCCESS.equals(code)) {
                return jsonObject.getString("data");
            }
        } catch (HttpException e) {
            logger.error("请求路径[" + httpVerb + "]的接口调用失败" + result + ":" + e);
            e.printStackTrace();
        }
        return result;
    }
}
