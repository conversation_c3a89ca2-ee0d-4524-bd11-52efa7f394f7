package com.xygk.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 目录和文件名称解析工具类
 * 支持解析带序号的文件/目录名称，例如：01_文件夹名称
 * 
 * <AUTHOR>
 * @date 2025/4/14
 */
public class DirectoryNameParser {

    /**
     * 匹配 "数字-名称" 或 "数字_名称" 格式（支持中英文、数字、下划线）
     */
    private static final Pattern DIR_PATTERN = Pattern.compile("^(\\d+)[-_]((?:[\\u4e00-\\u9fa5a-zA-Z0-9_]+\\s*)+)$");

    /**
     * 解析目录名称（仅处理目录）
     *
     * @param name 目录名称
     * @return [序号, 显示名称]（未匹配则序号=0，显示名称=原名称）
     */
    public static NameData parse(String name, boolean isDirectory) {
        if (!isDirectory) {
            // 文件直接返回原名称
            return new NameData(0, name);
        }
        Matcher matcher = DIR_PATTERN.matcher(name);
        if (matcher.find()) {
            int sort = Integer.parseInt(matcher.group(1));
            String displayName = matcher.group(2).trim();
            return new NameData(sort, displayName);
        }
        // 目录未匹配格式时保留原名称
        return new NameData(0, name);
    }

    public static class NameData {
        private final int sort;
        private final String displayName;

        public NameData(int sort, String displayName) {
            this.sort = sort;
            this.displayName = displayName;
        }

        public int getSort() {
            return sort;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}