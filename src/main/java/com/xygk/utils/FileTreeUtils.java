package com.xygk.utils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.AccessDeniedException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.Collator;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Queue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xygk.api.wenxuan.dto.TreeNode;

import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR>
 * @date 2025/4/14 11:03
 */
public class FileTreeUtils {
    private static final Logger logger = LoggerFactory.getLogger(FileTreeUtils.class);

    private static int compareFileNames(String name1, String name2) {
        try {
            // 记录原始文件名
            logger.debug("Comparing files - Original names: [{}] vs [{}]", name1, name2);

            // 确保使用UTF-8编码
            String normalized1 = new String(name1.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
            String normalized2 = new String(name2.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);

            // 记录编码后的文件名
            logger.debug("After UTF-8 normalization: [{}] vs [{}]", normalized1, normalized2);

            // 先转换为小写进行比较
            int result = normalized1.toLowerCase().compareTo(normalized2.toLowerCase());
            logger.debug("Lowercase comparison result: {}", result);

            if (result != 0) {
                return result;
            }

            // 如果小写比较相同，再按原始大小写比较
            result = normalized1.compareTo(normalized2);
            logger.debug("Original case comparison result: {}", result);
            return result;
        } catch (Exception e) {
            logger.error("Error comparing file names: [{}] vs [{}]", name1, name2, e);
            // 如果出现异常，使用原始比较
            return name1.compareTo(name2);
        }
    }

    public static TreeNode buildFileTree(String rootPath) throws IOException {
        // 记录环境信息
        logger.info("Building file tree for path: {}", rootPath);
        logger.info("System properties - JDK Version: {}, Default Locale: {}, File Encoding: {}",
                System.getProperty("java.version"),
                Locale.getDefault(),
                System.getProperty("file.encoding"));

        Path root = Paths.get(rootPath);
        if (!Files.exists(root)) {
            throw new IllegalArgumentException("路径不存在: " + rootPath);
        }

        // 创建中英文兼容的排序器
        Collator chineseCollator = Collator.getInstance(Locale.CHINA);
        // 设置排序强度为第二级，同时考虑基本字符
        chineseCollator.setStrength(Collator.IDENTICAL);

        boolean isRootDir = Files.isDirectory(root);
        // 解析根节点
        DirectoryNameParser.NameData rootData = DirectoryNameParser.parse(
                root.getFileName().toString(), isRootDir);
        // 生成根节点UUID和父级路径
        TreeNode rootNode = new TreeNode(
                root.getFileName().toString(),
                rootData.getDisplayName(),
                IdUtil.simpleUUID(),
                null,
                "",
                rootData.getSort(),
                0,
                isRootDir,
                root.toString());

        // BFS遍历队列
        Queue<Path> pathQueue = new LinkedList<>();
        Queue<TreeNode> nodeQueue = new LinkedList<>();
        if (isRootDir) {
            pathQueue.offer(root);
            nodeQueue.offer(rootNode);
        }

        while (!pathQueue.isEmpty()) {
            Path currentPath = pathQueue.poll();
            TreeNode currentNode = nodeQueue.poll();

            try (DirectoryStream<Path> stream = Files.newDirectoryStream(currentPath)) {
                // 收集所有子节点（目录和文件）
                List<Path> children = new ArrayList<>();
                stream.forEach(children::add);
                // 分离目录和文件
                List<Path> directories = new ArrayList<>();
                List<Path> files = new ArrayList<>();
                for (Path child : children) {
                    if (Files.isDirectory(child)) {
                        directories.add(child);
                    } else {
                        files.add(child);
                    }
                }

                // 判断当前是否为第一级目录（即根目录level==0）
                boolean isFirstLevel = currentNode != null && currentNode.getLevel() == 0;

                // 判断是否为第二级目录（根目录的直接子目录level==1）
                boolean isSecondLevel = currentNode != null && currentNode.getLevel() == 1;

                // 处理目录：已匹配和未匹配
                List<Path> matchedDirs = new ArrayList<>();
                List<Path> unmatchedDirs = new ArrayList<>();
                Map<Path, Integer> dirSortMap = new HashMap<>();
                Map<Path, String> dirDisplayNameMap = new HashMap<>();

                for (Path dir : directories) {
                    String dirName = dir.getFileName().toString();
                    DirectoryNameParser.NameData data = DirectoryNameParser.parse(dirName, true);
                    if (data.getSort() > 0) {
                        matchedDirs.add(dir);
                        dirSortMap.put(dir, data.getSort());
                        dirDisplayNameMap.put(dir, data.getDisplayName());
                    } else {
                        unmatchedDirs.add(dir);
                        dirDisplayNameMap.put(dir, dirName);
                    }
                }

                // 如果是第一级目录，对其直接子目录（第二级目录）按中文名称排序
                if (isFirstLevel) {
                    logger.info("Sorting first level unmatched directories, count: {}", unmatchedDirs.size());
                    // 对未匹配目录按名称排序
                    unmatchedDirs.sort((p1, p2) -> {
                        String name1 = p1.getFileName().toString();
                        String name2 = p2.getFileName().toString();
                        return compareFileNames(name1, name2);
                    });
                    logger.info("First level unmatched directories sorted successfully");
                }

                // 对已匹配目录按sort排序
                logger.info("Sorting matched directories, count: {}", matchedDirs.size());
                matchedDirs.sort(Comparator.comparing(dirSortMap::get));
                logger.info("Matched directories sorted successfully");

                // 计算已匹配目录的最大sort值
                int maxMatchedSort = matchedDirs.stream()
                        .mapToInt(dirSortMap::get)
                        .max().orElse(0);

                // 为未匹配目录分配递增sort值
                int currentUnmatchedSort = maxMatchedSort + 1;
                Map<Path, Integer> unmatchedSortMap = new HashMap<>();
                for (Path dir : unmatchedDirs) {
                    unmatchedSortMap.put(dir, currentUnmatchedSort++);
                }

                // 合并目录：先已匹配，后未匹配
                List<Path> sortedChildDirs = new ArrayList<>();
                sortedChildDirs.addAll(matchedDirs);
                sortedChildDirs.addAll(unmatchedDirs);

                // 对所有文件按名称排序
                logger.info("Sorting files, count: {}", files.size());
                files.sort((p1, p2) -> {
                    String name1 = p1.getFileName().toString();
                    String name2 = p2.getFileName().toString();
                    return compareFileNames(name1, name2);
                });
                logger.info("Files sorted successfully");

                // 为文件分配递增sort值（所有层级都从1开始）
                int currentFileSort = 1;
                Map<Path, Integer> fileSortMap = new HashMap<>();
                for (Path file : files) {
                    fileSortMap.put(file, currentFileSort++);
                }

                // 合并所有子节点：目录在前，文件在后
                List<Path> allChildren = new ArrayList<>();
                allChildren.addAll(sortedChildDirs);
                allChildren.addAll(files);

                // 构建子节点
                for (Path childPath : allChildren) {
                    boolean isChildDir = Files.isDirectory(childPath);
                    String childName = childPath.getFileName().toString();

                    // 获取显示名称和sort值
                    String displayName;
                    int sort;
                    // 生成子节点完整路径
                    Path childFullPath = currentPath.resolve(childName);

                    if (isChildDir) {
                        if (matchedDirs.contains(childPath)) {
                            sort = dirSortMap.get(childPath);
                        } else {
                            sort = unmatchedSortMap.get(childPath);
                        }
                        displayName = dirDisplayNameMap.get(childPath);
                    } else {
                        displayName = childName;
                        sort = fileSortMap.get(childPath);
                    }

                    // 生成UUID和父级路径
                    String childCode = IdUtil.simpleUUID();
                    String childParentCodes = null;
                    if (currentNode != null) {
                        childParentCodes = currentNode.getParentCodes().isEmpty()
                                ? currentNode.getCode()
                                : currentNode.getParentCodes() + "," + currentNode.getCode();
                    }

                    TreeNode childNode = null;
                    if (currentNode != null) {
                        childNode = new TreeNode(
                                childName,
                                displayName,
                                childCode,
                                currentNode.getCode(),
                                childParentCodes,
                                sort,
                                currentNode.getLevel() + 1,
                                isChildDir,
                                childFullPath.toString());
                    }
                    if (currentNode != null) {
                        currentNode.getChildren().add(childNode);
                    }

                    if (isChildDir) {
                        pathQueue.offer(childPath);
                        nodeQueue.offer(childNode);
                    }
                }

                // 如果是第二级目录，则对其子目录重新按照名称排序并设置序号
                if (isSecondLevel && currentNode != null) {
                    // 获取所有子目录节点
                    List<TreeNode> childDirs = new ArrayList<>();
                    for (TreeNode child : currentNode.getChildren()) {
                        if (child.isDirectory()) {
                            childDirs.add(child);
                        }
                    }

                    // 按照名称排序
                    childDirs.sort((n1, n2) -> chineseCollator.compare(n1.getOriginalName(), n2.getOriginalName()));

                    // 重新按排序顺序分配序号
                    int newSort = 1;
                    for (TreeNode childDir : childDirs) {
                        childDir.setSort(newSort++);
                    }
                }
            } catch (AccessDeniedException e) {
                throw new IllegalArgumentException("拒绝访问: " + currentPath);
            }
        }
        return rootNode;
    }
}
