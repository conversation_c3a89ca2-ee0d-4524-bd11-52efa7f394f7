package com.xygk.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 状元网日期工具类
 *
 * <AUTHOR>
 * @date 2023/3/8 11:33
 */
public class ZyDateUtils {
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN_GMT = "EEE, dd MMM yyyy HH:mm:ss 'GMT'";

    /**
     * 日期格式化
     *
     * @param date 日期
     * @return 返回GMT格式时间
     */
    public static String format(Date date) {
        if (date != null) {
            DateFormat df = new SimpleDateFormat(DATE_TIME_PATTERN_GMT, Locale.US);
            return df.format(date);
        }
        return null;
    }
}
