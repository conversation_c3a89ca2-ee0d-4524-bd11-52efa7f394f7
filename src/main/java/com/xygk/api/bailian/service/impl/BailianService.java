package com.xygk.api.bailian.service.impl;

import com.alibaba.dashscope.app.*;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.xygk.api.bailian.config.BailianConfig;
import com.xygk.api.bailian.dto.BailianRequest;
import com.xygk.api.bailian.dto.BailianResponse;
import com.xygk.api.bailian.exception.BailianException;

import io.reactivex.Flowable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Service
public class BailianService {

    private static final Logger logger = LoggerFactory.getLogger(BailianService.class);

    @Autowired
    private BailianConfig bailianConfig;

    // 会话缓存，存储会话历史
    private final Map<String, List<Map<String, String>>> sessionCache = new ConcurrentHashMap<>();

    // 会话超时管理
    private final Map<String, Long> sessionTimeouts = new ConcurrentHashMap<>();
    
    /**
     * 通用对话处理
     */
    public BailianResponse chat(BailianRequest request) throws ApiException, NoApiKeyException, InputRequiredException {
        validateRequest(request);

        // 清理过期会话
        cleanExpiredSessions();

        // 构建请求参数
        ApplicationParam param = ApplicationParam.builder()
                .apiKey(bailianConfig.getApiKey())
                .appId(getAppId(request))
                .prompt(request.getPrompt())
                .sessionId(getOrCreateSessionId(request))
                .incrementalOutput(request.getIncrementalOutput() != null ?
                    request.getIncrementalOutput() : bailianConfig.getIncrementalOutput())
                .build();

        try {
            Application application = new Application();
            ApplicationResult result = application.call(param);

            // 更新会话缓存
            updateSessionCache(param.getSessionId(), "user", request.getPrompt());
            updateSessionCache(param.getSessionId(), "assistant", result.getOutput().getText());

            logger.info("对话成功，sessionId: {}, appId: {}", param.getSessionId(), param.getAppId());

            return BailianResponse.success(
                result.getOutput().getText(),
                result.getOutput().getSessionId()
            );
        } catch (Exception e) {
            logger.error("对话调用失败: {}", e.getMessage(), e);
            throw new BailianException("对话调用失败", e);
        }
    }
    
    /**
     * 流式对话处理
     */
    public SseEmitter streamChat(BailianRequest request) {
        validateRequest(request);

        SseEmitter emitter = new SseEmitter(TimeUnit.MINUTES.toMillis(bailianConfig.getSessionTimeout()));

        // 清理过期会话
        cleanExpiredSessions();

        ApplicationParam param = ApplicationParam.builder()
                .apiKey(bailianConfig.getApiKey())
                .appId(getAppId(request))
                .prompt(request.getPrompt())
                .sessionId(getOrCreateSessionId(request))
                .incrementalOutput(true)
                .build();

        try {
            Application application = new Application();
            Flowable<ApplicationResult> result = application.streamCall(param);

            StringBuilder fullResponse = new StringBuilder();

            result.subscribe(
                data -> {
                    try {
                        String text = data.getOutput().getText();
                        fullResponse.append(text);

                        Map<String, Object> response = new HashMap<>();
                        response.put("text", text);
                        response.put("sessionId", data.getOutput().getSessionId());
                        response.put("timestamp", System.currentTimeMillis());
                        emitter.send(response);

                        logger.debug("流式响应发送成功: {}", text);
                    } catch (IOException e) {
                        logger.error("流式响应发送失败: {}", e.getMessage(), e);
                        emitter.completeWithError(e);
                    }
                },
                error -> {
                    logger.error("流式对话调用失败: {}", error.getMessage(), error);
                    emitter.completeWithError(error);
                },
                () -> {
                    // 更新会话缓存
                    updateSessionCache(param.getSessionId(), "user", request.getPrompt());
                    updateSessionCache(param.getSessionId(), "assistant", fullResponse.toString());

                    logger.info("流式对话完成，sessionId: {}", param.getSessionId());
                    emitter.complete();
                }
            );
        } catch (Exception e) {
            logger.error("流式对话初始化失败: {}", e.getMessage(), e);
            emitter.completeWithError(e);
        }

        return emitter;
    }
    
    /**
     * 多轮对话处理
     */
    public BailianResponse multiTurnChat(BailianRequest request) throws ApiException, NoApiKeyException, InputRequiredException {
        StringBuilder conversation = new StringBuilder();
        for (Map<String, String> message : request.getMessages()) {
            String role = message.get("role");
            String content = message.get("content");
            conversation.append(role).append(": ").append(content).append("\n");
        }
        
        ApplicationParam param = ApplicationParam.builder()
                .apiKey(bailianConfig.getApiKey())
                .appId(request.getAppId())
                .prompt(conversation.toString())
                .sessionId(request.getSessionId())
                .build();

        Application application = new Application();
        ApplicationResult result = application.call(param);
        
        return BailianResponse.success(
            result.getOutput().getText(),
            result.getOutput().getSessionId()
        );
    }
    
    /**
     * 知识库检索处理
     */
    public BailianResponse searchKnowledgeBase(BailianRequest request) throws ApiException, NoApiKeyException, InputRequiredException {
        ApplicationParam param = ApplicationParam.builder()
                .apiKey(bailianConfig.getApiKey())
                .appId(request.getAppId())
                .prompt("请在知识库中搜索以下内容：" + request.getQuery())
                .build();

        Application application = new Application();
        ApplicationResult result = application.call(param);
        
        return BailianResponse.success(
            result.getOutput().getText(),
            result.getOutput().getSessionId()
        );
    }

    /**
     * 文件处理功能
     */
    public BailianResponse processFile(MultipartFile file, String appId, String prompt) {
        try {
            validateFile(file);

            // 读取文件内容
            String fileContent = new String(file.getBytes(), StandardCharsets.UTF_8);

            // 构建包含文件内容的提示
            String fullPrompt = prompt + "\n\n文件内容：\n" + fileContent;

            BailianRequest request = new BailianRequest();
            request.setAppId(appId);
            request.setPrompt(fullPrompt);
            request.setSessionId(UUID.randomUUID().toString());

            return chat(request);

        } catch (Exception e) {
            logger.error("文件处理失败: {}", e.getMessage(), e);
            throw new BailianException("文件处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(BailianRequest request) {
        if (request == null) {
            throw new BailianException("请求参数不能为空");
        }
        if (!StringUtils.hasText(request.getPrompt()) &&
            (request.getMessages() == null || request.getMessages().isEmpty()) &&
            !StringUtils.hasText(request.getQuery())) {
            throw new BailianException("提示内容、消息列表或查询内容不能全部为空");
        }
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BailianException("文件不能为空");
        }

        // 检查文件大小（限制为10MB）
        long maxSize = 10 * 1024 * 1024;
        if (file.getSize() > maxSize) {
            throw new BailianException("文件大小不能超过10MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || (!contentType.startsWith("text/") &&
            !"application/json".equals(contentType) &&
            !"application/xml".equals(contentType))) {
            throw new BailianException("不支持的文件类型，仅支持文本文件");
        }
    }

    /**
     * 获取应用ID
     */
    private String getAppId(BailianRequest request) {
        if (StringUtils.hasText(request.getAppId())) {
            return request.getAppId();
        }
        if (StringUtils.hasText(bailianConfig.getDefaultAppId())) {
            return bailianConfig.getDefaultAppId();
        }
        throw new BailianException("应用ID不能为空，请在请求中指定appId或在配置中设置默认appId");
    }

    /**
     * 获取或创建会话ID
     */
    private String getOrCreateSessionId(BailianRequest request) {
        if (StringUtils.hasText(request.getSessionId())) {
            // 更新会话超时时间
            sessionTimeouts.put(request.getSessionId(), System.currentTimeMillis());
            return request.getSessionId();
        }

        String sessionId = UUID.randomUUID().toString();
        sessionTimeouts.put(sessionId, System.currentTimeMillis());
        return sessionId;
    }

    /**
     * 更新会话缓存
     */
    private void updateSessionCache(String sessionId, String role, String content) {
        if (!StringUtils.hasText(sessionId) || !StringUtils.hasText(content)) {
            return;
        }

        sessionCache.computeIfAbsent(sessionId, k -> new ArrayList<>());

        Map<String, String> message = new HashMap<>();
        message.put("role", role);
        message.put("content", content);
        message.put("timestamp", String.valueOf(System.currentTimeMillis()));

        List<Map<String, String>> messages = sessionCache.get(sessionId);
        messages.add(message);

        // 限制会话历史长度（最多保留50条消息）
        if (messages.size() > 50) {
            messages.subList(0, messages.size() - 50).clear();
        }

        // 更新会话超时时间
        sessionTimeouts.put(sessionId, System.currentTimeMillis());
    }

    /**
     * 清理过期会话
     */
    private void cleanExpiredSessions() {
        long currentTime = System.currentTimeMillis();
        long timeoutMillis = TimeUnit.MINUTES.toMillis(bailianConfig.getSessionTimeout());

        Iterator<Map.Entry<String, Long>> iterator = sessionTimeouts.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Long> entry = iterator.next();
            if (currentTime - entry.getValue() > timeoutMillis) {
                String sessionId = entry.getKey();
                iterator.remove();
                sessionCache.remove(sessionId);
                logger.debug("清理过期会话: {}", sessionId);
            }
        }
    }

    /**
     * 获取会话历史
     */
    public List<Map<String, String>> getSessionHistory(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            return new ArrayList<>();
        }

        cleanExpiredSessions();
        return sessionCache.getOrDefault(sessionId, new ArrayList<>());
    }

    /**
     * 清除会话历史
     */
    public void clearSessionHistory(String sessionId) {
        if (StringUtils.hasText(sessionId)) {
            sessionCache.remove(sessionId);
            sessionTimeouts.remove(sessionId);
            logger.info("清除会话历史: {}", sessionId);
        }
    }
}