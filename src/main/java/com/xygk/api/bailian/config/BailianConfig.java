package com.xygk.api.bailian.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 百炼智能体配置类
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@Component
@ConfigurationProperties(prefix = "bailian")
public class BailianConfig {
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 默认应用ID
     */
    private String defaultAppId;
    
    /**
     * 请求超时时间（毫秒）
     */
    private Integer timeout = 30000;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;
    
    /**
     * 是否启用增量输出
     */
    private Boolean incrementalOutput = true;
    
    /**
     * 流式输出缓冲区大小
     */
    private Integer streamBufferSize = 1024;
    
    /**
     * 会话超时时间（分钟）
     */
    private Integer sessionTimeout = 30;
}
