package com.xygk.api.bailian.exception;

/**
 * 百炼智能体异常类
 * <AUTHOR>
 * @date 2025-07-03
 */
public class BailianException extends RuntimeException {
    
    private String errorCode;
    private String errorMessage;
    
    public BailianException(String message) {
        super(message);
        this.errorMessage = message;
    }
    
    public BailianException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
    
    public BailianException(String message, Throwable cause) {
        super(message, cause);
        this.errorMessage = message;
    }
    
    public BailianException(String errorCode, String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
}
