package com.xygk.api.bailian.exception;

import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.xygk.api.bailian.dto.BailianResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 百炼智能体异常处理器
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestControllerAdvice(basePackages = "com.xygk.api.bailian")
public class BailianExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(BailianExceptionHandler.class);
    
    /**
     * 处理API异常
     */
    @ExceptionHandler(ApiException.class)
    public ResponseEntity<BailianResponse> handleApiException(ApiException e) {
        logger.error("百炼API调用异常: {}", e.getMessage(), e);
        BailianResponse response = BailianResponse.error("API调用失败: " + e.getMessage(), 500);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 处理API密钥异常
     */
    @ExceptionHandler(NoApiKeyException.class)
    public ResponseEntity<BailianResponse> handleNoApiKeyException(NoApiKeyException e) {
        logger.error("API密钥异常: {}", e.getMessage(), e);
        BailianResponse response = BailianResponse.error("API密钥配置错误", 401);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }
    
    /**
     * 处理输入参数异常
     */
    @ExceptionHandler(InputRequiredException.class)
    public ResponseEntity<BailianResponse> handleInputRequiredException(InputRequiredException e) {
        logger.error("输入参数异常: {}", e.getMessage(), e);
        BailianResponse response = BailianResponse.error("输入参数不完整: " + e.getMessage(), 400);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
    
    /**
     * 处理自定义百炼异常
     */
    @ExceptionHandler(BailianException.class)
    public ResponseEntity<BailianResponse> handleBailianException(BailianException e) {
        logger.error("百炼业务异常: {}", e.getErrorMessage(), e);
        BailianResponse response = BailianResponse.error(e.getErrorMessage(), 500);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<BailianResponse> handleException(Exception e) {
        logger.error("系统异常: {}", e.getMessage(), e);
        BailianResponse response = BailianResponse.error("系统内部错误", 500);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
