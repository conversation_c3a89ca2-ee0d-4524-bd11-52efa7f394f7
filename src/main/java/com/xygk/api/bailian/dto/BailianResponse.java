package com.xygk.api.bailian.dto;

import lombok.Data;
import java.util.Map;

@Data
public class BailianResponse {
    private String text;
    private String sessionId;
    private Map<String, Object> metadata;
    private String error;
    private Integer code;
    
    public static BailianResponse success(String text, String sessionId) {
        BailianResponse response = new BailianResponse();
        response.setText(text);
        response.setSessionId(sessionId);
        response.setCode(200);
        return response;
    }
    
    public static BailianResponse error(String error, Integer code) {
        BailianResponse response = new BailianResponse();
        response.setError(error);
        response.setCode(code);
        return response;
    }
} 