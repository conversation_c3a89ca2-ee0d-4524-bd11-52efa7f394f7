package com.xygk.api.bailian.controller;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.http.MediaType;
import io.reactivex.Flowable;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.io.IOException;

@RestController
@RequestMapping("/api/bailian")
@Api(tags = "阿里百炼智能体")
public class BailianController {

    private static final Logger logger = LoggerFactory.getLogger(BailianController.class);

    @Value("${bailian.api-key:sk-e64b6e3ee0c847139e4ff765a340f205}")
    private String apiKey;

    // 会话缓存
    private final Map<String, List<Map<String, String>>> sessionCache = new HashMap<>();

    /**
     * 普通对话
     */
    @PostMapping("/chat")
    public ResponseEntity<Map<String, Object>> chat(@RequestBody Map<String, Object> request) {
        logger.info("收到普通对话请求: {}", request);

        try {
            String appId = (String) request.get("appId");
            String prompt = (String) request.get("prompt");
            String sessionId = (String) request.get("sessionId");
            Boolean incrementalOutput = (Boolean) request.get("incrementalOutput");

            if (appId == null || appId.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("应用ID不能为空", 400));
            }

            if (prompt == null || prompt.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("提示内容不能为空", 400));
            }

            if (apiKey == null || apiKey.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("API密钥未配置", 500));
            }

            // 构建请求参数
            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(apiKey)
                    .appId(appId)
                    .prompt(prompt)
                    .sessionId(sessionId != null ? sessionId : generateSessionId())
                    .incrementalOutput(incrementalOutput != null ? incrementalOutput : false)
                    .build();

            Application application = new Application();
            ApplicationResult result = application.call(param);

            // 更新会话缓存
            updateSessionCache(param.getSessionId(), "user", prompt);
            updateSessionCache(param.getSessionId(), "assistant", result.getOutput().getText());

            logger.info("对话成功，sessionId: {}", param.getSessionId());

            return ResponseEntity.ok(createSuccessResponse(result.getOutput().getText(), result.getOutput().getSessionId()));

        } catch (ApiException e) {
            logger.error("API调用失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(createErrorResponse("API调用失败: " + e.getMessage(), 500));
        } catch (NoApiKeyException e) {
            logger.error("API密钥异常: {}", e.getMessage(), e);
            return ResponseEntity.status(401).body(createErrorResponse("API密钥配置错误", 401));
        } catch (InputRequiredException e) {
            logger.error("输入参数异常: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createErrorResponse("输入参数不完整: " + e.getMessage(), 400));
        } catch (Exception e) {
            logger.error("系统异常: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(createErrorResponse("系统内部错误: " + e.getMessage(), 500));
        }
    }

    /**
     * 流式对话
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamChat(@RequestBody Map<String, Object> request) {
        logger.info("收到流式对话请求: {}", request);
        // 30分钟超时
        SseEmitter emitter = new SseEmitter(30 * 60 * 1000L);

        // 设置SSE事件处理
        emitter.onCompletion(() -> logger.info("流式对话连接已完成"));
        emitter.onTimeout(() -> {
            logger.warn("流式对话连接超时");
            emitter.complete();
        });
        emitter.onError((ex) -> {
            logger.error("流式对话连接错误", ex);
            emitter.complete();
        });

        CompletableFuture.runAsync(() -> {
            try {
                String appId = (String) request.get("appId");
                String prompt = (String) request.get("prompt");
                String sessionId = (String) request.get("sessionId");

                if (appId == null || appId.trim().isEmpty()) {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("{\"error\":\"应用ID不能为空\",\"code\":400}"));
                    emitter.complete();
                    return;
                }

                if (prompt == null || prompt.trim().isEmpty()) {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("{\"error\":\"消息内容不能为空\",\"code\":400}"));
                    emitter.complete();
                    return;
                }

                // 构建请求参数
                ApplicationParam param = ApplicationParam.builder()
                        .apiKey(apiKey)
                        .appId(appId)
                        .prompt(prompt)
                        .sessionId(sessionId != null ? sessionId : generateSessionId())
                        .incrementalOutput(true)
                        .build();

                Application application = new Application();
                Flowable<ApplicationResult> result = application.streamCall(param);

                StringBuilder fullResponse = new StringBuilder();

                result.subscribe(
                    data -> {
                        try {
                            String text = data.getOutput().getText();
                            fullResponse.append(text);

                            Map<String, Object> response = new HashMap<>();
                            response.put("text", text);
                            response.put("sessionId", data.getOutput().getSessionId());
                            response.put("timestamp", System.currentTimeMillis());
                            response.put("code", 200);

                            // 发送SSE格式的数据
                            emitter.send(SseEmitter.event()
                                .name("message")
                                .data(response)
                                .reconnectTime(3000));

                            logger.info("流式响应发送成功: {}", text);
                        } catch (IOException e) {
                            logger.error("流式响应发送失败: {}", e.getMessage(), e);
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("error")
                                    .data("{\"error\":\"响应发送失败\",\"code\":500}"));
                            } catch (IOException ex) {
                                logger.error("发送错误消息失败", ex);
                            }
                            emitter.complete();
                        }
                    },
                    error -> {
                        logger.error("流式对话调用失败: {}", error.getMessage(), error);
                        try {
                            emitter.send(SseEmitter.event()
                                .name("error")
                                .data("{\"error\":\"对话调用失败: " + error.getMessage() + "\",\"code\":500}"));
                        } catch (IOException e) {
                            logger.error("发送错误消息失败", e);
                        }
                        emitter.complete();
                    },
                    () -> {
                        // 更新会话缓存
                        updateSessionCache(param.getSessionId(), "user", prompt);
                        updateSessionCache(param.getSessionId(), "assistant", fullResponse.toString());

                        logger.info("流式对话完成，sessionId: {}", param.getSessionId());

                        try {
                            emitter.send(SseEmitter.event()
                                .name("complete")
                                .data("{\"message\":\"对话完成\",\"sessionId\":\"" + param.getSessionId() + "\"}"));
                        } catch (IOException e) {
                            logger.error("发送完成消息失败", e);
                        }
                        emitter.complete();
                    }
                );

            } catch (Exception e) {
                logger.error("流式对话初始化失败: {}", e.getMessage(), e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("{\"error\":\"对话初始化失败: " + e.getMessage() + "\",\"code\":500}"));
                } catch (IOException ex) {
                    logger.error("发送错误消息失败", ex);
                }
                emitter.complete();
            }
        });

        return emitter;
    }

    /**
     * 测试流式对话端点
     */
    @GetMapping(value = "/chat/stream/test", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testStreamChat() {
        logger.info("收到流式对话测试请求");

        SseEmitter emitter = new SseEmitter(30 * 1000L); // 30秒超时

        CompletableFuture.runAsync(() -> {
            try {
                String[] words = {"你好", "，", "我是", "百炼", "智能体", "。", "这是", "一个", "流式", "对话", "测试", "。"};

                for (int i = 0; i < words.length; i++) {
                    Thread.sleep(500); // 模拟延迟

                    Map<String, Object> response = new HashMap<>();
                    response.put("text", words[i]);
                    response.put("index", i);
                    response.put("timestamp", System.currentTimeMillis());

                    emitter.send(SseEmitter.event()
                        .name("message")
                        .data(response));

                    logger.info("发送测试数据: {}", words[i]);
                }

                emitter.send(SseEmitter.event()
                    .name("complete")
                    .data("{\"message\":\"测试完成\"}"));

                emitter.complete();

            } catch (Exception e) {
                logger.error("测试流式对话失败: {}", e.getMessage(), e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("{\"error\":\"测试失败: " + e.getMessage() + "\"}"));
                } catch (IOException ex) {
                    logger.error("发送错误消息失败", ex);
                }
                emitter.complete();
            }
        });

        return emitter;
    }

    /**
     * 多轮对话
     */
    @PostMapping("/chat/multi-turn")
    public ResponseEntity<Map<String, Object>> multiTurnChat(@RequestBody Map<String, Object> request) {
        logger.info("收到多轮对话请求: {}", request);

        try {
            String appId = (String) request.get("appId");
            String sessionId = (String) request.get("sessionId");
            @SuppressWarnings("unchecked")
            List<Map<String, String>> messages = (List<Map<String, String>>) request.get("messages");

            if (appId == null || appId.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("应用ID不能为空", 400));
            }

            if (messages == null || messages.isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("消息列表不能为空", 400));
            }

            // 构建对话历史
            StringBuilder conversation = new StringBuilder();
            for (Map<String, String> message : messages) {
                String role = message.get("role");
                String content = message.get("content");
                if ("user".equals(role)) {
                    conversation.append("用户: ").append(content).append("\n");
                } else if ("assistant".equals(role)) {
                    conversation.append("助手: ").append(content).append("\n");
                }
            }

            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(apiKey)
                    .appId(appId)
                    .prompt(conversation.toString())
                    .sessionId(sessionId != null ? sessionId : generateSessionId())
                    .build();

            Application application = new Application();
            ApplicationResult result = application.call(param);

            logger.info("多轮对话成功，sessionId: {}", param.getSessionId());

            return ResponseEntity.ok(createSuccessResponse(result.getOutput().getText(), result.getOutput().getSessionId()));

        } catch (Exception e) {
            logger.error("多轮对话失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(createErrorResponse("多轮对话失败: " + e.getMessage(), 500));
        }
    }

    /**
     * 知识库检索
     */
    @PostMapping("/knowledge/search")
    public ResponseEntity<Map<String, Object>> knowledgeSearch(@RequestBody Map<String, Object> request) {
        logger.info("收到知识库检索请求: {}", request);

        try {
            String appId = (String) request.get("appId");
            String query = (String) request.get("query");

            if (appId == null || appId.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("应用ID不能为空", 400));
            }

            if (query == null || query.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("查询内容不能为空", 400));
            }

            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(apiKey)
                    .appId(appId)
                    .prompt("请在知识库中搜索以下内容：" + query)
                    .build();

            Application application = new Application();
            ApplicationResult result = application.call(param);

            logger.info("知识库检索成功");

            return ResponseEntity.ok(createSuccessResponse(result.getOutput().getText(), result.getOutput().getSessionId()));

        } catch (Exception e) {
            logger.error("知识库检索失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(createErrorResponse("知识库检索失败: " + e.getMessage(), 500));
        }
    }

    /**
     * 文件处理
     */
    @PostMapping("/file/process")
    public ResponseEntity<Map<String, Object>> processFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("appId") String appId,
            @RequestParam("prompt") String prompt) {

        logger.info("收到文件处理请求，文件名: {}, 应用ID: {}", file.getOriginalFilename(), appId);

        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("文件不能为空", 400));
            }

            if (appId == null || appId.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("应用ID不能为空", 400));
            }

            if (prompt == null || prompt.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("处理要求不能为空", 400));
            }

            // 检查文件大小（限制为20MB）
            if (file.getSize() > 20 * 1024 * 1024) {
                return ResponseEntity.badRequest().body(createErrorResponse("文件大小不能超过20MB", 400));
            }

            // 根据文件类型处理文件内容
            String fileContent = processFileByType(file);

            // 构建包含文件内容的提示
            String fullPrompt = prompt + "\n\n" + fileContent;

            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(apiKey)
                    .appId(appId)
                    .prompt(fullPrompt)
                    .sessionId(generateSessionId())
                    .build();

            Application application = new Application();
            ApplicationResult result = application.call(param);

            logger.info("文件处理成功，文件名: {}", file.getOriginalFilename());

            return ResponseEntity.ok(createSuccessResponse(result.getOutput().getText(), result.getOutput().getSessionId()));

        } catch (Exception e) {
            logger.error("文件处理失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(createErrorResponse("文件处理失败: " + e.getMessage(), 500));
        }
    }

    /**
     * 获取会话历史
     */
    @GetMapping("/session/{sessionId}/history")
    public ResponseEntity<List<Map<String, String>>> getSessionHistory(@PathVariable String sessionId) {
        logger.info("获取会话历史，sessionId: {}", sessionId);

        List<Map<String, String>> history = sessionCache.getOrDefault(sessionId, new ArrayList<>());
        return ResponseEntity.ok(history);
    }

    /**
     * 清除会话历史
     */
    @DeleteMapping("/session/{sessionId}/history")
    public ResponseEntity<Map<String, Object>> clearSessionHistory(@PathVariable String sessionId) {
        logger.info("清除会话历史，sessionId: {}", sessionId);

        sessionCache.remove(sessionId);
        return ResponseEntity.ok(createSuccessResponse("会话历史已清除", sessionId));
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "session_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 更新会话缓存
     */
    private void updateSessionCache(String sessionId, String role, String content) {
        if (sessionId == null || content == null) {
            return;
        }

        sessionCache.computeIfAbsent(sessionId, k -> new ArrayList<>());

        Map<String, String> message = new HashMap<>();
        message.put("role", role);
        message.put("content", content);
        message.put("timestamp", String.valueOf(System.currentTimeMillis()));

        List<Map<String, String>> messages = sessionCache.get(sessionId);
        messages.add(message);

        // 限制会话历史长度（最多保留50条消息）
        if (messages.size() > 50) {
            messages.subList(0, messages.size() - 50).clear();
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(String text, String sessionId) {
        Map<String, Object> response = new HashMap<>();
        response.put("text", text);
        response.put("sessionId", sessionId);
        response.put("code", 200);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 根据文件类型处理文件内容
     */
    private String processFileByType(MultipartFile file) throws Exception {
        String fileName = file.getOriginalFilename();
        String fileExtension = "";

        if (fileName != null && fileName.contains(".")) {
            fileExtension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        }

        switch (fileExtension) {
            case ".jpg":
            case ".jpeg":
            case ".png":
            case ".gif":
            case ".bmp":
            case ".webp":
            case ".svg":
                return processImageFile(file);

            case ".pdf":
                return processPdfFile(file);

            case ".doc":
            case ".docx":
                return processWordFile(file);

            case ".xls":
            case ".xlsx":
                return processExcelFile(file);

            case ".ppt":
            case ".pptx":
                return processPowerPointFile(file);

            case ".txt":
            case ".md":
            case ".json":
            case ".csv":
            case ".xml":
            case ".html":
            case ".js":
            case ".py":
            case ".java":
            case ".cpp":
            case ".c":
            case ".h":
                return processTextFile(file);

            default:
                return processGenericFile(file);
        }
    }

    /**
     * 处理图片文件
     */
    private String processImageFile(MultipartFile file) {
        return String.format("文件类型：图片文件\n文件名：%s\n文件大小：%s\n\n" +
                "注意：这是一个图片文件（%s），我可以帮您分析图片的基本信息，但无法直接查看图片内容。" +
                "如果您需要分析图片内容，请描述图片中的内容，或者告诉我您希望了解图片的哪些方面。",
                file.getOriginalFilename(),
                formatFileSize(file.getSize()),
                getFileExtension(file.getOriginalFilename()));
    }

    /**
     * 处理PDF文件
     */
    private String processPdfFile(MultipartFile file) {
        return String.format("文件类型：PDF文档\n文件名：%s\n文件大小：%s\n\n" +
                "注意：这是一个PDF文件，我可以帮您分析PDF的基本信息。" +
                "如果您需要分析PDF内容，请告诉我您希望了解文档的哪些方面，" +
                "或者您可以将PDF中的关键内容复制粘贴给我进行分析。",
                file.getOriginalFilename(),
                formatFileSize(file.getSize()));
    }

    /**
     * 处理Word文件
     */
    private String processWordFile(MultipartFile file) {
        return String.format("文件类型：Word文档\n文件名：%s\n文件大小：%s\n\n" +
                "注意：这是一个Word文档（%s），我可以帮您分析文档的基本信息。" +
                "如果您需要分析文档内容，请告诉我您希望了解文档的哪些方面，" +
                "或者您可以将文档中的关键内容复制粘贴给我进行分析。",
                file.getOriginalFilename(),
                formatFileSize(file.getSize()),
                getFileExtension(file.getOriginalFilename()));
    }

    /**
     * 处理Excel文件
     */
    private String processExcelFile(MultipartFile file) {
        return String.format("文件类型：Excel表格\n文件名：%s\n文件大小：%s\n\n" +
                "注意：这是一个Excel表格文件（%s），我可以帮您分析表格的基本信息。" +
                "如果您需要分析表格数据，请告诉我您希望了解数据的哪些方面，" +
                "或者您可以将表格中的关键数据复制粘贴给我进行分析。",
                file.getOriginalFilename(),
                formatFileSize(file.getSize()),
                getFileExtension(file.getOriginalFilename()));
    }

    /**
     * 处理PowerPoint文件
     */
    private String processPowerPointFile(MultipartFile file) {
        return String.format("文件类型：PowerPoint演示文稿\n文件名：%s\n文件大小：%s\n\n" +
                "注意：这是一个PowerPoint演示文稿（%s），我可以帮您分析演示文稿的基本信息。" +
                "如果您需要分析演示内容，请告诉我您希望了解演示文稿的哪些方面，" +
                "或者您可以将演示文稿中的关键内容复制粘贴给我进行分析。",
                file.getOriginalFilename(),
                formatFileSize(file.getSize()),
                getFileExtension(file.getOriginalFilename()));
    }

    /**
     * 处理文本文件
     */
    private String processTextFile(MultipartFile file) throws Exception {
        String content = new String(file.getBytes(), StandardCharsets.UTF_8);
        return String.format("文件类型：文本文件\n文件名：%s\n文件大小：%s\n\n文件内容：\n%s",
                file.getOriginalFilename(),
                formatFileSize(file.getSize()),
                content);
    }

    /**
     * 处理其他类型文件
     */
    private String processGenericFile(MultipartFile file) {
        return String.format("文件类型：其他文件\n文件名：%s\n文件大小：%s\n\n" +
                "注意：这是一个%s文件，我可以帮您分析文件的基本信息。" +
                "如果您需要分析文件内容，请告诉我您希望了解文件的哪些方面。",
                file.getOriginalFilename(),
                formatFileSize(file.getSize()),
                getFileExtension(file.getOriginalFilename()));
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName != null && fileName.contains(".")) {
            return fileName.substring(fileName.lastIndexOf("."));
        }
        return "未知格式";
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        }
        if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        }
        if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024));
        }
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String error, int code) {
        Map<String, Object> response = new HashMap<>();
        response.put("error", error);
        response.put("code", code);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}