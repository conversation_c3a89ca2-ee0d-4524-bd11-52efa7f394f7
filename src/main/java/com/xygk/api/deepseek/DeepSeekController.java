package com.xygk.api.deepseek;

import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Arrays;

/**
 * 
 * <AUTHOR>
 * @date 2025/05/07 14:34
 */
@RestController
@RequestMapping("/api/deepseek")
@Api(tags = "deepSeek模型")
public class DeepSeekController {

    private static final Logger logger = LoggerFactory.getLogger(DeepSeekController.class);
    
    @Value("${deepseek.api-key}")
    private String apiKey;
    
    @Value("${deepseek.api-endpoint}")
    private String apiEndpoint;

    @Value("${deepseek.mock-mode:false}")
    private boolean mockMode;
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    // 存储会话历史
    private final Map<String, List<JSONObject>> conversationHistory = new ConcurrentHashMap<>();
    
    // 支持的模型列表
    private static final List<String> SUPPORTED_MODELS = Arrays.asList(
        "deepseek-chat",
        "deepseek-coder",
        "deepseek-reasoner",
        "deepseek-v2",
        "deepseek-vl"
    );

    /**
     * DeepSeek模型对话
     * @param request 请求参数
     * @return 对话响应
     */
    @PostMapping("/chat")
    @ApiOperation("deepseek模型对话")
    public Map<String, Object> chat(@RequestBody Map<String, Object> request) {
        try {
            String model = (String) request.get("model");
            String prompt = (String) request.get("prompt");
            String sessionId = (String) request.get("sessionId");

            if (model == null || model.trim().isEmpty()) {
                model = "deepseek-chat";
            }

            if (prompt == null || prompt.trim().isEmpty()) {
                return createErrorResponse("消息内容不能为空", 400);
            }

            if (sessionId == null || sessionId.trim().isEmpty()) {
                sessionId = "session_" + System.currentTimeMillis();
            }

            // 验证模型是否支持
            if (!SUPPORTED_MODELS.contains(model)) {
                return createErrorResponse("不支持的模型: " + model, 400);
            }

            // 调用实际的DeepSeek API
            return callDeepSeekAPI(model, prompt, sessionId);

        } catch (Exception e) {
            logger.error("DeepSeek对话失败: {}", e.getMessage(), e);
            return createErrorResponse("对话失败: " + e.getMessage(), 500);
        }
    }



    /**
     * 调用DeepSeek API
     */
    private Map<String, Object> callDeepSeekAPI(String model, String prompt, String sessionId) {
        // 如果启用模拟模式，返回模拟响应
        if (mockMode) {
            return createMockResponse(model, prompt, sessionId);
        }

        try {
            // 构建请求体
            HttpEntity<Map<String, Object>> entity = getMapHttpEntity(model, prompt);

            logger.info("调用DeepSeek API: model={}, prompt={}", model, prompt);

            org.springframework.http.ResponseEntity<String> response =
                restTemplate.postForEntity(apiEndpoint, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                // 解析响应
                JSONObject responseJson = JSON.parseObject(response.getBody());

                if (responseJson.containsKey("choices") &&
                        !responseJson.getJSONArray("choices").isEmpty()) {

                    JSONObject choice = responseJson.getJSONArray("choices").getJSONObject(0);
                    JSONObject message = choice.getJSONObject("message");
                    String content = message.getString("content");

                    Map<String, Object> result = new HashMap<>();
                    result.put("text", content);
                    result.put("content", content);
                    result.put("model", model);
                    result.put("sessionId", sessionId);
                    result.put("timestamp", System.currentTimeMillis());
                    result.put("code", 200);

                    logger.info("DeepSeek API调用成功: {}", content.substring(0, Math.min(content.length(), 100)));
                    return result;
                } else {
                    logger.error("DeepSeek API响应格式错误: {}", response.getBody());
                    return createErrorResponse("API响应格式错误", 500);
                }
            } else {
                logger.error("DeepSeek API调用失败: {}", response.getStatusCode());
                return createErrorResponse("API调用失败: " + response.getStatusCode(), 500);
            }

        } catch (org.springframework.web.client.HttpClientErrorException e) {
            String errorBody = e.getResponseBodyAsString();
            logger.error("DeepSeek API调用失败: {} - {}", e.getStatusCode(), errorBody);

            // 解析错误信息
            if (e.getStatusCode().value() == 402) {
                return createErrorResponse("DeepSeek API余额不足，请充值后重试", 402);
            } else if (e.getStatusCode().value() == 401) {
                return createErrorResponse("DeepSeek API密钥无效，请检查配置", 401);
            } else if (e.getStatusCode().value() == 429) {
                return createErrorResponse("DeepSeek API请求频率过高，请稍后重试", 429);
            } else {
                return createErrorResponse("DeepSeek API调用失败: " + e.getStatusCode(), e.getStatusCode().value());
            }
        } catch (Exception e) {
            logger.error("调用DeepSeek API异常: {}", e.getMessage(), e);
            return createErrorResponse("API调用异常: " + e.getMessage(), 500);
        }
    }

    @NotNull
    private HttpEntity<Map<String, Object>> getMapHttpEntity(String model, String prompt) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);
        requestBody.put("stream", false);

        // 构建消息列表
        List<Map<String, String>> messages = new ArrayList<>();
        Map<String, String> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", prompt);
        messages.add(userMessage);
        requestBody.put("messages", messages);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + apiKey);
        headers.set("Content-Type", "application/json");

        // 发送请求
        return new HttpEntity<>(requestBody, headers);
    }

    /**
     * 创建模拟响应
     */
    private Map<String, Object> createMockResponse(String model, String prompt, String sessionId) {
        logger.info("使用模拟模式响应DeepSeek请求: model={}, prompt={}", model, prompt);

        String responseText;
        switch (model) {
            case "deepseek-coder":
                responseText = "我是DeepSeek Coder，专门用于代码生成和编程任务。您的问题是：" + prompt + "\n\n这是一个模拟回复，因为API余额不足。";
                break;
            case "deepseek-reasoner":
                responseText = "我是DeepSeek Reasoner，擅长逻辑推理和复杂分析。针对您的问题：" + prompt + "\n\n这是一个模拟回复，因为API余额不足。";
                break;
            case "deepseek-v2":
                responseText = "我是DeepSeek V2，最新版本的模型。关于您的问题：" + prompt + "\n\n这是一个模拟回复，因为API余额不足。";
                break;
            case "deepseek-vl":
                responseText = "我是DeepSeek VL，支持视觉理解的多模态模型。您的问题：" + prompt + "\n\n这是一个模拟回复，因为API余额不足。";
                break;
            default: // deepseek-chat
                responseText = "您好！我是DeepSeek Chat。您的问题是：" + prompt + "\n\n这是一个模拟回复，因为API余额不足，无法调用真实的DeepSeek API。请充值后体验真实的AI对话功能。";
                break;
        }

        Map<String, Object> response = new HashMap<>();
        response.put("text", responseText);
        response.put("content", responseText);
        response.put("model", model);
        response.put("sessionId", sessionId);
        response.put("timestamp", System.currentTimeMillis());
        response.put("code", 200);
        response.put("mock", true);

        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String error, int code) {
        Map<String, Object> response = new HashMap<>();
        response.put("error", error);
        response.put("code", code);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
    
    @GetMapping("/models")
    @ApiOperation("获取支持的模型列表")
    public List<String> getSupportedModels() {
        return SUPPORTED_MODELS;
    }
    
    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("deepseek模型流式对话")
    public SseEmitter streamChat(@RequestParam String message, 
                               @RequestParam(required = false) String sessionId,
                               @RequestParam(defaultValue = "deepseek-chat") String model) {
        // 验证模型是否支持
        if (!SUPPORTED_MODELS.contains(model)) {
            throw new IllegalArgumentException("不支持的模型: " + model);
        }
        
        SseEmitter emitter = new SseEmitter(30 * 1000L);
        
        // 获取或创建会话历史
        List<JSONObject> history = conversationHistory.computeIfAbsent(sessionId, k -> new ArrayList<>());
        
        // 添加用户消息到历史记录
        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");
        userMessage.put("content", message);
        history.add(userMessage);
        
        emitter.onCompletion(() -> {
            logger.info("SSE连接已完成");
        });
        
        emitter.onTimeout(() -> {
            logger.warn("SSE连接超时");
            emitter.complete();
        });
        
        emitter.onError((ex) -> {
            logger.error("SSE连接错误", ex);
            emitter.complete();
        });
        
        try {
            emitter.send(SseEmitter.event()
                    .id("init")
                    .name("init")
                    .data("连接已建立")
                    .reconnectTime(3000));
        } catch (Exception e) {
            logger.error("发送初始化事件失败", e);
            emitter.completeWithError(e);
            return emitter;
        }
        
        CompletableFuture.runAsync(() -> {
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.set("Authorization", "Bearer " + apiKey);
                headers.set("Content-Type", "application/json; charset=UTF-8");
                headers.set("Accept", "text/event-stream");
                
                // 构建请求体
                JSONObject requestBody = new JSONObject();
                requestBody.put("model", model);
                requestBody.put("stream", true);
                
                // 确保消息内容使用UTF-8编码
                List<JSONObject> encodedHistory = new ArrayList<>();
                for (JSONObject msg : history) {
                    JSONObject encodedMsg = new JSONObject();
                    encodedMsg.put("role", msg.getString("role"));
                    encodedMsg.put("content", new String(msg.getString("content").getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8));
                    encodedHistory.add(encodedMsg);
                }
                requestBody.put("messages", encodedHistory);
                
                String requestJson = requestBody.toJSONString();
                logger.info("请求体: {}", requestJson);
                
                restTemplate.execute(
                    apiEndpoint,
                    HttpMethod.POST,
                    request -> {
                        request.getHeaders().addAll(headers);
                        request.getBody().write(requestJson.getBytes(StandardCharsets.UTF_8));
                    },
                    response -> {
                        StringBuilder assistantResponse = new StringBuilder();
                        try (BufferedReader reader = new BufferedReader(
                                new InputStreamReader(response.getBody(), StandardCharsets.UTF_8))) {
                            String line;
                            while ((line = reader.readLine()) != null) {
                                if (line.trim().isEmpty()) {
                                    continue;
                                }
                                
                                if (line.startsWith("data: ")) {
                                    String data = line.substring(6).trim();
                                    
                                    if ("[DONE]".equals(data)) {
                                        // 添加助手回复到历史记录
                                        JSONObject assistantMessage = new JSONObject();
                                        assistantMessage.put("role", "assistant");
                                        assistantMessage.put("content", assistantResponse.toString());
                                        history.add(assistantMessage);
                                        
                                        emitter.send(SseEmitter.event()
                                            .name("done")
                                            .data("[DONE]"));
                                        break;
                                    }
                                    
                                    try {
                                        JSONObject dataJson = JSON.parseObject(data);
                                        if (dataJson.containsKey("choices")) {
                                            JSONObject choice = dataJson.getJSONArray("choices")
                                                .getJSONObject(0);
                                            JSONObject delta = choice.getJSONObject("delta");
                                            if (delta.containsKey("content")) {
                                                String content = delta.getString("content");
                                                assistantResponse.append(content);
                                                emitter.send(SseEmitter.event()
                                                    .name("message")
                                                    .data(content));
                                            }
                                        }
                                    } catch (Exception e) {
                                        logger.error("解析响应数据失败", e);
                                    }
                                }
                            }
                        }
                        return null;
                    }
                );
                
                emitter.complete();
            } catch (Exception e) {
                logger.error("处理流式响应失败", e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("处理响应失败: " + e.getMessage()));
                } catch (Exception ex) {
                    logger.error("发送错误消息失败", ex);
                }
                emitter.complete();
            }
        });
        
        return emitter;
    }
    
    @PostMapping("/clear")
    @ApiOperation("清除会话历史")
    public void clearHistory(@RequestParam String sessionId) {
        conversationHistory.remove(sessionId);
    }
}
