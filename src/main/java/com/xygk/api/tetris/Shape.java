package com.xygk.api.tetris;

import java.awt.Color;
import java.util.Random;

public class Shape {
    private static final Random random = new Random();
    private static final Shape[] SHAPES = {
        // I
        new Shape(new boolean[][] {
            {false, false, false, false},
            {true, true, true, true},
            {false, false, false, false},
            {false, false, false, false}
        }, Color.CYAN),
        
        // J
        new Shape(new boolean[][] {
            {true, false, false, false},
            {true, true, true, false},
            {false, false, false, false},
            {false, false, false, false}
        }, Color.BLUE),
        
        // L
        new Shape(new boolean[][] {
            {false, false, true, false},
            {true, true, true, false},
            {false, false, false, false},
            {false, false, false, false}
        }, Color.ORANGE),
        
        // O
        new Shape(new boolean[][] {
            {true, true, false, false},
            {true, true, false, false},
            {false, false, false, false},
            {false, false, false, false}
        }, Color.YELLOW),
        
        // S
        new Shape(new boolean[][] {
            {false, true, true, false},
            {true, true, false, false},
            {false, false, false, false},
            {false, false, false, false}
        }, Color.GREEN),
        
        // T
        new Shape(new boolean[][] {
            {false, true, false, false},
            {true, true, true, false},
            {false, false, false, false},
            {false, false, false, false}
        }, Color.MAGENTA),
        
        // Z
        new Shape(new boolean[][] {
            {true, true, false, false},
            {false, true, true, false},
            {false, false, false, false},
            {false, false, false, false}
        }, Color.RED)
    };
    
    private boolean[][] shape;
    private final Color color;
    
    private Shape(boolean[][] shape, Color color) {
        this.shape = shape;
        this.color = color;
    }
    
    public static Shape getRandomShape() {
        return SHAPES[random.nextInt(SHAPES.length)];
    }
    
    public boolean[][] getShape() {
        return shape;
    }
    
    public Color getColor() {
        return color;
    }
    
    public void rotate() {
        boolean[][] rotated = new boolean[4][4];
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                rotated[j][3-i] = shape[i][j];
            }
        }
        shape = rotated;
    }
    
    public void rotateBack() {
        boolean[][] rotated = new boolean[4][4];
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                rotated[3-j][i] = shape[i][j];
            }
        }
        shape = rotated;
    }
} 