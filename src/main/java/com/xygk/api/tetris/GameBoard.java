package com.xygk.api.tetris;

import javax.swing.*;
import java.awt.*;
import java.util.Random;

public class GameBoard extends JPanel {
    private final int boardWidth;
    private final int boardHeight;
    private final int blockSize;
    private final Color[][] board;
    private Shape currentPiece;
    private int currentX;
    private int currentY;
    private boolean isPaused;
    private final Random random;
    
    public GameBoard(int width, int height, int blockSize) {
        this.boardWidth = width;
        this.boardHeight = height;
        this.blockSize = blockSize;
        this.board = new Color[height][width];
        this.random = new Random();
        this.isPaused = false;
        
        setBackground(Color.BLACK);
        newPiece();
    }
    
    public boolean newPiece() {
        currentPiece = Shape.getRandomShape();
        currentX = boardWidth / 2 - 2;
        currentY = 0;
        
        return isValidPosition();
    }
    
    private boolean isValidPosition() {
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                if (currentPiece.getShape()[i][j]) {
                    int x = currentX + j;
                    int y = currentY + i;
                    
                    if (x < 0 || x >= boardWidth || y >= boardHeight) {
                        return false;
                    }
                    
                    if (y >= 0 && board[y][x] != null) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    
    public boolean moveLeft() {
        if (isPaused) return false;
        currentX--;
        if (!isValidPosition()) {
            currentX++;
            return false;
        }
        repaint();
        return true;
    }
    
    public boolean moveRight() {
        if (isPaused) return false;
        currentX++;
        if (!isValidPosition()) {
            currentX--;
            return false;
        }
        repaint();
        return true;
    }
    
    public boolean moveDown() {
        if (isPaused) return false;
        currentY++;
        if (!isValidPosition()) {
            currentY--;
            mergePiece();
            return false;
        }
        repaint();
        return true;
    }
    
    public void rotate() {
        if (isPaused) return;
        currentPiece.rotate();
        if (!isValidPosition()) {
            currentPiece.rotateBack();
        }
        repaint();
    }
    
    public void dropDown() {
        if (isPaused) return;
        while (moveDown());
    }
    
    private void mergePiece() {
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                if (currentPiece.getShape()[i][j]) {
                    int x = currentX + j;
                    int y = currentY + i;
                    if (y >= 0) {
                        board[y][x] = currentPiece.getColor();
                    }
                }
            }
        }
    }
    
    public int clearLines() {
        int linesCleared = 0;
        for (int i = boardHeight - 1; i >= 0; i--) {
            boolean lineIsFull = true;
            for (int j = 0; j < boardWidth; j++) {
                if (board[i][j] == null) {
                    lineIsFull = false;
                    break;
                }
            }
            
            if (lineIsFull) {
                linesCleared++;
                // 将上面的行下移
                for (int k = i; k > 0; k--) {
                    System.arraycopy(board[k-1], 0, board[k], 0, boardWidth);
                }
                // 清空最上面的行
                for (int j = 0; j < boardWidth; j++) {
                    board[0][j] = null;
                }
                i++; // 重新检查当前行
            }
        }
        repaint();
        return linesCleared;
    }
    
    public void setPaused(boolean paused) {
        this.isPaused = paused;
        repaint();
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        
        // 绘制已固定的方块
        for (int i = 0; i < boardHeight; i++) {
            for (int j = 0; j < boardWidth; j++) {
                if (board[i][j] != null) {
                    drawBlock(g, j, i, board[i][j]);
                }
            }
        }
        
        // 绘制当前方块
        if (currentPiece != null) {
            for (int i = 0; i < 4; i++) {
                for (int j = 0; j < 4; j++) {
                    if (currentPiece.getShape()[i][j]) {
                        drawBlock(g, currentX + j, currentY + i, currentPiece.getColor());
                    }
                }
            }
        }
        
        // 绘制暂停状态
        if (isPaused) {
            g.setColor(new Color(0, 0, 0, 128));
            g.fillRect(0, 0, getWidth(), getHeight());
            g.setColor(Color.WHITE);
            g.setFont(new Font("Arial", Font.BOLD, 30));
            String pauseText = "已暂停";
            FontMetrics fm = g.getFontMetrics();
            int x = (getWidth() - fm.stringWidth(pauseText)) / 2;
            int y = (getHeight() - fm.getHeight()) / 2 + fm.getAscent();
            g.drawString(pauseText, x, y);
        }
    }
    
    private void drawBlock(Graphics g, int x, int y, Color color) {
        g.setColor(color);
        g.fillRect(x * blockSize, y * blockSize, blockSize - 1, blockSize - 1);
        g.setColor(color.brighter());
        g.drawLine(x * blockSize, y * blockSize, x * blockSize + blockSize - 1, y * blockSize);
        g.drawLine(x * blockSize, y * blockSize, x * blockSize, y * blockSize + blockSize - 1);
        g.setColor(color.darker());
        g.drawLine(x * blockSize + blockSize - 1, y * blockSize, x * blockSize + blockSize - 1, y * blockSize + blockSize - 1);
        g.drawLine(x * blockSize, y * blockSize + blockSize - 1, x * blockSize + blockSize - 1, y * blockSize + blockSize - 1);
    }
} 