package com.xygk.api.tetris;

import javax.swing.*;
import java.awt.*;
import java.awt.event.*;

public class TetrisGame extends J<PERSON>rame {
    private static final int BOARD_WIDTH = 10;
    private static final int BOARD_HEIGHT = 20;
    private static final int BLOCK_SIZE = 30;
    
    private GameBoard gameBoard;
    private JLabel scoreLabel;
    private JLabel levelLabel;
    private Timer timer;
    private int score = 0;
    private int level = 1;
    
    public TetrisGame() {
        setTitle("俄罗斯方块");
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setResizable(false);
        
        // 创建游戏面板
        gameBoard = new GameBoard(BOARD_WIDTH, BOARD_HEIGHT, BLOCK_SIZE);
        gameBoard.setPreferredSize(new Dimension(BOARD_WIDTH * BLOCK_SIZE, BOARD_HEIGHT * BLOCK_SIZE));
        
        // 创建信息面板
        JPanel infoPanel = new JPanel();
        infoPanel.setLayout(new BoxLayout(infoPanel, BoxLayout.Y_AXIS));
        scoreLabel = new JLabel("分数: 0");
        levelLabel = new JLabel("等级: 1");
        infoPanel.add(scoreLabel);
        infoPanel.add(levelLabel);
        
        // 创建主面板
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BorderLayout());
        mainPanel.add(gameBoard, BorderLayout.CENTER);
        mainPanel.add(infoPanel, BorderLayout.EAST);
        
        // 添加键盘监听
        addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                switch (e.getKeyCode()) {
                    case KeyEvent.VK_LEFT:
                        gameBoard.moveLeft();
                        break;
                    case KeyEvent.VK_RIGHT:
                        gameBoard.moveRight();
                        break;
                    case KeyEvent.VK_DOWN:
                        gameBoard.moveDown();
                        break;
                    case KeyEvent.VK_UP:
                        gameBoard.rotate();
                        break;
                    case KeyEvent.VK_SPACE:
                        gameBoard.dropDown();
                        break;
                    case KeyEvent.VK_P:
                        togglePause();
                        break;
                }
            }
        });
        
        // 设置游戏循环
        timer = new Timer(1000, e -> {
            if (!gameBoard.moveDown()) {
                // 如果方块不能继续下落，检查是否有完整的行
                int linesCleared = gameBoard.clearLines();
                if (linesCleared > 0) {
                    updateScore(linesCleared);
                }
                // 生成新的方块
                if (!gameBoard.newPiece()) {
                    gameOver();
                }
            }
        });
        
        add(mainPanel);
        pack();
        setLocationRelativeTo(null);
    }
    
    private void updateScore(int linesCleared) {
        // 根据消除的行数计算得分
        int points;
        switch (linesCleared) {
            case 1: points = 100; break;
            case 2: points = 300; break;
            case 3: points = 500; break;
            case 4: points = 800; break;
            default: points = 0;
        }
        
        score += points * level;
        scoreLabel.setText("分数: " + score);
        
        // 每1000分升一级
        int newLevel = score / 1000 + 1;
        if (newLevel > level) {
            level = newLevel;
            levelLabel.setText("等级: " + level);
            // 提高游戏速度
            timer.setDelay(1000 / level);
        }
    }
    
    private void togglePause() {
        if (timer.isRunning()) {
            timer.stop();
            gameBoard.setPaused(true);
        } else {
            timer.start();
            gameBoard.setPaused(false);
        }
    }
    
    private void gameOver() {
        timer.stop();
        JOptionPane.showMessageDialog(this, 
            "游戏结束！\n最终得分: " + score, 
            "游戏结束", 
            JOptionPane.INFORMATION_MESSAGE);
        System.exit(0);
    }
    
    public void start() {
        timer.start();
        setVisible(true);
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            TetrisGame game = new TetrisGame();
            game.start();
        });
    }
} 