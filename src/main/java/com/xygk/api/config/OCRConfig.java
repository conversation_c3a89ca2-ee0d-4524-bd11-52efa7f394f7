package com.xygk.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

@Slf4j
@Configuration
public class OCRConfig {
    
    @Value("${paddle.ocr.model-dir:models/ocr}")
    private String paddleModelDir;
    
    @Value("${tesseract.ocr.data-path:tessdata}")
    private String tesseractDataPath;
    
    @Value("${paddle.ocr.enable:true}")
    private boolean paddleOcrEnabled;
    
    @Value("${tesseract.ocr.enable:true}")
    private boolean tesseractOcrEnabled;
    
    @PostConstruct
    public void init() {
        log.info("初始化OCR配置...");
        
        // 创建必要的目录
        createDirectories();
        
        // 检查模型文件
        checkModels();
        
        log.info("OCR配置初始化完成");
        log.info("PaddleOCR启用状态: {}", paddleOcrEnabled);
        log.info("Tesseract启用状态: {}", tesseractOcrEnabled);
    }
    
    private void createDirectories() {
        try {
            // 创建PaddleOCR模型目录
            if (paddleOcrEnabled) {
                Files.createDirectories(Paths.get(paddleModelDir));
                log.info("PaddleOCR模型目录: {}", new File(paddleModelDir).getAbsolutePath());
            }
            
            // 创建Tesseract数据目录
            if (tesseractOcrEnabled) {
                Files.createDirectories(Paths.get(tesseractDataPath));
                log.info("Tesseract数据目录: {}", new File(tesseractDataPath).getAbsolutePath());
            }
            
        } catch (Exception e) {
            log.error("创建OCR目录失败: {}", e.getMessage());
        }
    }
    
    private void checkModels() {
        if (paddleOcrEnabled) {
            checkPaddleModels();
        }
        
        if (tesseractOcrEnabled) {
            checkTesseractData();
        }
    }
    
    private void checkPaddleModels() {
        File modelDir = new File(paddleModelDir);
        if (!modelDir.exists() || modelDir.listFiles() == null || modelDir.listFiles().length == 0) {
            log.warn("PaddleOCR模型目录为空: {}", modelDir.getAbsolutePath());
            log.info("PaddleOCR将在首次使用时自动下载模型，这可能需要一些时间");
            log.info("如果网络环境不佳，建议手动下载模型文件到: {}", modelDir.getAbsolutePath());
            log.info("模型下载地址: https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.6/doc/doc_ch/models_list.md");
        } else {
            log.info("PaddleOCR模型目录存在，包含 {} 个文件", modelDir.listFiles().length);
        }
    }
    
    private void checkTesseractData() {
        File dataDir = new File(tesseractDataPath);
        if (!dataDir.exists()) {
            log.warn("Tesseract数据目录不存在: {}", dataDir.getAbsolutePath());
            log.info("请下载Tesseract训练数据文件到: {}", dataDir.getAbsolutePath());
            log.info("中文数据下载地址: https://github.com/tesseract-ocr/tessdata/raw/main/chi_sim.traineddata");
            log.info("英文数据下载地址: https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata");
        } else {
            File[] dataFiles = dataDir.listFiles((dir, name) -> name.endsWith(".traineddata"));
            if (dataFiles == null || dataFiles.length == 0) {
                log.warn("Tesseract数据目录中没有找到训练数据文件(.traineddata)");
            } else {
                log.info("Tesseract数据目录包含 {} 个训练数据文件", dataFiles.length);
                for (File file : dataFiles) {
                    log.info("  - {}", file.getName());
                }
            }
        }
    }
    
    // Getter方法
    public String getPaddleModelDir() {
        return paddleModelDir;
    }
    
    public String getTesseractDataPath() {
        return tesseractDataPath;
    }
    
    public boolean isPaddleOcrEnabled() {
        return paddleOcrEnabled;
    }
    
    public boolean isTesseractOcrEnabled() {
        return tesseractOcrEnabled;
    }
}
