package com.xygk.api.ali;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/ali/qwen")
@Api(tags = "阿里通义千问大模型")
public class QwenController {
    
    private static final Logger logger = LoggerFactory.getLogger(QwenController.class);
    
    @Autowired
    private QwenClient qwenClient;
    
    private final List<QwenClient.Message> conversationHistory = new ArrayList<>();
    
    /**
     * 单轮对话
     * @param model 模型名称
     * @param prompt 用户输入
     * @return 模型响应
     */
    @PostMapping("/chat")
    public String chat(@RequestParam(defaultValue = "qwen-turbo") String model,
                      @RequestParam String prompt) {
        List<QwenClient.Message> messages = qwenClient.getMessages(prompt);
        return qwenClient.callQwen(model, messages);
    }
    
    /**
     * 多轮对话
     * @param model 模型名称
     * @param prompt 用户输入
     * @return 模型响应
     */
    @PostMapping("/conversation")
    public String conversation(@RequestParam(defaultValue = "qwen-turbo") String model,
                             @RequestParam String prompt) {
        conversationHistory.add(new QwenClient.Message("user", prompt));
        String response = qwenClient.callQwen(model, conversationHistory);
        conversationHistory.add(new QwenClient.Message("assistant", response));
        return response;
    }
    
    /**
     * 流式输出
     * @param model 模型名称
     * @param prompt 用户输入
     * @return 模型响应
     */
    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter stream(@RequestParam(defaultValue = "qwen-turbo") String model,
                                          @RequestParam String prompt) {
        SseEmitter emitter = new SseEmitter(30 * 1000L);
        
        // 设置SSE相关的响应头
        emitter.onCompletion(() -> {
            logger.info("SSE连接已完成");
        });
        
        emitter.onTimeout(() -> {
            logger.warn("SSE连接超时");
            emitter.complete();
        });
        
        emitter.onError((ex) -> {
            logger.error("SSE连接错误", ex);
            emitter.complete();
        });
        
        // 发送一个初始化事件
        try {
            emitter.send(SseEmitter.event()
                    .id("init")
                    .name("init")
                    .data("连接已建立")
                    .reconnectTime(3000));
        } catch (Exception e) {
            logger.error("发送初始化事件失败", e);
            emitter.completeWithError(e);
            return emitter;
        }
        
        List<QwenClient.Message> messages = qwenClient.getMessages(prompt);
        qwenClient.streamCallQwen(model, messages, emitter);
        
        return emitter;
    }
    
    /**
     * 文档上传和对话
     * @param model 模型名称
     * @param prompt 用户输入
     * @param file 上传的文件
     * @return 模型响应
     */
    @PostMapping("/upload")
    public String upload(@RequestParam(defaultValue = "qwen-turbo") String model,
                        @RequestParam String prompt,
                        @RequestParam("file") MultipartFile file) {
        List<QwenClient.Message> messages = qwenClient.getMessages(prompt);
        return qwenClient.callQwenWithFile(model, messages, file);
    }
    
    /**
     * 清除对话历史
     */
    @PostMapping("/clear")
    public void clearHistory() {
        conversationHistory.clear();
    }
} 