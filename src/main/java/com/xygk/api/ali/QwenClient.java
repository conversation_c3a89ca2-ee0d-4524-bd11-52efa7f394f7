package com.xygk.api.ali;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Component
public class QwenClient {
    
    private static final Logger logger = LoggerFactory.getLogger(QwenClient.class);
    
    @Value("${aliyun.qwen.api-key}")
    private String apiKey;
    @Value("${aliyun.qwen.api-endpoint}")
    private String BASE_URL;
    
    /**
     * 调用通义千问模型
     * @param model 模型名称
     * @param messages 消息列表
     * @return 模型响应
     */
    public String callQwen(String model, List<Message> messages) {
        logger.info("开始调用通义千问API，model: {}, messages: {}", model, messages);
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(BASE_URL);
            httpPost.setHeader("Authorization", "Bearer " + apiKey);
            httpPost.setHeader("Content-Type", "application/json");
            
            JSONObject requestBody = new JSONObject();
            requestBody.put("model", model);
            requestBody.put("messages", messages);
            
            String requestJson = requestBody.toJSONString();
            logger.info("请求体: {}", requestJson);
            
            httpPost.setEntity(new StringEntity(requestJson, "UTF-8"));
            
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                String responseString = EntityUtils.toString(entity);
                logger.info("API响应: {}", responseString);
                
                JSONObject responseJson = JSON.parseObject(responseString);
                
                if (response.getStatusLine().getStatusCode() == 200) {
                    JSONArray choices = responseJson.getJSONArray("choices");
                    if (choices == null || choices.isEmpty()) {
                        logger.error("API响应中没有choices字段或为空: {}", responseString);
                        throw new RuntimeException("API响应格式错误");
                    }
                    JSONObject choice = choices.getJSONObject(0);
                    JSONObject message = choice.getJSONObject("message");
                    if (message == null) {
                        logger.error("API响应中没有message字段: {}", responseString);
                        throw new RuntimeException("API响应格式错误");
                    }
                    String content = message.getString("content");
                    if (content == null) {
                        logger.error("API响应中没有content字段: {}", responseString);
                        throw new RuntimeException("API响应格式错误");
                    }
                    return content;
                } else {
                    throw new RuntimeException("调用通义千问API失败: " + responseString);
                }
            }
        } catch (IOException e) {
            logger.error("HTTP请求失败", e);
            throw new RuntimeException("HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 流式输出
     * @param model 模型名称
     * @param messages 消息列表
     * @param emitter SseEmitter
     */
    public void streamCallQwen(String model, List<Message> messages, SseEmitter emitter) {
        CompletableFuture.runAsync(() -> {
            int maxRetries = 3;
            int retryCount = 0;
            boolean success = false;
            boolean isEmitterCompleted = false;
            
            while (retryCount < maxRetries && !success && !isEmitterCompleted) {
                try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                    HttpPost httpPost = new HttpPost(BASE_URL);
                    httpPost.setHeader("Authorization", "Bearer " + apiKey);
                    httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
                    httpPost.setHeader("Accept", "text/event-stream");
                    httpPost.setHeader("Connection", "keep-alive");
                    
                    JSONObject requestBody = new JSONObject();
                    requestBody.put("model", model);
                    requestBody.put("messages", messages);
                    requestBody.put("stream", true);
                    
                    String requestJson = requestBody.toJSONString();
                    logger.info("流式请求体: {}", requestJson);
                    
                    StringEntity entity = new StringEntity(requestJson, "UTF-8");
                    entity.setContentType("application/json; charset=UTF-8");
                    httpPost.setEntity(entity);
                    
                    try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                        int statusCode = response.getStatusLine().getStatusCode();
                        logger.info("API响应状态码: {}", statusCode);
                        
                        if (statusCode != 200) {
                            String errorResponse = EntityUtils.toString(response.getEntity(), "UTF-8");
                            logger.error("API请求失败: {}", errorResponse);
                            if (!isEmitterCompleted) {
                                try {
                                    emitter.send(SseEmitter.event()
                                        .name("error")
                                        .data(errorResponse));
                                } catch (Exception e) {
                                    logger.error("发送错误消息失败", e);
                                }
                            }
                            retryCount++;
                            if (retryCount < maxRetries) {
                                logger.info("尝试第 {} 次重试...", retryCount + 1);
                                Thread.sleep(1000 * retryCount);
                                continue;
                            }
                            break;
                        }
                        
                        HttpEntity responseEntity = response.getEntity();
                        try (BufferedReader reader = new BufferedReader(
                                new InputStreamReader(responseEntity.getContent(), "UTF-8"))) {
                            
                            String line;
                            int messageCount = 0;
                            boolean isCompleted = false;
                            long lastActivityTime = System.currentTimeMillis();
                            
                            while ((line = reader.readLine()) != null && !isCompleted && !isEmitterCompleted) {
                                if (line.trim().isEmpty()) {
                                    continue;
                                }
                                
                                // 检查超时
                                if (System.currentTimeMillis() - lastActivityTime > 30000) { // 30秒超时
                                    logger.warn("连接超时，准备重试");
                                    break;
                                }
                                
                                lastActivityTime = System.currentTimeMillis();
                                logger.info("原始SSE数据: {}", line);
                                
                                if (line.startsWith("data: ")) {
                                    String data = line.substring(6).trim();
                                    logger.info("处理数据: {}", data);
                                    
                                    if (data.equals("[DONE]")) {
                                        logger.info("收到结束标记");
                                        if (!isEmitterCompleted) {
                                            try {
                                                emitter.send(SseEmitter.event()
                                                    .name("done")
                                                    .data("[DONE]")
                                                    .id("done"));
                                                success = true;
                                            } catch (Exception e) {
                                                logger.error("发送完成消息失败", e);
                                            }
                                        }
                                        isCompleted = true;
                                        break;
                                    }
                                    
                                    try {
                                        JSONObject dataJson = JSON.parseObject(data);
                                        logger.info("解析后的JSON: {}", dataJson);
                                        
                                        if (dataJson.containsKey("error")) {
                                            String errorMsg = dataJson.getString("error");
                                            logger.error("API返回错误: {}", errorMsg);
                                            if (!isEmitterCompleted) {
                                                try {
                                                    emitter.send(SseEmitter.event()
                                                        .name("error")
                                                        .data(errorMsg)
                                                        .id("error"));
                                                } catch (Exception e) {
                                                    logger.error("发送错误消息失败", e);
                                                }
                                            }
                                            isCompleted = true;
                                            break;
                                        }
                                        
                                        JSONArray choices = dataJson.getJSONArray("choices");
                                        if (choices != null && !choices.isEmpty()) {
                                            JSONObject choice = choices.getJSONObject(0);
                                            JSONObject delta = choice.getJSONObject("delta");
                                            if (delta != null) {
                                                String content = delta.getString("content");
                                                if (content != null && !content.trim().isEmpty()) {
                                                    logger.info("发送内容: {}", content);
                                                    if (!isEmitterCompleted) {
                                                        try {
                                                            emitter.send(SseEmitter.event()
                                                                .name("message")
                                                                .data(content)
                                                                .id("msg_" + messageCount++));
                                                        } catch (Exception e) {
                                                            logger.error("发送内容消息失败", e);
                                                            isCompleted = true;
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    } catch (Exception e) {
                                        logger.error("解析响应失败: {} - {}", data, e.getMessage());
                                        if (!isEmitterCompleted) {
                                            try {
                                                emitter.send(SseEmitter.event()
                                                    .name("error")
                                                    .data("解析响应失败: " + e.getMessage())
                                                    .id("error_parse"));
                                            } catch (Exception ex) {
                                                logger.error("发送解析错误消息失败", ex);
                                            }
                                        }
                                        isCompleted = true;
                                        break;
                                    }
                                }
                            }
                            
                            if (!isCompleted && !success && !isEmitterCompleted) {
                                retryCount++;
                                if (retryCount < maxRetries) {
                                    logger.info("连接中断，尝试第 {} 次重试...", retryCount + 1);
                                    Thread.sleep(1000 * retryCount);
                                    continue;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("流式请求失败", e);
                    retryCount++;
                    if (retryCount < maxRetries) {
                        logger.info("发生错误，尝试第 {} 次重试...", retryCount + 1);
                        try {
                            Thread.sleep(1000 * retryCount);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                        continue;
                    }
                    if (!isEmitterCompleted) {
                        try {
                            emitter.send(SseEmitter.event()
                                .name("error")
                                .data("请求失败: " + e.getMessage())
                                .id("error_request"));
                        } catch (Exception ex) {
                            logger.error("发送错误消息失败", ex);
                        }
                    }
                }
            }
            
            if (!success && !isEmitterCompleted) {
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("达到最大重试次数，请求失败")
                        .id("error_max_retries"));
                } catch (Exception e) {
                    logger.error("发送最终错误消息失败", e);
                }
            }
            
            if (!isEmitterCompleted) {
                try {
                    emitter.complete();
                    isEmitterCompleted = true;
                } catch (Exception e) {
                    logger.error("完成emitter失败", e);
                }
            }
        });
    }

    /**
     * 文档上传和对话
     * @param model 模型名称
     * @param messages 消息列表
     * @param file 上传的文件
     * @return 模型响应
     */
    public String callQwenWithFile(String model, List<Message> messages, MultipartFile file) {
        try {
            String fileContent = new String(file.getBytes());
            messages.add(new Message("user", "文件内容：" + fileContent));
            return callQwen(model, messages);
        } catch (IOException e) {
            throw new RuntimeException("处理文件失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建消息列表
     * @param prompt 用户输入
     * @return 消息列表
     */
    public List<Message> getMessages(String prompt) {
        List<Message> messages = new ArrayList<>();
        messages.add(new Message("user", prompt));
        return messages;
    }
    
    public static class Message {
        private String role;
        private String content;
        
        public Message() {
        }
        
        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }
        
        public String getRole() {
            return role;
        }
        
        public void setRole(String role) {
            this.role = role;
        }
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
    }
} 