package com.xygk.api.zydown.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xygk.api.zydown.dto.BookInfo;
import com.xygk.api.zydown.dto.Catalog;
import com.xygk.api.zydown.dto.WinShareCatalogTree;
import com.xygk.api.zydown.entity.ZyMappingBook;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/17 15:40
 */
@Mapper
public interface ZyMappingBookDao extends BaseMapper<ZyMappingBook> {
    /**
     * 查询书籍信息
     *
     * @return
     */
    List<Catalog> selectListByZyId();

    /**
     * 文轩书籍章节信息
     *
     * @return List<BookInfo>
     */
    List<BookInfo> getBookInfoList(String zyBookId);

    /**
     * 文轩章节树
     * @return  List<WinShareCatalogTree>
     */
    List<WinShareCatalogTree> getWinShareCatalogTreeList();

    /**
     * 查询图书信息
     * @param wxBookId 文轩图书ID
     * @return BookInfo
     */
    BookInfo getBookInfoById(String wxBookId);
}
