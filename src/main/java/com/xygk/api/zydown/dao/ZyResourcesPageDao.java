package com.xygk.api.zydown.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xygk.api.zydown.dto.ResourceInfo;
import com.xygk.api.zydown.dto.WinShareCatalogTree;
import com.xygk.api.zydown.dto.ZyResourceDownInfo;
import com.xygk.api.zydown.entity.ZyResourcesPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/17 15:40
 */
@Mapper
public interface ZyResourcesPageDao extends BaseMapper<ZyResourcesPage> {
    /**
     * 查询待下载的试卷资源
     *
     * @return List<ZyResourcesPage>
     */
    List<ResourceInfo> selectPaperList();

    /**
     * 更新下载状态
     *
     * @param resourcesId 资源ID
     * @param status      状态
     */
    void updateDownloadStatus(@Param("resourcesId") String resourcesId, @Param("status") Integer status);

    /**
     * 查询待下载的中考资源
     *
     * @return List<ZyResourcesPage>
     */
    List<ResourceInfo> selectSpecialList();

    /**
     * 查询待下载同步资源
     *
     * @return List<ZyResourcesPage>
     */
    List<ResourceInfo> selectSynchronousResource(ZyResourceDownInfo downInfo);

    Integer selectByResourcesId(String resourcesId);

    List<WinShareCatalogTree> getListByBooKId(String wxBookId);

    ZyResourceDownInfo selectCatalog();
}
