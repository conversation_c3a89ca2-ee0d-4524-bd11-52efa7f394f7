// package com.xygk.api.zydown.task;

// import com.xygk.api.zydown.service.InitializeUser;
// import com.xygk.api.zydown.service.ResourceDownService;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.scheduling.annotation.Scheduled;
// import org.springframework.stereotype.Component;

// import javax.annotation.Resource;

// /**
//  * <AUTHOR>
//  * @date 2024/1/19 13:57
//  */
// @Component
// public class DownloadResourceTask {
//     private static final Logger logger = LoggerFactory.getLogger(DownloadResourceTask.class);
//     @Resource
//     private ResourceDownService resourceDownService;
//     @Resource
//     private InitializeUser initializeUser;

//     /**
//      * 下载同步资源
//      */
// //    @Scheduled(cron = "0 0/1 * * * ?")
//     @Scheduled(initialDelay = 5000, fixedDelay = 2000)
//     public void downloadSynchronizeResources() {
//         resourceDownService.downloadSynchronizeResources();
//     }

//     /**
//      * 下载试卷资源
//      */
//     @Scheduled(initialDelay = 5000, fixedDelay = 2000)
//     public void downloadPaperResource() {
//         resourceDownService.examPaperDownload();
//     }


//     /**
//      * 下载中考资源
//      */
//     @Scheduled(initialDelay = 5000, fixedDelay = 2000)
//     public void specialSubject() {
//         resourceDownService.specialSubject();
//     }

//     /**
//      * 初始化用户信息
//      */
//     @Scheduled(cron = "0 0 1 * * ?")
//     public void initializeUser() {
//         initializeUser.initializeUser();
//     }


// //    @Scheduled(fixedRate = Long.MAX_VALUE)
// //    public void resourcePage() {
// //        resourceDownService.resourcePage();
// //    }

// }
