package com.xygk.api.zydown.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/1/17 13:10
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("tb_zy_resource_down")
public class ZyResourceDown {
    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * bookId
     */
    private Long bookId;

    /**
     * 章节code
     */
    private String catalogCode;

    /**
     * 需下载数量
     */
    private Integer downloadRequired;

    /**
     * 已下载数量
     */
    private Integer downloadedQuantity;

    /**
     * 资源数量
     */
    private Integer isDownload;

}
