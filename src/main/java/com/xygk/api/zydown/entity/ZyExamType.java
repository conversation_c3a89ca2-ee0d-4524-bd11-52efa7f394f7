package com.xygk.api.zydown.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/1/18 9:05
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("tb_zy_examtype")
public class ZyExamType {
    /**
     * ID
     */
    private Long id;

    /**
     * ID
     */
    @TableField("pId")
    private Long pId;

    private String name;

    private String leaf;

    @TableField("subjectId")
    private Long subjectId;

    private String period;
}
