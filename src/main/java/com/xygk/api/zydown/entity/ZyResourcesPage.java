package com.xygk.api.zydown.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 状元网资源
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_zy_resources_page")
public class ZyResourcesPage {

    /**
     * 资源ID
     */
    private String resourcesId;

    /**
     * 资源ID
     */
    private Integer resourceType;

    /**
     * 状元网书籍
     */
    private Long zyBookId;

    /**
     * 资源标题
     */
    private String title;

    /**
     * 等级
     */
    private String rank;

    /**
     * 学段
     */
    private String period;

    /**
     * 学科
     */
    private Integer subjectId;

    /**
     * 学科名称
     */
    private String subjectName;

    /**
     * 教材版本、册、章节、小节Id（逗号分隔）
     */
    private String categoryId;

    /**
     * 教材版本、册、章节、小节名称（逗号分隔）
     */
    private String categoryName;

    /**
     * 年级
     */
    private String grade;

    /**
     * 年份
     */
    private String year;

    /**
     * 标签，逗号分隔
     */
    private String tags;

    /**
     * 省
     */
    private String province;

    /**
     * 市（多个市Id，逗号分隔）
     */
    private String district;

    /**
     * 资源文件类型
     */
    private String fileType;

    /**
     * 知识点，逗号分隔
     */
    private String pointsId;

    /**
     * 知识点名称，逗号分隔
     */
    private String pointsName;

    /**
     * 资源属性
     */
    private String attribute;

    /**
     * 文件大小
     */
    private Integer fileSize;

    /**
     * 文件大小（转换后的）
     */
    private String fileSizeStr;

    /**
     * 是否是考试
     */
    private Integer exam;

    /**
     * 考试类型
     */
    private Integer examType;

    /**
     * 资源状态
     */
    private Integer status;

    /**
     * 文件页数
     */
    private Integer filePageCount;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 下载次数
     */
    private Integer down;

    /**
     * 查看次数
     */
    private Integer look;

    /**
     * 点赞次数
     */
    private Integer support;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 上传用户
     */
    private String createBy;

    /**
     * 学校ID
     */
    private String schoolId;

    /**
     * 预览地址
     */
    private String previewUrl;

    /**
     * 文件的扩展名
     */
    private String fileExt;

    private String type;

    private String typeName;

    private Integer downloadStatus;

    private String wxCatalogCode;
    private String zyResourceType;


}