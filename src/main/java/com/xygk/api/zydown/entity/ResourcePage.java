package com.xygk.api.zydown.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/17 15:09
 */
@NoArgsConstructor
@Data
public class ResourcePage {

    private String resourcesId;

    private String title;

    private String rank;

    private String period;

    private Integer subjectId;

    private String subjectName;

    private String categoryId;

    private String categoryName;

    private String grade;

    private String year;

    private String tags;

    private String province;

    private String district;

    private String fileType;

    private String pointsId;

    private String pointsName;

    private String attribute;

    private Integer fileSize;

    private String fileSizeStr;

    private boolean exam;

    private Integer examType;

    private Integer status;

    private Integer filePageCount;

    private Date updateTime;

    private Integer down;

    private Integer look;

    private Integer support;

    private double price;

    private String createBy;

    private String schoolId;

    private String previewUrl;

    private String fileExt;

    private String type;

    private String typeName;

    private String catalogId;


}
