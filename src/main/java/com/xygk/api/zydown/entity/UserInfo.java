package com.xygk.api.zydown.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/1/17 13:10
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("tb_zy_mapping_user")
public class UserInfo {
    /**
     * ID
     */
    private Long id;

    /**
     * memberId
     */
    private String memberId;

    /**
     * 下载次数
     */
    private Integer downloadsTotal;

    /**
     * 已下载次数
     */
    private Integer downNumber;

}
