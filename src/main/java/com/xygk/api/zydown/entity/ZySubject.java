package com.xygk.api.zydown.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 学科信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_zy_subject")
public class ZySubject {

    /**
     * 书籍ID
     */
    @TableField("subjectId")
    private Long subjectId;

    /**
     * 状元网书籍
     */
    @TableField("subjectName")
    private String subjectName;

    /**
     * 学段Id
     */
    @TableField("period")
    private String period;

}