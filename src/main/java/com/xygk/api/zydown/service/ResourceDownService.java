package com.xygk.api.zydown.service;

import com.xygk.api.zydown.dto.BookInfo;
import com.xygk.api.zydown.dto.WinShareCatalogTree;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/16 13:54
 */
public interface ResourceDownService {
    /**
     * 试卷下载
     */
    void examPaperDownload();

    /**
     * 获取资源详情
     *
     * @param resourcesId 资源ID
     * @return 详情
     */
    String getResourceDetail(String resourcesId);

    /**
     * 获取资源下载地址
     *
     * @param resourcesId 资源ID
     * @param memberId    用户Id
     * @return 资源下载地址
     */
    String getResourceDownloadUrl(String resourcesId, String memberId);

    /**
     * 下载资源到本地目录
     *
     * @param resourcesPath 资源保存目录
     * @param resourceUrl   资源下载地址
     * @param resourceId    资源ID
     */
    void saveResource(String resourcesPath, String resourceUrl, String resourceId);

    /**
     * 下载同步资源
     */
    void downloadSynchronizeResources();

    /**
     * 中考专题
     */
    void specialSubject();

    /**
     * 资源分页
     */
    void resourcePage();

    /**
     * 试卷资源分页
     */
    void paperResourcePage();

    /**
     * 中考试卷
     */
    void testResourcePage();

    /**
     * 文轩书籍章节信息
     *
     * @return List<BookInfo>
     */
    List<BookInfo> getBookInfoList(String catalogCode);

    /**
     * 文轩章节树
     * @return List<WinShareCatalogTree>
     */
    List<WinShareCatalogTree> getWinShareCatalogTreeList();
}
