package com.xygk.api.zydown.service;

import com.xygk.api.zydown.dao.ZyMappingUserDao;
import com.xygk.api.zydown.entity.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 初始化用户信息
 *
 * <AUTHOR>
 * @date 2024/1/19 13:27
 */
//@Component
public class InitializeUser {
    @Resource
    private ZyMappingUserDao zyMappingUserDao;

    private static final Logger logger = LoggerFactory.getLogger(InitializeUser.class);
    /**
     * 用户信息
     */
    public static List<UserInfo> userInfoList = new ArrayList<>();

//    @PostConstruct
    public void initializeUser() {
        try {
            userInfoList = zyMappingUserDao.listAll();
        } catch (Exception e) {
            logger.error("初始化用户信息失败" + e);
            e.printStackTrace();
        }

    }
}
