package com.xygk.api.zydown.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xygk.api.zydown.dao.*;
import com.xygk.api.zydown.dto.*;
import com.xygk.api.zydown.entity.*;
import com.xygk.api.zydown.service.InitializeUser;
import com.xygk.api.zydown.service.ResourceDownService;
import com.xygk.redis.RedisUtils;
import com.xygk.utils.ZyHttpRequestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/16 13:54
 */
@Service
public class ResourceDownServiceImpl implements ResourceDownService {
    private static final Logger logger = LoggerFactory.getLogger(ResourceDownServiceImpl.class);
    /**
     * 系统路径斜杠
     */
    private static final String SEPARATOR = File.separator;
    @Value("${resource.path}")
    private String resourcePath;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ZyResourceDownDao zyResourceDownDao;

    /**
     * 视频
     */
    private static final String VIDEO_TYPE = "6";
    @Resource
    private ZyMappingBookDao zyMappingBookDao;
    @Resource
    private ZyResourcesPageDao zyResourcesPageDao;
    @Resource
    private ZySubjectDao zySubjectDao;
    @Resource
    private ZyExamTypeDao zyExamTypeDao;

    @Override
    public void examPaperDownload() {
        //查询待下载的试卷资源
        List<ResourceInfo> list = zyResourcesPageDao.selectPaperList();
        logger.info("开始下载试卷资源,总计【" + list.size() + "】条");
        int i = 0;
        for (ResourceInfo resourceInfo : list) {
            i++;
            String resourcesId = resourceInfo.getResourcesId();
            int status = 1;
            if (CollectionUtils.isEmpty(InitializeUser.userInfoList)) {
                logger.error("用户信息为空,结束下载");
                return;
            }
            //随机获取用户信息
            int index = (int) (Math.random() * InitializeUser.userInfoList.size());
            UserInfo userInfo = InitializeUser.userInfoList.get(index);
            try {
                //获取资源详情
                String result = getResourceDetail(resourcesId);
                ResourceDetail resourceDetail = JSON.parseObject(result, ResourceDetail.class);
                if (!VIDEO_TYPE.equals(resourceDetail.getFileType())) {
                    logger.info("开始下载试卷资源,第【" + i + "】条数据,资源ID【" + resourcesId + "】");
                    String directory = resourceInfo.getPeriodName() + resourceInfo.getSubjectName() + SEPARATOR
                            + resourceInfo.getPaperTypeName() + SEPARATOR
                            + resourceInfo.getRankName() + SEPARATOR
                            + resourceInfo.getGradeName() + SEPARATOR
                            + resourceInfo.getYear();
                    String path = resourcePath + SEPARATOR + "试卷类资源" + SEPARATOR + directory + SEPARATOR;
                    //获取资源下载地址
                    String resourceDownloadUrl = getResourceDownloadUrl(resourcesId, userInfo.getMemberId());
                    //下载资源保存
                    saveResource(path, resourceDownloadUrl, resourcesId);
                }
            } catch (Exception e) {
                logger.error("资源【" + resourcesId + "】,下载失败" + e);
                status = 2;
            } finally {
                userInfo.setDownloadsTotal(userInfo.getDownloadsTotal() - 1);
                updateUserInfo(index, userInfo);
                zyResourcesPageDao.updateDownloadStatus(resourcesId, status);
            }
        }
        logger.warn("————试卷资源下载完成,总资源数量【" + list.size() + "】,完成时间" + DateUtil.now() + "————");

    }

    @Override
    public String getResourceDetail(String resourcesId) {
        String result = "";
        try {
            String url = "/web/api/resources/detail/" + resourcesId;
            result = ZyHttpRequestUtils.get(url);
        } catch (Exception e) {
            logger.error("获取资源【" + resourcesId + "】详情出错" + e);
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public String getResourceDownloadUrl(String resourcesId, String memberId) {
        String resourceDownloadUrl = "";
        try {
            String url = "/web/api/bk/thirdparty/down?resourcesId=" + resourcesId + "&memberId=" + memberId;
            resourceDownloadUrl = ZyHttpRequestUtils.post(url);
        } catch (Exception e) {
            logger.error("获取资源" + "【" + resourcesId + "】" + "下载地址出错:" + e);
            e.printStackTrace();
        }
        return resourceDownloadUrl;
    }

    @Override
    public void saveResource(String resourcesPath, String resourceUrl, String resourceId) {
        if (StringUtils.isBlank(resourceUrl)) {
            logger.warn("资源【" + resourceId + "】的下载地址为空");
            return;
        }
        if (StringUtils.isBlank(resourcesPath)) {
            logger.warn("资源【" + resourceId + "】的磁盘储存目录为空");
            return;
        }
        try {
            Thread.sleep(2000);
            Map<String, String> map = HttpUtil.decodeParamMap(resourceUrl, StandardCharsets.UTF_8);
            String fileName = map.get("response-content-disposition");
            int index = fileName.lastIndexOf('=');
            fileName = fileName.substring(index + 1);
            HttpUtil.downloadFileFromUrl(resourceUrl, resourcesPath + fileName);
        } catch (Exception e) {
            logger.error("资源【" + resourceId + "】文件下载失败:" + e);
            e.printStackTrace();
        }
    }

    @Override
    public void downloadSynchronizeResources() {
        //查询需下载章节信息
        ZyResourceDownInfo downInfo = zyResourcesPageDao.selectCatalog();
        if (ObjectUtil.isEmpty(downInfo)) {
            logger.warn("章节信息为空,结束本次下载");
            return;
        }
        //查询待下载的同步资源
        downInfo.setLimitTotal(downInfo.getDownloadRequired() - downInfo.getDownloadedQuantity());
        List<ResourceInfo> list = zyResourcesPageDao.selectSynchronousResource(downInfo);
        Long downInfoId = downInfo.getId();
        if (list.size() == 0) {
            zyResourceDownDao.updateResourceDown(downInfoId, 1);
            logger.warn("无同步资源信息,结束本次下载");
            return;
        }
        logger.info("开始下载同步资源,数量【" + list.size() + "】");
        int i = 0;
        for (ResourceInfo resourceInfo : list) {
            i++;
            String resourcesId = resourceInfo.getResourcesId();
            String wxBookId = resourceInfo.getWxBookId();
            int status = 1;
            List<UserInfo> userInfoList = InitializeUser.userInfoList;
            if (CollectionUtils.isEmpty(userInfoList)) {
                logger.error("用户信息为空,结束下载");
                return;
            }
            //随机获取用户信息
            int index = (int) (Math.random() * userInfoList.size());
            UserInfo userInfo = userInfoList.get(index);
            try {
                //获取资源详情
                String result = getResourceDetail(resourceInfo.getResourcesId());
                ResourceDetail resourceDetail = JSON.parseObject(result, ResourceDetail.class);
                if (!VIDEO_TYPE.equals(resourceDetail.getFileType())) {
                    logger.info("开始下载同步资源,第【" + i + "】条数据,资源ID【" + resourcesId + "】");
                    //查询图书信息
                    BookInfo bookInfo = zyMappingBookDao.getBookInfoById(wxBookId);
                    //查询文轩教材目录
                    List<WinShareCatalogTree> catalogTreeList = zyResourcesPageDao.getListByBooKId(wxBookId);
                    Map<String, String> treeMap = new HashMap<>();
                    for (WinShareCatalogTree winShareCatalogTree : catalogTreeList) {
                        String code = winShareCatalogTree.getCode();
                        String name = winShareCatalogTree.getName();
                        treeMap.put(code, name);
                    }
                    Map<String, String> catalogMap = new HashMap<>();
                    for (WinShareCatalogTree winShareCatalogTree : catalogTreeList) {
                        String code = winShareCatalogTree.getCode();
                        String name = winShareCatalogTree.getName();
                        String parentIds = winShareCatalogTree.getParentIds();
                        Integer level = winShareCatalogTree.getLevel();
                        if (StringUtils.isNotBlank(parentIds)) {
                            String[] split = parentIds.split("_");
                            StringBuilder chapterName = new StringBuilder();
                            if (level == 1) {
                                chapterName.append(treeMap.get(winShareCatalogTree.getCode())).append(SEPARATOR);
                            } else {
                                for (String catalogCode : split) {
                                    String catalogName = treeMap.get(catalogCode);
                                    chapterName.append(catalogName).append(SEPARATOR);
                                }
                            }
                            name = chapterName.toString();
                        }
                        catalogMap.put(code, name);
                    }
                    String directory = bookInfo.getPhaseName() + bookInfo.getSubjectName() + SEPARATOR
                            + bookInfo.getEditionName() + SEPARATOR
                            + bookInfo.getGradeName() + bookInfo.getFasciculeName() + SEPARATOR
                            + catalogMap.get(resourceInfo.getCatalogCode()) + SEPARATOR
                            + resourceInfo.getResourcesTypeName() + SEPARATOR
                            + resourceInfo.getRankName() + SEPARATOR
                            + resourceInfo.getYear();
                    String path = resourcePath + SEPARATOR + "同步类资源" + SEPARATOR + directory + SEPARATOR;
                    //获取资源下载地址
                    String resourceDownloadUrl = getResourceDownloadUrl(resourcesId, userInfo.getMemberId());
                    //下载资源保存
                    saveResource(path, resourceDownloadUrl, resourcesId);
                    //更新下载次数
                    zyResourceDownDao.updateDownload(downInfoId);
                }
            } catch (Exception e) {
                logger.error("资源【" + resourcesId + "】,下载失败" + e);
                status = 2;
            } finally {
                userInfo.setDownloadsTotal(userInfo.getDownloadsTotal() - 1);
                updateUserInfo(index, userInfo);
                zyResourcesPageDao.updateDownloadStatus(resourcesId, status);
            }
        }
    }

    @Override
    public void specialSubject() {
        //查询待下载的中考资源
        List<ResourceInfo> list = zyResourcesPageDao.selectSpecialList();
        logger.info("开始下载中考资源,总计【" + list.size() + "】条");
        int i = 0;
        for (ResourceInfo resourceInfo : list) {
            i++;
            String resourcesId = resourceInfo.getResourcesId();
            int status = 1;
            List<UserInfo> userInfoList = InitializeUser.userInfoList;
            if (CollectionUtils.isEmpty(userInfoList)) {
                logger.error("用户信息为空,结束下载");
                return;
            }
            int index = (int) (Math.random() * userInfoList.size());
            UserInfo userInfo = userInfoList.get(index);
            //随机获取用户信息
            try {
                //获取资源详情
                String result = getResourceDetail(resourcesId);
                ResourceDetail resourceDetail = JSON.parseObject(result, ResourceDetail.class);
                if (!VIDEO_TYPE.equals(resourceDetail.getFileType())) {
                    logger.info("开始下载中考资源,第【" + i + "】条数据,资源ID【" + resourcesId + "】");
                    String directory = resourceInfo.getPeriodName() + resourceInfo.getSubjectName() + SEPARATOR +
                            resourceInfo.getExamTypeName() + SEPARATOR
                            + resourceInfo.getResourcesTypeName() + SEPARATOR
                            + resourceInfo.getRankName() + SEPARATOR
                            + resourceInfo.getGradeName() + SEPARATOR
                            + resourceInfo.getYear();
                    String path = resourcePath + SEPARATOR + "中考专题类资源" + SEPARATOR + directory + SEPARATOR;
                    //获取资源下载地址
                    String resourceDownloadUrl = getResourceDownloadUrl(resourcesId, userInfo.getMemberId());
                    //下载资源保存
                    saveResource(path, resourceDownloadUrl, resourcesId);
                }
            } catch (Exception e) {
                logger.error("资源【" + resourcesId + "】,下载失败" + e);
                status = 2;
            } finally {
                userInfo.setDownloadsTotal(userInfo.getDownloadsTotal() - 1);
                updateUserInfo(index, userInfo);
                zyResourcesPageDao.updateDownloadStatus(resourcesId, status);
            }
        }
        logger.warn("————中考资源下载完成,总资源数量【" + list.size() + "】,完成时间" + DateUtil.now() + "————");

    }

    @Override
    public void resourcePage() {
        int total = 0;
        //查询同步资源数据
        List<Catalog> list = zyMappingBookDao.selectListByZyId();
        for (Catalog catalog : list) {
            int catalogTotal = 0;
            total = total + catalogTotal;
            List<String> typeList = new ArrayList<>();
            typeList.add("1");
            typeList.add("2");
            typeList.add("4");
            typeList.add("5");
            typeList.add("9");
            typeList.add("28");
            typeList.add("38");
            String catalogId = catalog.getCatalogId();
            String bookId = catalog.getBookId();
            Long zyBookId = catalog.getZyBookId();
            String catalogCode = catalog.getCatalogCode();
//            String catalogId = "17066";
//            String bookId = "2227";
//            Long zyBookId = 111L;
//            String catalogCode = "1111";
            for (String type : typeList) {
                if (catalogTotal > 100) {
                    logger.warn("本次类型结束,类型为【" + type + "】,数量为【" + catalogTotal + "】");
                    break;
                }
                logger.info("开始获取书籍ID【" + bookId + "】,章节id为【" + catalogId + "】资源类型为【" + type + "】的同步资源");
                JSONObject jsonObject;
                int totalPage = 1;
                try {
                    String url = "/web/api/resources/pagelist?pageNum=1&pageSize=50&orderByColumn=updateTime&isAsc=asc&categoryId=" + catalogId + "&updateTime=2021-01-01&type=" + type;
//                    String url = "/web/api/resources/pagelist?pageNum=1&pageSize=50&orderByColumn=updateTime&isAsc=desc&categoryId=" + catalogId + "&type=" + type;
                    String result = ZyHttpRequestUtils.get(url);
                    jsonObject = JSON.parseObject(result);
                    Integer resourceTotal = jsonObject.getInteger("total");
                    //总页码
                    totalPage = (resourceTotal + 50 - 1) / 50;
                    total = total + resourceTotal;
                    logger.info("书籍ID【" + bookId + "】," +
                            "章节ID【" + catalogId + "】," +
                            "资源类型为【" + type + "】的同步资源," +
                            "数量为【" + resourceTotal + "】");
                } catch (Exception e) {
                    logger.error("书籍ID【" + bookId + "】," +
                            "章节ID【" + catalogId + "】," +
                            "资源类型为【" + type + "】的同步资源,获取分页数据失败");
                }
                for (int i = totalPage; i >= 1; i--) {
                    if (catalogTotal > 100) {
                        logger.warn("本次分页结束,类型为【" + type + "】,数量为【" + catalogTotal + "】");
                        break;
                    }
                    logger.info("书籍ID为【" + bookId + "】," +
                            "章节ID为【" + catalogId + "】," +
                            "资源类型为【" + type + "】的同步资源," +
                            "总页码【" + totalPage + "】" +
                            "开始获取第【" + i + "】页的数据");
                    try {
                        String pageUrl = "/web/api/resources/pagelist?orderByColumn=updateTime&isAsc=asc&pageNum=" + i + "&pageSize=50&categoryId=" + catalogId + "&updateTime=2021-01-01&type=" + type;
//                        String pageUrl = "/web/api/resources/pagelist?orderByColumn=updateTime&isAsc=desc&pageNum=" + i + "&pageSize=50&categoryId=" + catalogId + "&type=" + type;
                        String body = ZyHttpRequestUtils.get(pageUrl);
                        JSONObject parseObject = JSON.parseObject(body);
                        List<ZyResourcesPage> pageList = JSON.parseArray(parseObject.getString("rows"), ZyResourcesPage.class);
                        for (ZyResourcesPage zyResourcesPage : pageList) {
                            if (catalogTotal > 100) {
                                logger.warn("本次数据写入结束,章节ID【" + catalogId + "】,数量【" + catalogTotal + "】");
                                break;
                            }
                            catalogTotal++;
                            zyResourcesPage.setZyBookId(zyBookId);
                            zyResourcesPage.setResourceType(1);
                            zyResourcesPage.setZyResourceType(type);
                            zyResourcesPage.setWxCatalogCode(catalogCode);
                            zyResourcesPage.setDownloadStatus(0);
                            zyResourcesPageDao.insert(zyResourcesPage);
                        }
                    } catch (Exception e) {
                        logger.error("书籍ID为【" + bookId + "】,资源类型为【" + type + "】,书籍章节【" + catalogId + "】的资源,获取第【" + i + "】页的数据失败");
                    }
                }
            }
        }
        logger.warn("————同步资源下载完成,总数量【" + total + "】完成时间" + DateUtil.now() + "————");
    }

    @Override
    public void paperResourcePage() {
        int total = 0;
        //试卷类型
        List<String> paperType = new ArrayList<>();
        paperType.add("12");
        paperType.add("13");
        paperType.add("14");
        paperType.add("18");
        paperType.add("20");
        paperType.add("32");
        for (String s : paperType) {
            logger.info("开始获取试卷类型为" + s + "的试卷资源");
            String url = "/web/api/resources/pagelist?orderByColumn=updateTime&isAsc=asc&pageNum=1&pageSize=50&updateTime=2021-01-01&type=" + s;
            String result = ZyHttpRequestUtils.get(url);
            JSONObject jsonObject = JSON.parseObject(result);
            //总页码
            int totalPage;
            totalPage = (jsonObject.getInteger("total") + 50 - 1) / 50;
            total = total + jsonObject.getInteger("total");
            for (int i = 1; i <= totalPage; i++) {
                logger.info("开始获取类型为【" + s + "】的试卷资源,总条数【" + jsonObject.getInteger("total") + "】,总页码【" + totalPage + "】,开始获取第" + i + "页的数据");
                try {
                    String pageUrl = "/web/api/resources/pagelist?orderByColumn=updateTime&isAsc=asc&pageNum=" + i + "&pageSize=50&updateTime=2021-01-01&type=" + s;
                    String body = ZyHttpRequestUtils.get(pageUrl);
                    JSONObject parseObject = JSON.parseObject(body);
                    List<ZyResourcesPage> pageList = JSON.parseArray(parseObject.getString("rows"), ZyResourcesPage.class);
                    for (ZyResourcesPage zyResourcesPage : pageList) {
                        zyResourcesPage.setResourceType(2);
                        zyResourcesPage.setZyResourceType(s);
                        zyResourcesPage.setDownloadStatus(0);
//                        String resourcesId = zyResourcesPage.getResourcesId();
//                        Integer count = zyResourcesPageDao.selectByResourcesId(resourcesId);
                        zyResourcesPageDao.insert(zyResourcesPage);
                    }
                } catch (Exception e) {
                    logger.info("试卷类型为【" + s + "】" + ",第" + i + "页的数据获取失败");
                }
            }
            logger.warn("————试卷类型为【" + s + "】完成,完成时间" + DateUtil.now() + "————");
        }
    }

    @Override
    public void testResourcePage() {
        //资源类型
        List<String> typeList = new ArrayList<>();
        typeList.add("1");
        typeList.add("2");
        typeList.add("5");
        typeList.add("6");
        typeList.add("9");
        typeList.add("28");
        typeList.add("38");
        typeList.add("39");
        for (String s : typeList) {
            int total = 0;
            QueryWrapper<ZySubject> wrapper = new QueryWrapper<>();
            wrapper.eq("period", 2);
            wrapper.le("subjectId", '9');
            List<ZySubject> list = zySubjectDao.selectList(wrapper);
            for (ZySubject zySubject : list) {
                QueryWrapper<ZyExamType> examType = new QueryWrapper<>();
                examType.eq("period", zySubject.getPeriod());
                examType.eq("subjectId", zySubject.getSubjectId());
                examType.notIn("id", 6, 17, 33, 39, 45);
                List<ZyExamType> examTypeList = zyExamTypeDao.selectList(examType);
                for (ZyExamType zyExamType : examTypeList) {
                    logger.info("开始获取类型为【" + s + "】," +
                            "学科ID为【" + zySubject.getSubjectId() + "】" +
                            "zyExamType类型为【" + zyExamType.getId() + "】" +
                            "的中考专题数据");
                    Integer totalCount;
                    int totalPage = 0;
                    try {
                        JSONObject jsonObject;
                        String url = "/web/api/resources/pagelist?orderByColumn=updateTime&isAsc=asc&pageNum=1&pageSize=50&updateTime=2021-01-01&period=2&subjectId=" + zySubject.getSubjectId() + "&examType=" + zyExamType.getId() + "&type=" + s;
                        String result = ZyHttpRequestUtils.get(url);
                        jsonObject = JSON.parseObject(result);
                        totalCount = jsonObject.getInteger("total");
                        //总页码
                        totalPage = (totalCount + 50 - 1) / 50;
                        total = total + totalCount;
                    } catch (Exception e) {
                        logger.error("开始获取类型为【" + s + "】," +
                                "学科ID为【" + zySubject.getSubjectId() + "】" +
                                "zyExamType类型为【" + zyExamType + "】" +
                                "的中考专题分页数据失败");
                    }
                    for (int i = 1; i <= totalPage; i++) {
                        logger.info("开始获取类型为【" + s + "】," +
                                "学科ID为【" + zySubject.getSubjectId() + "】" +
                                "zyExamType类型为【" + zyExamType.getId() + "】" + ",总页码【" + totalPage + "】,开始获取第" + i + "页的数据");
                        try {
                            String pageUrl = "/web/api/resources/pagelist?orderByColumn=updateTime&isAsc=asc&pageNum=" + i + "&pageSize=50&updateTime=2021-01-01&period=2&subjectId=" + zySubject.getSubjectId() + "&examType=" + zyExamType.getId() + "&type=" + s;
                            String body = ZyHttpRequestUtils.get(pageUrl);
                            JSONObject parseObject = JSON.parseObject(body);
                            List<ZyResourcesPage> pageList = JSON.parseArray(parseObject.getString("rows"), ZyResourcesPage.class);
                            for (ZyResourcesPage zyResourcesPage : pageList) {
                                zyResourcesPage.setResourceType(3);
                                zyResourcesPage.setDownloadStatus(0);
                                zyResourcesPage.setZyResourceType(s);
                                //String resourcesId = zyResourcesPage.getResourcesId();
                                //Integer count = zyResourcesPageDao.selectByResourcesId(resourcesId);
                                zyResourcesPageDao.insert(zyResourcesPage);
                            }
                        } catch (Exception e) {
                            logger.error("开始获取类型为【" + s + "】," +
                                    "学科ID为【" + zySubject.getSubjectId() + "】" +
                                    "zyExamType类型为【" + zyExamType.getId() + "】" + ",开始获取第" + i + "页的数据失败");
                        }
                    }

                }
            }
        }
        logger.warn("————中考专题下载完成,完成时间" + DateUtil.now() + "————");
    }

    @Override
    public List<BookInfo> getBookInfoList(String zyBookId) {
        return zyMappingBookDao.getBookInfoList(zyBookId);
    }

    @Override
    public List<WinShareCatalogTree> getWinShareCatalogTreeList() {
        return zyMappingBookDao.getWinShareCatalogTreeList();
    }


    private void updateUserInfo(int index, UserInfo userInfo) {
        if (userInfo.getDownloadsTotal() == 0) {
            InitializeUser.userInfoList.remove(index);
        } else {
            InitializeUser.userInfoList.set(index, userInfo);
        }
    }

}
