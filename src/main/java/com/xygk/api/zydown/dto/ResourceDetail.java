package com.xygk.api.zydown.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/16 14:03
 */
@Data
@ApiModel(value = "资源详情")
public class ResourceDetail {

    @ApiModelProperty(value = "资源ID")
    private String resourcesId;

    @ApiModelProperty(value = "资源标题")
    private String title;

    @ApiModelProperty(value = "资源等级")
    private String rank;

    @ApiModelProperty(value = "学段")
    private String period;

    @ApiModelProperty(value = "学段名称")
    private String periodName;

    @ApiModelProperty(value = "学科")
    private String subjectId;

    @ApiModelProperty(value = "学科名称")
    private String subjectName;

    @ApiModelProperty(value = "教材版本、册、章节、小节Id")
    private List<String> categoryIds;

    @ApiModelProperty(value = "教材版本、册、章节、小节名称")
    private List<String> categoryNames;

    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "标签")
    private List<String> tag;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private List<String> district;

    @ApiModelProperty(value = "资源文件类型")
    private String fileType;

    @ApiModelProperty(value = "知识点，逗号分隔")
    private String pointsId;

    @ApiModelProperty(value = "知识点名称，逗号分隔")
    private String pointsName;

    @ApiModelProperty(value = "资源属性")
    private String attribute;

    @ApiModelProperty(value = "文件大小")
    private Integer fileSize;

    @ApiModelProperty(value = "文件大小（转换后的）")
    private String fileSizeStr;

    @ApiModelProperty(value = "是否是考试")
    private boolean exam;

    @ApiModelProperty(value = "考试类型")
    private String examType;

    @ApiModelProperty(value = "资源类型")
    private List<String> types;

    @ApiModelProperty(value = "资源类型名称")
    private List<String> typeNames;

    @ApiModelProperty(value = "资源状态")
    private String status;

    @ApiModelProperty(value = "文件页数")
    private Integer filePageCount;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "下载次数")
    private Integer down;

    @ApiModelProperty(value = "查看次数")
    private Integer look;

    @ApiModelProperty(value = "点赞次数")
    private Integer support;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "资源预览地址")
    private String previewUrl;

    @ApiModelProperty(value = "上传用户")
    private String createBy;

    private String attachments;

    private String url;
}
