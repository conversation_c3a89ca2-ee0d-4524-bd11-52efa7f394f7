package com.xygk.api.zydown.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/16 13:18
 */
@NoArgsConstructor
@Data
@ApiModel(value = "章节信息")
public class Catalog {

    @ApiModelProperty(value = "文轩图书ID")
    private String bookId;

    @ApiModelProperty(value = "文轩图书名称")
    private String bookName;

    @ApiModelProperty(value = "状元网图书ID")
    private Long zyBookId;

    @ApiModelProperty(value = "状元网章节id")
    private String catalogId;

    @ApiModelProperty(value = "状元网章节code")
    private String catalogCode;
}
