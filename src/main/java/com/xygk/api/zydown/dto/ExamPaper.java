package com.xygk.api.zydown.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/16 13:18
 */
@NoArgsConstructor
@Data
@ApiModel(value = "试卷对象")
public class ExamPaper {

    @JsonProperty("previewUrl")
    private String previewUrl;

    @JsonProperty("year")
    private String year;

    @JsonProperty("typeName")
    private String typeName;

    @JsonProperty("title")
    private String title;

    @JsonProperty("type")
    private String type;

    @JsonProperty("fileExt")
    private String fileExt;

    @JsonProperty("categoryName")
    private String categoryName;

    @JsonProperty("down")
    private Integer down;

    @JsonProperty("subjectId")
    private Integer subjectId;

    @JsonProperty("province")
    private String province;

    @JsonProperty("price")
    private Integer price;

    @JsonProperty("rank")
    private String rank;

    @JsonProperty("attribute")
    private String attribute;

    @JsonProperty("subjectName")
    private String subjectName;

    @JsonProperty("period")
    private String period;

    @JsonProperty("updateTime")
    private String updateTime;

    @JsonProperty("look")
    private Integer look;

    @JsonProperty("tags")
    private String tags;

    @JsonProperty("exam")
    private Boolean exam;

    @JsonProperty("nexam")
    private Boolean nexam;

    @JsonProperty("createBy")
    private String createBy;

    @JsonProperty("resourcesId")
    private String resourcesId;

    @JsonProperty("fileSize")
    private Integer fileSize;

    @JsonProperty("grade")
    private String grade;

    @JsonProperty("support")
    private Integer support;

    @JsonProperty("categoryId")
    private String categoryId;

    @JsonProperty("fileType")
    private String fileType;

    @JsonProperty("filePageCount")
    private Integer filePageCount;

    @JsonProperty("status")
    private Integer status;
}
