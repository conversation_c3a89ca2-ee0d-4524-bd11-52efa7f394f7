package com.xygk.api.zydown.controller;

import com.xygk.api.zydown.service.ResourceDownService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 资源下载流程如下:
 * 1、查询待下载的资源
 * 2、查询可下载的用户信息,每个用户每天只能下载20个资源
 * 3、下载资源
 * 4、资源保存到本地磁盘目录
 * 5、更新用户下载次数
 * </p>
 *
 * <AUTHOR>
 * @date 2023/3/3 14:34
 */
@RestController
@RequestMapping("resourceDown/")
@Api(tags = "资源下载")
public class ResourceDownController {
    @Resource
    private ResourceDownService resourceDownService;

    @GetMapping("synchronizeResources")
    @ApiOperation("同步资源下载")
    public void synchronizeResources() {
        resourceDownService.downloadSynchronizeResources();
    }

    @GetMapping("examPaperDownload")
    @ApiOperation("试卷资源下载")
    public void examPaperDownload() {
        resourceDownService.examPaperDownload();
    }

    @GetMapping("specialSubject")
    @ApiOperation("中考专题资源下载")
    public void specialSubject() {
        resourceDownService.specialSubject();
    }
}
