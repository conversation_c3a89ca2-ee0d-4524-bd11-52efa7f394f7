package com.xygk.api.zydown.controller;

import com.xygk.api.zydown.service.ResourceDownService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/3 14:34
 */
@RestController
@RequestMapping("resourcePage/")
@Api(tags = "资源分页下载")
public class ResourcePageController {
    @Resource
    private ResourceDownService resourceDownService;

    @GetMapping("resourcePage")
    @ApiOperation("同步资源分页")
    public void resourcePage() {
        resourceDownService.resourcePage();
    }

    @GetMapping("paperResourcePage")
    @ApiOperation("试卷资源分页")
    public void paperResourcePage() {
        resourceDownService.paperResourcePage();
    }

    @GetMapping("testResourcePage")
    @ApiOperation("中考专题分页")
    public void testResourcePage() {
        resourceDownService.testResourcePage();
    }
}
