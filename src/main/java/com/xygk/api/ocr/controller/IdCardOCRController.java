package com.xygk.api.ocr.controller;

import com.xygk.api.ocr.entity.IdCardInfo;
import com.xygk.api.ocr.service.IdCardOCRService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 身份证OCR识别控制器
 * 专门处理身份证识别相关的API请求
 */
@Slf4j
@RestController
@RequestMapping("/api/ocr/idcard")
@Api(tags = "身份证OCR识别", description = "身份证文字识别相关接口")
public class IdCardOCRController {
    
    @Autowired
    private IdCardOCRService idCardOCRService;
    
    /**
     * 身份证识别接口
     */
    @PostMapping("/recognize")
    @ApiOperation(value = "身份证OCR识别", notes = "识别身份证正面或背面信息，返回结构化数据")
    public ResponseEntity<Map<String, Object>> recognizeIdCard(
            @ApiParam(value = "身份证图片文件", required = true)
            @RequestParam("image") MultipartFile image) {
        
        log.info("收到身份证识别请求，文件名: {}, 大小: {} bytes", 
                image.getOriginalFilename(), image.getSize());
        
        try {
            // 验证文件
            if (image.isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("上传的文件为空"));
            }
            
            // 验证文件类型
            String contentType = image.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return ResponseEntity.badRequest().body(createErrorResponse("请上传图片文件"));
            }
            
            // 验证文件大小（限制10MB）
            if (image.getSize() > 10 * 1024 * 1024) {
                return ResponseEntity.badRequest().body(createErrorResponse("图片文件过大，请上传小于10MB的图片"));
            }
            
            // 执行身份证识别
            IdCardInfo idCardInfo = idCardOCRService.recognizeIdCard(image);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", idCardInfo.isSuccess());
            response.put("message", idCardInfo.getMessage());
            response.put("timestamp", idCardInfo.getTimestamp());
            response.put("engine", idCardInfo.getOcrEngine());
            response.put("card_type", idCardInfo.getCardType());
            response.put("confidence", idCardInfo.getConfidence());
            
            // 添加识别的原始文本
            response.put("raw_texts", idCardInfo.getRawTexts());
            
            // 添加结构化信息
            Map<String, Object> structuredInfo = new HashMap<>();
            
            if ("front".equals(idCardInfo.getCardType()) || "both".equals(idCardInfo.getCardType()) || "unknown".equals(idCardInfo.getCardType())) {
                // 正面信息
                if (idCardInfo.getName() != null) {
                    structuredInfo.put("name", idCardInfo.getName());
                }
                if (idCardInfo.getGender() != null) {
                    structuredInfo.put("gender", idCardInfo.getGender());
                }
                if (idCardInfo.getNation() != null) {
                    structuredInfo.put("nation", idCardInfo.getNation());
                }
                if (idCardInfo.getBirthDate() != null) {
                    structuredInfo.put("birth_date", idCardInfo.getBirthDate());
                }
                if (idCardInfo.getAddress() != null) {
                    structuredInfo.put("address", idCardInfo.getAddress());
                }
                if (idCardInfo.getIdNumber() != null) {
                    structuredInfo.put("id_number", idCardInfo.getIdNumber());
                    structuredInfo.put("id_valid", idCardInfo.isValidIdNumber());
                    if (idCardInfo.getAge() != null) {
                        structuredInfo.put("age", idCardInfo.getAge());
                    }
                    structuredInfo.put("province_code", idCardInfo.getProvinceCode());
                    structuredInfo.put("city_code", idCardInfo.getCityCode());
                    structuredInfo.put("district_code", idCardInfo.getDistrictCode());
                    structuredInfo.put("gender_from_id", idCardInfo.getGenderFromIdNumber());
                    structuredInfo.put("gender_consistent", idCardInfo.isGenderConsistent());
                }
            }
            
            if ("back".equals(idCardInfo.getCardType()) || "both".equals(idCardInfo.getCardType()) || "unknown".equals(idCardInfo.getCardType())) {
                // 背面信息
                if (idCardInfo.getIssuingAuthority() != null) {
                    structuredInfo.put("issuing_authority", idCardInfo.getIssuingAuthority());
                }
                if (idCardInfo.getValidPeriod() != null) {
                    structuredInfo.put("valid_period", idCardInfo.getValidPeriod());
                }
                if (idCardInfo.getValidFrom() != null) {
                    structuredInfo.put("valid_from", idCardInfo.getValidFrom());
                }
                if (idCardInfo.getValidTo() != null) {
                    structuredInfo.put("valid_to", idCardInfo.getValidTo());
                }
            }
            
            response.put("structured_info", structuredInfo);
            
            if (idCardInfo.isSuccess()) {
                log.info("身份证识别成功，类型: {}, 置信度: {:.2f}%", 
                        idCardInfo.getCardType(), idCardInfo.getConfidence());
                return ResponseEntity.ok(response);
            } else {
                log.warn("身份证识别失败: {}", idCardInfo.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            // 记录详细错误信息，但不在响应中暴露敏感信息
            log.error("身份证识别异常: {}", e.getMessage(), e);

            // 根据异常类型提供不同的用户友好错误信息
            String userMessage;
            if (e.getMessage().contains("无法读取图片文件")) {
                userMessage = "图片文件格式不支持或已损坏，请上传有效的图片文件";
            } else if (e.getMessage().contains("超时")) {
                userMessage = "识别超时，请尝试上传较小的图片或稍后重试";
            } else if (e.getMessage().contains("Python环境")) {
                userMessage = "OCR服务暂时不可用，请联系管理员检查环境配置";
            } else {
                userMessage = "身份证识别服务暂时不可用，请稍后重试";
            }

            return ResponseEntity.internalServerError().body(createErrorResponse(userMessage));
        }
    }
    
    /**
     * 身份证正面识别接口
     */
    @PostMapping("/recognize/front")
    @ApiOperation(value = "身份证正面识别", notes = "专门识别身份证正面信息")
    public ResponseEntity<Map<String, Object>> recognizeIdCardFront(
            @ApiParam(value = "身份证正面图片", required = true)
            @RequestParam("image") MultipartFile image) {
        
        log.info("收到身份证正面识别请求");
        return recognizeIdCard(image);
    }
    
    /**
     * 身份证背面识别接口
     */
    @PostMapping("/recognize/back")
    @ApiOperation(value = "身份证背面识别", notes = "专门识别身份证背面信息")
    public ResponseEntity<Map<String, Object>> recognizeIdCardBack(
            @ApiParam(value = "身份证背面图片", required = true)
            @RequestParam("image") MultipartFile image) {
        
        log.info("收到身份证背面识别请求");
        return recognizeIdCard(image);
    }
    
    /**
     * 获取身份证OCR服务状态
     */
    @GetMapping("/status")
    @ApiOperation(value = "获取身份证OCR服务状态", notes = "检查身份证OCR服务是否可用")
    public ResponseEntity<Map<String, Object>> getIdCardOCRStatus() {
        
        Map<String, Object> status = new HashMap<>();
        status.put("service_name", "身份证OCR识别服务");
        status.put("version", "1.0.0");
        status.put("status", "running");
        status.put("timestamp", System.currentTimeMillis());
        
        // 可以添加更多状态信息
        Map<String, Object> capabilities = new HashMap<>();
        capabilities.put("front_recognition", true);
        capabilities.put("back_recognition", true);
        capabilities.put("structured_parsing", true);
        capabilities.put("id_validation", true);
        capabilities.put("age_calculation", true);
        capabilities.put("region_parsing", true);
        
        status.put("capabilities", capabilities);
        
        Map<String, String> supportedFormats = new HashMap<>();
        supportedFormats.put("input", "jpg, jpeg, png, bmp, gif");
        supportedFormats.put("max_size", "10MB");
        
        status.put("supported_formats", supportedFormats);
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * 身份证号码验证接口
     */
    @PostMapping("/validate")
    @ApiOperation(value = "身份证号码验证", notes = "验证身份证号码格式和校验位")
    public ResponseEntity<Map<String, Object>> validateIdNumber(
            @ApiParam(value = "身份证号码", required = true)
            @RequestParam("id_number") String idNumber) {
        
        log.info("收到身份证号码验证请求: {}", idNumber);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 创建临时的IdCardInfo对象进行验证
            IdCardInfo tempInfo = new IdCardInfo();
            tempInfo.setIdNumber(idNumber);
            
            boolean isValid = tempInfo.isValidIdNumber();
            response.put("success", true);
            response.put("id_number", idNumber);
            response.put("valid", isValid);
            
            if (isValid) {
                response.put("age", tempInfo.getAge());
                response.put("gender", tempInfo.getGenderFromIdNumber());
                response.put("province_code", tempInfo.getProvinceCode());
                response.put("city_code", tempInfo.getCityCode());
                response.put("district_code", tempInfo.getDistrictCode());
                response.put("message", "身份证号码格式正确");
            } else {
                response.put("message", "身份证号码格式不正确");
            }
            
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("身份证号码验证异常: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("身份证号码验证异常: " + e.getMessage()));
        }
    }
    
    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
