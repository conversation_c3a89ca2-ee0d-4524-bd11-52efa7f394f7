package com.xygk.api.ocr.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDate;
import java.util.List;

/**
 * 身份证信息实体类
 * 用于存储身份证OCR识别结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IdCardInfo {
    
    /**
     * 身份证类型：front-正面，back-背面
     */
    private String cardType;
    
    /**
     * 识别是否成功
     */
    private boolean success;
    
    /**
     * 识别消息
     */
    private String message;
    
    /**
     * 识别置信度
     */
    private double confidence;
    
    /**
     * 原始识别文本列表
     */
    private List<String> rawTexts;
    
    // ========== 正面信息 ==========
    
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 民族
     */
    private String nation;
    
    /**
     * 出生日期
     */
    private String birthDate;
    
    /**
     * 住址
     */
    private String address;
    
    /**
     * 身份证号码
     */
    private String idNumber;
    
    // ========== 背面信息 ==========
    
    /**
     * 签发机关
     */
    private String issuingAuthority;
    
    /**
     * 有效期限
     */
    private String validPeriod;
    
    /**
     * 有效期开始日期
     */
    private String validFrom;
    
    /**
     * 有效期结束日期
     */
    private String validTo;
    
    // ========== 辅助信息 ==========
    
    /**
     * 识别时间戳
     */
    private long timestamp;
    
    /**
     * 使用的OCR引擎
     */
    private String ocrEngine;
    
    /**
     * 图像质量评分 (0-100)
     */
    private int imageQuality;
    
    /**
     * 是否检测到人脸
     */
    private boolean faceDetected;
    
    /**
     * 是否检测到国徽
     */
    private boolean emblemDetected;
    
    /**
     * 创建成功结果
     */
    public static IdCardInfo success(String cardType, String message) {
        return IdCardInfo.builder()
                .success(true)
                .cardType(cardType)
                .message(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static IdCardInfo error(String cardType, String message) {
        return IdCardInfo.builder()
                .success(false)
                .cardType(cardType)
                .message(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 检查是否为有效的身份证号码
     */
    public boolean isValidIdNumber() {
        if (idNumber == null || idNumber.length() != 18) {
            return false;
        }
        
        // 简单的身份证号码格式验证
        return idNumber.matches("^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$");
    }
    
    /**
     * 获取年龄（根据身份证号码计算）
     */
    public Integer getAge() {
        if (!isValidIdNumber()) {
            return null;
        }
        
        try {
            String birthYear = idNumber.substring(6, 10);
            String birthMonth = idNumber.substring(10, 12);
            String birthDay = idNumber.substring(12, 14);
            
            LocalDate birthDate = LocalDate.of(
                Integer.parseInt(birthYear),
                Integer.parseInt(birthMonth),
                Integer.parseInt(birthDay)
            );
            
            LocalDate now = LocalDate.now();
            return now.getYear() - birthDate.getYear();
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取省份代码
     */
    public String getProvinceCode() {
        if (!isValidIdNumber()) {
            return null;
        }
        return idNumber.substring(0, 2);
    }
    
    /**
     * 获取城市代码
     */
    public String getCityCode() {
        if (!isValidIdNumber()) {
            return null;
        }
        return idNumber.substring(0, 4);
    }
    
    /**
     * 获取区县代码
     */
    public String getDistrictCode() {
        if (!isValidIdNumber()) {
            return null;
        }
        return idNumber.substring(0, 6);
    }
    
    /**
     * 判断性别（根据身份证号码）
     */
    public String getGenderFromIdNumber() {
        if (!isValidIdNumber()) {
            return null;
        }
        
        try {
            int genderCode = Integer.parseInt(idNumber.substring(16, 17));
            return genderCode % 2 == 0 ? "女" : "男";
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 验证性别是否一致
     */
    public boolean isGenderConsistent() {
        if (gender == null) {
            return true;
        }
        
        String genderFromId = getGenderFromIdNumber();
        return genderFromId == null || gender.equals(genderFromId);
    }
}
