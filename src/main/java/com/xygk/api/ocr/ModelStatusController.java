package com.xygk.api.ocr;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/ocr")
@Api(tags = "OCR模型状态")
public class ModelStatusController {
    
    @Value("${paddle.ocr.model-dir:models/ocr}")
    private String paddleModelDir;
    
    @Value("${tesseract.ocr.data-path:tessdata}")
    private String tesseractDataPath;
    
    @Value("${paddle.ocr.enable:true}")
    private boolean paddleOcrEnabled;
    
    @Value("${paddle.ocr.mock-mode:false}")
    private boolean paddleMockMode;
    
    @Value("${tesseract.ocr.enable:true}")
    private boolean tesseractOcrEnabled;
    
    @GetMapping("/model-status")
    @ApiOperation(value = "检查OCR模型状态", notes = "检查PaddleOCR和Tesseract模型文件的下载和配置状态")
    public Map<String, Object> getModelStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 基本信息
        status.put("timestamp", System.currentTimeMillis());
        status.put("paddleOcrEnabled", paddleOcrEnabled);
        status.put("paddleMockMode", paddleMockMode);
        status.put("tesseractOcrEnabled", tesseractOcrEnabled);
        
        // PaddleOCR状态
        Map<String, Object> paddleStatus = checkPaddleOCRStatus();
        status.put("paddleOCR", paddleStatus);
        
        // Tesseract状态
        Map<String, Object> tesseractStatus = checkTesseractStatus();
        status.put("tesseract", tesseractStatus);
        
        // 总体状态
        boolean overallReady = (boolean) paddleStatus.get("ready") || (boolean) tesseractStatus.get("ready");
        status.put("ready", overallReady);
        
        // 建议
        List<String> suggestions = new ArrayList<>();
        if (!paddleStatus.get("ready").equals(true) && !paddleMockMode) {
            suggestions.add("PaddleOCR模型未就绪，建议下载模型文件或启用模拟模式");
        }
        if (!tesseractStatus.get("ready").equals(true)) {
            suggestions.add("Tesseract训练数据未就绪，建议下载训练数据文件");
        }
        if (suggestions.isEmpty()) {
            suggestions.add("所有OCR引擎已就绪，可以正常使用");
        }
        status.put("suggestions", suggestions);
        
        return status;
    }
    
    private Map<String, Object> checkPaddleOCRStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("enabled", paddleOcrEnabled);
        status.put("mockMode", paddleMockMode);
        status.put("modelDir", paddleModelDir);
        
        if (!paddleOcrEnabled) {
            status.put("ready", false);
            status.put("message", "PaddleOCR已禁用");
            return status;
        }
        
        if (paddleMockMode) {
            status.put("ready", true);
            status.put("message", "PaddleOCR运行在模拟模式");
            return status;
        }
        
        // 检查模型目录
        File modelDir = new File(paddleModelDir);
        status.put("modelDirExists", modelDir.exists());
        
        if (!modelDir.exists()) {
            status.put("ready", false);
            status.put("message", "模型目录不存在");
            return status;
        }
        
        // 检查模型文件
        List<String> requiredModels = new ArrayList<>();
        requiredModels.add("ch_PP-OCRv4_det_infer");
        requiredModels.add("ch_PP-OCRv4_rec_infer");
        
        List<String> foundModels = new ArrayList<>();
        List<String> missingModels = new ArrayList<>();
        
        for (String modelName : requiredModels) {
            File modelSubDir = new File(modelDir, modelName);
            if (modelSubDir.exists() && modelSubDir.isDirectory()) {
                // 检查是否有必需的模型文件
                File pdmodel = new File(modelSubDir, "inference.pdmodel");
                File pdiparams = new File(modelSubDir, "inference.pdiparams");
                
                if (pdmodel.exists() && pdiparams.exists()) {
                    foundModels.add(modelName);
                } else {
                    missingModels.add(modelName + " (缺少模型文件)");
                }
            } else {
                missingModels.add(modelName);
            }
        }
        
        status.put("foundModels", foundModels);
        status.put("missingModels", missingModels);
        
        boolean ready = missingModels.isEmpty();
        status.put("ready", ready);
        
        if (ready) {
            status.put("message", "PaddleOCR模型已就绪");
        } else {
            status.put("message", "缺少必需的模型文件: " + String.join(", ", missingModels));
        }
        
        return status;
    }
    
    private Map<String, Object> checkTesseractStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("enabled", tesseractOcrEnabled);
        status.put("dataPath", tesseractDataPath);
        
        if (!tesseractOcrEnabled) {
            status.put("ready", false);
            status.put("message", "Tesseract已禁用");
            return status;
        }
        
        // 检查数据目录
        File dataDir = new File(tesseractDataPath);
        status.put("dataDirExists", dataDir.exists());
        
        if (!dataDir.exists()) {
            status.put("ready", false);
            status.put("message", "训练数据目录不存在");
            return status;
        }
        
        // 检查训练数据文件
        List<String> requiredData = new ArrayList<>();
        requiredData.add("chi_sim.traineddata");
        requiredData.add("eng.traineddata");
        
        List<String> foundData = new ArrayList<>();
        List<String> missingData = new ArrayList<>();
        
        for (String dataFile : requiredData) {
            File file = new File(dataDir, dataFile);
            if (file.exists() && file.isFile() && file.length() > 0) {
                foundData.add(dataFile);
            } else {
                missingData.add(dataFile);
            }
        }
        
        // 检查可选文件
        List<String> optionalData = new ArrayList<>();
        optionalData.add("osd.traineddata");
        optionalData.add("chi_tra.traineddata");
        List<String> foundOptional = new ArrayList<>();
        
        for (String dataFile : optionalData) {
            File file = new File(dataDir, dataFile);
            if (file.exists() && file.isFile() && file.length() > 0) {
                foundOptional.add(dataFile);
            }
        }
        
        status.put("foundData", foundData);
        status.put("missingData", missingData);
        status.put("foundOptional", foundOptional);
        
        boolean ready = missingData.isEmpty();
        status.put("ready", ready);
        
        if (ready) {
            status.put("message", "Tesseract训练数据已就绪");
        } else {
            status.put("message", "缺少必需的训练数据: " + String.join(", ", missingData));
        }
        
        return status;
    }
    
    @GetMapping("/download-links")
    @ApiOperation(value = "获取模型下载链接", notes = "获取PaddleOCR和Tesseract模型的官方下载链接")
    public Map<String, Object> getDownloadLinks() {
        Map<String, Object> links = new HashMap<>();
        
        // PaddleOCR模型下载链接
        Map<String, String> paddleLinks = new HashMap<>();
        paddleLinks.put("检测模型", "https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_det_infer.tar");
        paddleLinks.put("识别模型", "https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_rec_infer.tar");
        paddleLinks.put("分类模型", "https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar");
        links.put("paddleOCR", paddleLinks);
        
        // Tesseract训练数据下载链接
        Map<String, String> tesseractLinks = new HashMap<>();
        tesseractLinks.put("中文简体", "https://github.com/tesseract-ocr/tessdata/raw/main/chi_sim.traineddata");
        tesseractLinks.put("英文", "https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata");
        tesseractLinks.put("方向检测", "https://github.com/tesseract-ocr/tessdata/raw/main/osd.traineddata");
        tesseractLinks.put("中文繁体", "https://github.com/tesseract-ocr/tessdata/raw/main/chi_tra.traineddata");
        links.put("tesseract", tesseractLinks);
        
        // 下载说明
        Map<String, String> instructions = new HashMap<>();
        instructions.put("paddleOCR", "下载后解压到 " + paddleModelDir + " 目录");
        instructions.put("tesseract", "下载后放置到 " + tesseractDataPath + " 目录");
        instructions.put("配置", "下载完成后在application.yml中设置 paddle.ocr.mock-mode: false");
        links.put("instructions", instructions);
        
        return links;
    }
}
