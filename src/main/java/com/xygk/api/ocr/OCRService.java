package com.xygk.api.ocr;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OCRService {
    private Tesseract tesseract;

    @Value("${tesseract.ocr.data-path:tessdata}")
    private String tesseractDataPath;

    @Value("${tesseract.ocr.language:chi_sim+eng}")
    private String tesseractLanguage;

    @Value("${tesseract.ocr.enable:true}")
    private boolean tesseractEnabled;

    @Value("${tesseract.ocr.ocr-engine-mode:1}")
    private int ocrEngineMode;

    @Value("${tesseract.ocr.page-seg-mode:6}")
    private int pageSegMode;

    @Value("${tesseract.ocr.dpi:300}")
    private int dpi;

    @Value("${tesseract.ocr.scale-factor:1.5}")
    private double scaleFactor;

    @Value("${tesseract.ocr.enable-preprocessing:true}")
    private boolean enablePreprocessing;

    @Value("${tesseract.ocr.min-confidence:60}")
    private int minConfidence;

    @Value("${tesseract.ocr.chinese-only:true}")
    private boolean chineseOnly;

    @Value("${tesseract.ocr.aggressive-cleaning:true}")
    private boolean aggressiveCleaning;

    @PostConstruct
    public void init() {
        if (!tesseractEnabled) {
            log.info("Tesseract OCR已禁用");
            return;
        }

        try {
            tesseract = new Tesseract();

            // 设置数据路径
            File dataDir = new File(tesseractDataPath);
            if (!dataDir.exists()) {
                log.error("Tesseract数据目录不存在: {}", dataDir.getAbsolutePath());
                return;
            }

            tesseract.setDatapath(tesseractDataPath);
            tesseract.setLanguage(tesseractLanguage); // 使用中英文混合

            // 设置基本的OCR参数
            tesseract.setOcrEngineMode(ocrEngineMode);
            tesseract.setPageSegMode(pageSegMode);

            log.info("使用基本Tesseract配置，最大兼容性模式");

            log.info("Tesseract配置详情:");
            log.info("- OCR引擎模式: {}", ocrEngineMode);
            log.info("- 页面分割模式: {}", pageSegMode);
            log.info("- DPI设置: {}", dpi);
            log.info("- 缩放因子: {}", scaleFactor);

            log.info("Tesseract OCR初始化成功");
            log.info("数据路径: {}", dataDir.getAbsolutePath());
            log.info("语言设置: {}", tesseractLanguage);

        } catch (Exception e) {
            log.error("Tesseract OCR初始化失败: {}", e.getMessage(), e);
            tesseract = null;
        }
    }
    
    public List<String> recognizeText(MultipartFile imageFile) {
        if (!tesseractEnabled) {
            throw new RuntimeException("Tesseract OCR已禁用");
        }

        if (tesseract == null) {
            throw new RuntimeException("Tesseract OCR未正确初始化，请检查配置和训练数据");
        }

        if (imageFile == null || imageFile.isEmpty()) {
            throw new RuntimeException("图片文件为空");
        }

        File tempFile = null;
        try {
            // 验证文件类型
            String contentType = imageFile.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw new RuntimeException("不支持的文件类型，请上传图片文件");
            }

            log.debug("开始处理图片，原始文件类型: {}, 大小: {} bytes",
                     contentType, imageFile.getSize());

            // 创建临时文件，统一使用PNG格式避免JPEG格式问题
            tempFile = File.createTempFile("tesseract_ocr_", ".png");
            log.debug("创建临时文件: {}", tempFile.getAbsolutePath());

            // 读取并预处理图片
            BufferedImage image = null;
            try {
                // 尝试读取图片
                image = ImageIO.read(new ByteArrayInputStream(imageFile.getBytes()));
                if (image == null) {
                    throw new RuntimeException("无法读取图片文件，可能文件损坏或格式不支持");
                }

                log.debug("原始图片读取成功，尺寸: {}x{}, 类型: {}",
                         image.getWidth(), image.getHeight(), image.getType());

                // 使用最基本的图片处理
                image = preprocessImageBasic(image);
                log.debug("基础图片预处理完成");

                log.debug("图片预处理完成，最终尺寸: {}x{}", image.getWidth(), image.getHeight());

                // 保存为PNG格式
                if (!ImageIO.write(image, "PNG", tempFile)) {
                    throw new RuntimeException("图片格式转换失败");
                }

                log.debug("图片已保存为PNG格式，文件大小: {} bytes", tempFile.length());

            } catch (IOException e) {
                throw new RuntimeException("图片处理失败: " + e.getMessage(), e);
            }

            // 验证文件大小
            if (tempFile.length() == 0) {
                throw new RuntimeException("图片文件处理后为空");
            }

            log.debug("开始OCR识别，处理后文件大小: {} bytes", tempFile.length());

            // 执行OCR识别
            String result = tesseract.doOCR(tempFile);

            log.debug("OCR识别完成，原始结果长度: {}", result != null ? result.length() : 0);

            if (result == null || result.trim().isEmpty()) {
                log.warn("OCR识别结果为空，可能是图片中没有文字或文字不清晰");
                List<String> emptyResult = new ArrayList<>();
                emptyResult.add("未识别到文字内容，请确保图片清晰且包含文字");
                return emptyResult;
            }

            // 处理识别结果，使用最宽松的清理
            List<String> lines = Arrays.stream(result.split("\n"))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(this::cleanOCRTextGentle)  // 使用温和的清理
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());

            log.info("Tesseract OCR识别成功，原始{}行，清理后{}行文字",
                    result.split("\n").length, lines.size());

            // 如果清理后没有有效文本，返回提示
            if (lines.isEmpty()) {
                lines.add("识别完成，但未检测到清晰的文字内容");
                lines.add("建议：");
                lines.add("1. 使用更清晰、高对比度的图片");
                lines.add("2. 确保图片中的文字足够大");
                lines.add("3. 避免倾斜或模糊的文字");
                lines.add("4. 尝试不同的图片格式");
            }

            return lines;

        } catch (TesseractException e) {
            log.error("Tesseract OCR识别失败: {}", e.getMessage(), e);

            // 如果是图片格式问题，尝试备用处理方法
            if (e.getMessage().contains("JFIF") || e.getMessage().contains("marker") ||
                e.getMessage().contains("SOI")) {
                log.warn("检测到图片格式问题，尝试备用处理方法");
                try {
                    return processImageWithFallback(imageFile);
                } catch (Exception fallbackException) {
                    log.error("备用处理方法也失败: {}", fallbackException.getMessage());
                    throw new RuntimeException("图片格式有问题，OCR识别失败。请尝试使用其他格式的图片（如PNG）", e);
                }
            }

            throw new RuntimeException("OCR识别失败: " + e.getMessage(), e);
        } catch (IOException e) {
            log.error("文件处理失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件处理失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("OCR识别过程中发生未知错误: {}", e.getMessage(), e);
            throw new RuntimeException("OCR识别失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                    log.debug("临时文件已删除: {}", tempFile.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 备用图片处理方法
     */
    private List<String> processImageWithFallback(MultipartFile imageFile) throws Exception {
        File tempFile = null;
        try {
            log.info("使用备用图片处理方法");

            // 创建临时文件
            tempFile = File.createTempFile("tesseract_fallback_", ".bmp");

            // 读取原始图片
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageFile.getBytes()));
            if (originalImage == null) {
                throw new RuntimeException("无法读取图片文件");
            }

            // 创建新的BufferedImage，确保格式正确
            BufferedImage processedImage = new BufferedImage(
                originalImage.getWidth(),
                originalImage.getHeight(),
                BufferedImage.TYPE_INT_RGB
            );

            // 绘制图片到新的BufferedImage
            processedImage.getGraphics().drawImage(originalImage, 0, 0, null);

            // 保存为BMP格式（最兼容的格式）
            ImageIO.write(processedImage, "BMP", tempFile);

            log.debug("备用方法：图片已转换为BMP格式，文件大小: {} bytes", tempFile.length());

            // 执行OCR识别
            String result = tesseract.doOCR(tempFile);

            if (result == null || result.trim().isEmpty()) {
                List<String> emptyResult = new ArrayList<>();
                emptyResult.add("未识别到文字内容（备用方法）");
                return emptyResult;
            }

            List<String> lines = Arrays.stream(result.split("\n"))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());

            log.info("备用方法OCR识别成功，识别到 {} 行文字", lines.size());
            return lines;

        } finally {
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                } catch (IOException e) {
                    log.warn("删除备用临时文件失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 基础图片预处理（最简单的处理）
     */
    private BufferedImage preprocessImageBasic(BufferedImage originalImage) {
        try {
            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            // 只做最基本的格式转换，确保是RGB格式
            if (originalImage.getType() == BufferedImage.TYPE_INT_RGB) {
                // 如果已经是RGB格式，直接返回
                return originalImage;
            }

            // 转换为RGB格式
            BufferedImage rgbImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = rgbImage.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);
            g2d.drawImage(originalImage, 0, 0, null);
            g2d.dispose();

            log.debug("图片已转换为RGB格式");
            return rgbImage;
        } catch (Exception e) {
            log.warn("基础图片预处理失败，使用原图: {}", e.getMessage());
            return originalImage;
        }
    }

    /**
     * 高级图片预处理，提高OCR识别质量
     */
    private BufferedImage preprocessImageAdvanced(BufferedImage originalImage) {
        try {
            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            log.debug("开始高级图片预处理，原始尺寸: {}x{}", width, height);

            // 1. 智能缩放图片（针对中文字符优化）
            if (scaleFactor != 1.0) {
                // 对于小图片，进行更大的缩放以提高识别率
                double actualScaleFactor = scaleFactor;
                if (width < 600 || height < 400) {
                    actualScaleFactor = Math.max(scaleFactor, 2.5); // 小图片使用更大的缩放
                }

                int newWidth = (int) (width * actualScaleFactor);
                int newHeight = (int) (height * actualScaleFactor);

                BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = scaledImage.createGraphics();

                // 使用高质量渲染设置
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                // 填充白色背景
                g2d.setColor(Color.WHITE);
                g2d.fillRect(0, 0, newWidth, newHeight);

                g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
                g2d.dispose();

                originalImage = scaledImage;
                width = newWidth;
                height = newHeight;
                log.debug("图片已智能缩放到: {}x{} (缩放因子: {})", width, height, actualScaleFactor);
            }

            // 2. 转换为灰度并去噪
            BufferedImage grayImage = convertToGrayscaleWithDenoising(originalImage);

            // 3. 自适应二值化处理
            BufferedImage binaryImage = adaptiveBinarization(grayImage);

            // 4. 形态学处理（去除噪点）
            BufferedImage cleanedImage = morphologicalCleaning(binaryImage);

            // 5. 转换回RGB格式（Tesseract需要）
            BufferedImage finalImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2dFinal = finalImage.createGraphics();
            g2dFinal.setColor(Color.WHITE);
            g2dFinal.fillRect(0, 0, width, height);
            g2dFinal.drawImage(cleanedImage, 0, 0, null);
            g2dFinal.dispose();

            log.debug("高级图片预处理完成：缩放 -> 灰度去噪 -> 自适应二值化 -> 形态学清理 -> RGB转换");
            return finalImage;

        } catch (Exception e) {
            log.warn("高级图片预处理失败，使用基础处理: {}", e.getMessage());
            return preprocessImageBasic(originalImage);
        }
    }

    /**
     * 转换为灰度并去噪
     */
    private BufferedImage convertToGrayscaleWithDenoising(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage grayImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);

        // 转换为灰度并应用简单的去噪
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int r = (rgb >> 16) & 0xFF;
                int g = (rgb >> 8) & 0xFF;
                int b = rgb & 0xFF;

                // 使用加权平均转换为灰度
                int gray = (int) (0.299 * r + 0.587 * g + 0.114 * b);

                int grayRgb = (gray << 16) | (gray << 8) | gray;
                grayImage.setRGB(x, y, grayRgb);
            }
        }

        return grayImage;
    }

    /**
     * 自适应二值化处理
     */
    private BufferedImage adaptiveBinarization(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage binaryImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);

        // 计算全局阈值
        int totalGray = 0;
        int pixelCount = width * height;

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int gray = (rgb >> 16) & 0xFF;
                totalGray += gray;
            }
        }

        int globalThreshold = totalGray / pixelCount;

        // 应用阈值
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int gray = (rgb >> 16) & 0xFF;

                // 使用稍微调整的阈值
                int threshold = (int) (globalThreshold * 0.8); // 稍微降低阈值，保留更多文字

                if (gray > threshold) {
                    binaryImage.setRGB(x, y, Color.WHITE.getRGB());
                } else {
                    binaryImage.setRGB(x, y, Color.BLACK.getRGB());
                }
            }
        }

        return binaryImage;
    }

    /**
     * 形态学清理（去除小噪点）
     */
    private BufferedImage morphologicalCleaning(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage cleanedImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);

        // 简单的形态学开运算（腐蚀后膨胀）
        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                int centerPixel = image.getRGB(x, y);

                // 检查3x3邻域
                boolean hasBlackNeighbor = false;
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dx = -1; dx <= 1; dx++) {
                        int neighborPixel = image.getRGB(x + dx, y + dy);
                        if (neighborPixel == Color.BLACK.getRGB()) {
                            hasBlackNeighbor = true;
                            break;
                        }
                    }
                    if (hasBlackNeighbor) {
                        break;
                    }
                }

                // 如果是孤立的黑点，则去除
                if (centerPixel == Color.BLACK.getRGB() && !hasBlackNeighbor) {
                    cleanedImage.setRGB(x, y, Color.WHITE.getRGB());
                } else {
                    cleanedImage.setRGB(x, y, centerPixel);
                }
            }
        }

        return cleanedImage;
    }

    /**
     * 增强图片对比度（保留作为备用方法）
     */
    private BufferedImage enhanceContrast(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage enhancedImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);

        // 简单的对比度增强算法
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int gray = (rgb >> 16) & 0xFF; // 获取灰度值

                // 增强对比度：使用简单的线性变换
                int enhanced = (int) (((gray - 128) * 1.5) + 128);
                enhanced = Math.max(0, Math.min(255, enhanced));

                // 二值化处理（可选，对某些图片效果更好）
                if (enhanced > 127) {
                    enhanced = 255; // 白色
                } else {
                    enhanced = 0;   // 黑色
                }

                int newRgb = (enhanced << 16) | (enhanced << 8) | enhanced;
                enhancedImage.setRGB(x, y, newRgb);
            }
        }

        return enhancedImage;
    }

    /**
     * 温和的文本清理方法（最宽松）
     */
    private String cleanOCRTextGentle(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }

        // 只做最基本的清理
        text = text.trim();

        // 移除控制字符
        text = text.replaceAll("[\\x00-\\x1F\\x7F]", "");

        // 合并多个空格
        text = text.replaceAll("\\s+", " ");

        // 如果长度太短，直接丢弃
        if (text.length() < 1) {
            return "";
        }

        return text;
    }

    /**
     * 激进的乱码清理方法
     */
    private String cleanOCRTextAggressive(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }

        String originalText = text;

        // 1. 首先移除明显的乱码模式
        // 移除类似 "LLSO%2LL626122L0%E" 的模式
        text = text.replaceAll("[A-Z]{2,}%?\\d*[A-Z]*%?[A-Z]*", " ");
        // 移除连续的相同字符（如LL、626122）
        text = text.replaceAll("(.)\\1{2,}", " ");
        // 移除包含%和数字的乱码
        text = text.replaceAll("\\w*%\\w*", " ");
        // 移除纯英文乱码词（如wees, BEMNIY）
        text = text.replaceAll("\\b[a-zA-Z]{4,}\\b", " ");

        // 2. 移除所有非中文、非数字、非基本英文字母的字符
        text = text.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s\\.,;:!?()（）【】\"\"''、。，；：！？]", " ");
        text = text.replaceAll("\\s+", " ").trim();

        // 3. 如果长度太短，直接丢弃
        if (text.length() < 2) {
            return "";
        }

        // 4. 检查中文字符比例
        long chineseCount = text.chars()
                .filter(c -> c >= 0x4e00 && c <= 0x9fa5)
                .count();

        long totalValidChars = text.chars()
                .filter(c -> Character.isLetterOrDigit(c))
                .count();

        // 4. 如果没有中文字符且英文字符过于分散，可能是乱码
        if (chineseCount == 0 && totalValidChars > 0) {
            // 检查是否为连续的有意义英文单词
            if (!text.matches(".*\\b[a-zA-Z]{2,}\\b.*")) {
                log.debug("移除分散英文字符行: {}", originalText);
                return "";
            }
        }

        // 5. 如果有中文字符，但比例太低，可能是乱码
        if (totalValidChars > 0 && chineseCount > 0) {
            double chineseRatio = (double) chineseCount / totalValidChars;
            if (chineseRatio < 0.3 && text.length() > 5) {
                log.debug("移除中文比例过低行: {}", originalText);
                return "";
            }
        }

        // 6. 检查是否包含常见的有意义词汇
        if (text.length() > 3 && !containsMeaningfulContent(text)) {
            log.debug("移除无意义内容行: {}", originalText);
            return "";
        }

        return text;
    }

    /**
     * 标准乱码清理方法
     */
    private String cleanOCRText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }

        String originalText = text;

        // 1. 移除常见的OCR乱码字符和模式
        // 移除您提到的乱码模式：LLSO%2LL626122L0%E wees BEMNIY HN
        text = text.replaceAll("[%\\\\~`!@#$%^&*()_+={}\\[\\]|:;\"'<>?,./]+", " "); // 移除特殊字符
        text = text.replaceAll("[\\x00-\\x1F\\x7F]", ""); // 移除控制字符
        text = text.replaceAll("\\b[A-Z]{2,}\\b", " "); // 移除连续大写字母（如LLSO, BEMNIY, HN）
        text = text.replaceAll("\\b\\d{2,}\\b", " "); // 移除长数字串（如626122）
        text = text.replaceAll("\\b[a-z]{4,}\\b", " "); // 移除长小写字母串（如wees）
        text = text.replaceAll("[L]{2,}", " "); // 移除连续的L字符
        text = text.replaceAll("\\s+", " "); // 合并多个空格
        text = text.trim();

        // 2. 检查是否为纯乱码行
        if (text.length() < 2) {
            return "";
        }

        // 3. 计算有效字符比例
        long validCharCount = text.chars()
                .filter(c -> Character.isLetterOrDigit(c) || Character.isWhitespace(c) || isValidChineseChar(c))
                .count();

        double validRatio = (double) validCharCount / text.length();

        // 4. 如果有效字符比例太低，认为是乱码
        if (validRatio < 0.6) {
            log.debug("移除低有效字符比例行 ({}%): {}", (int)(validRatio * 100), originalText);
            return "";
        }

        // 5. 检查是否包含过多单个字符
        if (text.length() > 10) {
            long singleCharCount = text.chars()
                    .filter(c -> c >= 'A' && c <= 'Z')
                    .count();

            if (singleCharCount > text.length() * 0.7) {
                log.debug("移除过多单个大写字母行: {}", originalText);
                return "";
            }
        }

        // 6. 移除明显的乱码模式
        if (text.matches(".*[A-Z]{3,}\\s+[A-Z]{3,}.*") && !containsValidWords(text)) {
            log.debug("移除乱码模式行: {}", originalText);
            return "";
        }

        return text;
    }

    /**
     * 检查是否为有效的中文字符
     */
    private boolean isValidChineseChar(int c) {
        return (c >= 0x4E00 && c <= 0x9FFF) || // 基本汉字
               (c >= 0x3400 && c <= 0x4DBF) || // 扩展A
               (c >= 0x20000 && c <= 0x2A6DF); // 扩展B
    }

    /**
     * 检查是否包含有意义的内容
     */
    private boolean containsMeaningfulContent(String text) {
        // 检查是否包含常见的中文词汇
        String[] commonChineseWords = {
            "的", "了", "是", "我", "不", "在", "人", "有", "来", "他", "这", "上", "着", "个", "地", "到", "大", "里", "说", "就", "去", "子", "得", "也", "和", "那", "要", "下", "看", "天", "时", "过", "出", "小", "么", "起", "你", "都", "把", "好", "还", "多", "没", "为", "又", "可", "家", "学", "只", "以", "主", "会", "样", "年", "想", "生", "同", "老", "中", "十", "从", "自", "面", "前", "头", "道", "它", "后", "然", "走", "很", "像", "见", "两", "用", "她", "国", "动", "进", "成", "回", "什", "边", "作", "对", "开", "而", "己", "些", "现", "山", "民", "候", "经", "发", "工", "向", "事", "命", "给", "长", "水", "几", "义", "三", "声", "于", "高", "手", "知", "理", "眼", "志", "点", "心", "战", "二", "问", "但", "身", "方", "实", "吃", "做", "叫", "当", "住", "听", "革", "打", "呢", "真", "全", "才", "四", "已", "所", "敌", "之", "最", "光", "产", "情", "路", "分", "总", "条", "白", "话", "东", "席", "次", "亲", "如", "被", "花", "口", "放", "儿", "常", "气", "五", "第", "使", "写", "军", "吧", "文", "运", "再", "果", "怎", "定", "许", "快", "明", "行", "因", "别", "飞", "外", "树", "物", "活", "部", "门", "无", "往", "船", "望", "新", "带", "队", "先", "力", "完", "却", "站", "代", "员", "机", "更", "九", "您", "每", "风", "级", "跟", "笑", "啊", "孩", "万", "少", "直", "意", "夜", "比", "阶", "连", "车", "重", "便", "斗", "马", "哪", "化", "太", "指", "变", "社", "似", "士", "者", "干", "石", "满", "日", "决", "百", "原", "拿", "群", "究", "各", "六", "本", "思", "解", "立", "河", "村", "八", "难", "早", "论", "吗", "根", "共", "让", "相", "研", "今", "其", "书", "坐", "接", "应", "关", "信", "觉", "步", "反", "处", "记", "将", "千", "找", "争", "领", "或", "师", "结", "块", "跑", "谁", "草", "越", "字", "加", "脚", "紧", "爱", "等", "习", "阵", "怕", "月", "青", "半", "火", "法", "题", "建", "赶", "位", "唱", "海", "七", "女", "任", "件", "感", "准", "张", "团", "屋", "离", "色", "脸", "片", "科", "倒", "睛", "利", "世", "刚", "且", "由", "送", "切", "星", "导", "晚", "表", "够", "整", "认", "响", "雪", "流", "未", "场", "该", "并", "底", "深", "刻", "平", "伟", "忙", "提", "确", "近", "亮", "轻", "讲", "农", "古", "黑", "告", "界", "拉", "名", "呀", "土", "清", "阳", "照", "办", "史", "改", "历", "转", "画", "造", "嘴", "此", "治", "北", "必", "服", "雨", "穿", "内", "识", "验", "传", "业", "菜", "爬", "睡", "兴", "形", "量", "咱", "观", "苦", "体", "众", "通", "冲", "合", "破", "友", "度", "术", "饭", "公", "旁", "房", "极", "南", "枪", "读", "沙", "岁", "线", "野", "坚", "空", "收", "算", "至", "政", "城", "劳", "落", "钱", "特", "围", "弟", "胜", "教", "热", "获", "艺", "铁"
        };

        for (String word : commonChineseWords) {
            if (text.contains(word)) {
                return true;
            }
        }

        // 检查是否包含有意义的英文单词
        String[] commonEnglishWords = {
            "the", "and", "for", "are", "but", "not", "you", "all", "can", "had", "her", "was", "one", "our", "out", "day", "get", "has", "him", "his", "how", "man", "new", "now", "old", "see", "two", "way", "who", "boy", "did", "its", "let", "put", "say", "she", "too", "use", "about", "after", "again", "before", "could", "first", "from", "good", "great", "group", "hand", "hard", "high", "important", "large", "last", "left", "life", "little", "long", "make", "most", "move", "much", "name", "need", "number", "other", "part", "place", "point", "right", "same", "seem", "small", "sound", "still", "such", "take", "tell", "thing", "think", "time", "turn", "very", "want", "water", "well", "were", "what", "when", "where", "which", "while", "with", "word", "work", "world", "would", "write", "year", "young"
        };

        for (String word : commonEnglishWords) {
            if (text.toLowerCase().contains(word)) {
                return true;
            }
        }

        // 检查是否包含数字序列（可能是有意义的）
        if (text.matches(".*\\d{2,}.*")) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否包含有效的词汇（保留原方法作为备用）
     */
    private boolean containsValidWords(String text) {
        // 检查是否包含常见的中文词汇或英文单词
        String[] commonWords = {"的", "了", "是", "我", "不", "在", "人", "有", "来", "他", "这", "上", "着", "个", "地", "到", "大", "里", "说", "就", "去", "子", "得", "也", "和", "那", "要", "下", "看", "天", "时", "过", "出", "小", "么", "起", "你", "都", "把", "好", "还", "多", "没", "为", "又", "可", "家", "学", "只", "以", "主", "会", "样", "年", "想", "生", "同", "老", "中", "十", "从", "自", "面", "前", "头", "道", "它", "后", "然", "走", "很", "像", "见", "两", "用", "她", "国", "动", "进", "成", "回", "什", "边", "作", "对", "开", "而", "己", "些", "现", "山", "民", "候", "经", "发", "工", "向", "事", "命", "给", "长", "水", "几", "义", "三", "声", "于", "高", "手", "知", "理", "眼", "志", "点", "心", "战", "二", "问", "但", "身", "方", "实", "吃", "做", "叫", "当", "住", "听", "革", "打", "呢", "真", "全", "才", "四", "已", "所", "敌", "之", "最", "光", "产", "情", "路", "分", "总", "条", "白", "话", "东", "席", "次", "亲", "如", "被", "花", "口", "放", "儿", "常", "气", "五", "第", "使", "写", "军", "吧", "文", "运", "再", "果", "怎", "定", "许", "快", "明", "行", "因", "别", "飞", "外", "树", "物", "活", "部", "门", "无", "往", "船", "望", "新", "带", "队", "先", "力", "完", "却", "站", "代", "员", "机", "更", "九", "您", "每", "风", "级", "跟", "笑", "啊", "孩", "万", "少", "直", "意", "夜", "比", "阶", "连", "车", "重", "便", "斗", "马", "哪", "化", "太", "指", "变", "社", "似", "士", "者", "干", "石", "满", "日", "决", "百", "原", "拿", "群", "究", "各", "六", "本", "思", "解", "立", "河", "村", "八", "难", "早", "论", "吗", "根", "共", "让", "相", "研", "今", "其", "书", "坐", "接", "应", "关", "信", "觉", "步", "反", "处", "记", "将", "千", "找", "争", "领", "或", "师", "结", "块", "跑", "谁", "草", "越", "字", "加", "脚", "紧", "爱", "等", "习", "阵", "怕", "月", "青", "半", "火", "法", "题", "建", "赶", "位", "唱", "海", "七", "女", "任", "件", "感", "准", "张", "团", "屋", "离", "色", "脸", "片", "科", "倒", "睛", "利", "世", "刚", "且", "由", "送", "切", "星", "导", "晚", "表", "够", "整", "认", "响", "雪", "流", "未", "场", "该", "并", "底", "深", "刻", "平", "伟", "忙", "提", "确", "近", "亮", "轻", "讲", "农", "古", "黑", "告", "界", "拉", "名", "呀", "土", "清", "阳", "照", "办", "史", "改", "历", "转", "画", "造", "嘴", "此", "治", "北", "必", "服", "雨", "穿", "内", "识", "验", "传", "业", "菜", "爬", "睡", "兴", "形", "量", "咱", "观", "苦", "体", "众", "通", "冲", "合", "破", "友", "度", "术", "饭", "公", "旁", "房", "极", "南", "枪", "读", "沙", "岁", "线", "野", "坚", "空", "收", "算", "至", "政", "城", "劳", "落", "钱", "特", "围", "弟", "胜", "教", "热", "获", "艺", "铁", "the", "and", "for", "are", "but", "not", "you", "all", "can", "had", "her", "was", "one", "our", "out", "day", "get", "has", "him", "his", "how", "man", "new", "now", "old", "see", "two", "way", "who", "boy", "did", "its", "let", "put", "say", "she", "too", "use"};

        for (String word : commonWords) {
            if (text.contains(word)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查Tesseract是否可用
     */
    public boolean isAvailable() {
        return tesseractEnabled && tesseract != null;
    }

    /**
     * 获取Tesseract版本信息
     */
    public String getVersion() {
        if (!isAvailable()) {
            return "Tesseract不可用";
        }
        try {
            // 这里可以添加获取版本的逻辑
            return "Tesseract 4.x";
        } catch (Exception e) {
            return "版本信息获取失败";
        }
    }
} 