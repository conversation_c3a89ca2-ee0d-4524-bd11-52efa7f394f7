package com.xygk.api.ocr;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.xygk.api.ocr.service.IdCardOCRService;
import com.xygk.api.ocr.entity.IdCardInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/ocr")
@Api(tags = "OCR文字识别")
public class OCRController {

    @Autowired
    private PaddleOCRService paddleOCRService;

    @Autowired
    private SimplePaddleOCRService simplePaddleOCRService;

    @Autowired
    private OCRService tesseractOCRService;

    @Autowired
    private IdCardOCRService idCardOCRService;

    @PostMapping("/paddle/recognize")
    @ApiOperation(value = "PaddleOCR文字识别", notes = "使用PaddleOCR进行图片文字识别")
    public ResponseEntity<Map<String, Object>> paddleOCRRecognize(
            @ApiParam(value = "图片文件", required = true)
            @RequestParam("image") MultipartFile image) {

        log.info("收到PaddleOCR识别请求，文件名: {}, 大小: {} bytes",
                image.getOriginalFilename(), image.getSize());

        try {
            // 优先使用SimplePaddleOCRService
            SimplePaddleOCRService.OCRResult result = simplePaddleOCRService.recognizeText(image);

            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("texts", result.getTexts());
            response.put("timestamp", result.getTimestamp());
            response.put("engine", "PaddleOCR");
            response.put("mode", simplePaddleOCRService.getStatus());

            if (result.isSuccess()) {
                log.info("PaddleOCR识别成功，识别到{}行文本", result.getTexts().size());
                return ResponseEntity.ok(response);
            } else {
                log.warn("PaddleOCR识别失败: {}", result.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            log.error("PaddleOCR识别异常: {}", e.getMessage(), e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "OCR识别异常: " + e.getMessage());
            errorResponse.put("texts", new String[0]);
            errorResponse.put("timestamp", System.currentTimeMillis());
            errorResponse.put("engine", "PaddleOCR");

            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    @PostMapping("/tesseract/recognize")
    @ApiOperation(value = "Tesseract文字识别", notes = "使用Tesseract进行图片文字识别")
    public ResponseEntity<Map<String, Object>> tesseractOCRRecognize(
            @ApiParam(value = "图片文件", required = true)
            @RequestParam("image") MultipartFile image) {

        log.info("收到Tesseract识别请求，文件名: {}, 大小: {} bytes",
                image.getOriginalFilename(), image.getSize());

        try {
            java.util.List<String> results = tesseractOCRService.recognizeText(image);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "识别成功");
            response.put("texts", results);
            response.put("timestamp", System.currentTimeMillis());
            response.put("engine", "Tesseract");

            log.info("Tesseract识别成功，识别到{}行文本", results.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Tesseract识别失败: {}", e.getMessage(), e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "OCR识别失败: " + e.getMessage());
            errorResponse.put("texts", new String[0]);
            errorResponse.put("timestamp", System.currentTimeMillis());
            errorResponse.put("engine", "Tesseract");

            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/recognize")
    @ApiOperation(value = "智能OCR识别", notes = "自动选择最佳OCR引擎进行文字识别")
    public ResponseEntity<Map<String, Object>> smartOCRRecognize(
            @ApiParam(value = "图片文件", required = true)
            @RequestParam("image") MultipartFile image,
            @ApiParam(value = "首选引擎", allowableValues = "paddle,tesseract")
            @RequestParam(value = "engine", defaultValue = "tesseract") String preferredEngine) {

        log.info("收到智能OCR识别请求，首选引擎: {}, 文件名: {}",
                preferredEngine, image.getOriginalFilename());

        // 智能选择：优先使用 PaddleOCR（更高的中文识别准确率）
        if ("tesseract".equalsIgnoreCase(preferredEngine)) {
            log.info("用户指定使用Tesseract进行OCR识别");
            ResponseEntity<Map<String, Object>> tesseractResult = tesseractOCRRecognize(image);
            if (tesseractResult.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = tesseractResult.getBody();
                if (body != null && (Boolean) body.get("success")) {
                    return tesseractResult;
                }
            }
            log.warn("Tesseract识别失败或结果不理想，尝试PaddleOCR");
            return paddleOCRRecognize(image);
        } else {
            log.info("使用PaddleOCR进行高精度中文识别");
            ResponseEntity<Map<String, Object>> paddleResult = paddleOCRRecognize(image);
            if (paddleResult.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = paddleResult.getBody();
                if (body != null && (Boolean) body.get("success")) {
                    return paddleResult;
                }
            }
            log.warn("PaddleOCR识别失败，尝试Tesseract作为备用方案");
            return tesseractOCRRecognize(image);
        }
    }

    /**
     * 身份证识别快捷接口
     */
    @PostMapping("/idcard")
    @ApiOperation(value = "身份证OCR识别", notes = "专门用于身份证识别的快捷接口")
    public ResponseEntity<Map<String, Object>> idCardRecognize(
            @ApiParam(value = "身份证图片文件", required = true)
            @RequestParam("image") MultipartFile image) {

        log.info("收到身份证识别请求，文件名: {}, 大小: {} bytes",
                image.getOriginalFilename(), image.getSize());

        try {
            // 执行身份证识别
            IdCardInfo idCardInfo = idCardOCRService.recognizeIdCard(image);

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", idCardInfo.isSuccess());
            response.put("message", idCardInfo.getMessage());
            response.put("timestamp", idCardInfo.getTimestamp());
            response.put("engine", "PaddleOCR-IdCard");
            response.put("card_type", idCardInfo.getCardType());
            response.put("confidence", idCardInfo.getConfidence());

            // 添加结构化信息
            Map<String, Object> structuredInfo = new HashMap<>();
            if (idCardInfo.getName() != null) structuredInfo.put("name", idCardInfo.getName());
            if (idCardInfo.getGender() != null) structuredInfo.put("gender", idCardInfo.getGender());
            if (idCardInfo.getNation() != null) structuredInfo.put("nation", idCardInfo.getNation());
            if (idCardInfo.getBirthDate() != null) structuredInfo.put("birth_date", idCardInfo.getBirthDate());
            if (idCardInfo.getAddress() != null) structuredInfo.put("address", idCardInfo.getAddress());
            if (idCardInfo.getIdNumber() != null) {
                structuredInfo.put("id_number", idCardInfo.getIdNumber());
                structuredInfo.put("id_valid", idCardInfo.isValidIdNumber());
            }
            if (idCardInfo.getIssuingAuthority() != null) structuredInfo.put("issuing_authority", idCardInfo.getIssuingAuthority());
            if (idCardInfo.getValidPeriod() != null) structuredInfo.put("valid_period", idCardInfo.getValidPeriod());

            response.put("structured_info", structuredInfo);
            response.put("raw_texts", idCardInfo.getRawTexts());

            if (idCardInfo.isSuccess()) {
                log.info("身份证识别成功，类型: {}, 置信度: {:.2f}%",
                        idCardInfo.getCardType(), idCardInfo.getConfidence());
                return ResponseEntity.ok(response);
            } else {
                log.warn("身份证识别失败: {}", idCardInfo.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            log.error("身份证识别异常: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "身份证识别服务异常: " + e.getMessage());
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    @GetMapping("/status")
    @ApiOperation(value = "OCR服务状态", notes = "获取OCR服务的运行状态")
    public ResponseEntity<Map<String, Object>> getOCRStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("timestamp", System.currentTimeMillis());
        status.put("paddleOCR", "可用");
        status.put("tesseract", "可用");
        status.put("idCardOCR", "可用");
        status.put("version", "1.0.0");

        return ResponseEntity.ok(status);
    }
}