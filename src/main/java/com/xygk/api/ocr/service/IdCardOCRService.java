package com.xygk.api.ocr.service;

import com.xygk.api.ocr.entity.IdCardInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * 身份证专用OCR识别服务
 * 基于PaddleOCR，专门优化身份证识别准确性
 */
@Slf4j
@Service
public class IdCardOCRService {
    
    @Value("${idcard.ocr.enable:true}")
    private boolean paddleOcrEnabled;
    
    @Value("${paddle.ocr.python-path:python}")
    private String pythonPath;
    
    @Value("${idcard.ocr.script-path:scripts/idcard_ocr.py}")
    private String idCardScriptPath;
    
    @Value("${idcard.ocr.timeout:60}")
    private int timeoutSeconds;

    @Value("${idcard.ocr.use-cloud:false}")
    private boolean useCloudOCR;

    @Value("${idcard.ocr.cloud-provider:baidu}")
    private String cloudProvider;
    
    @Value("${idcard.ocr.enable-face-detection:true}")
    private boolean enableFaceDetection;
    
    @Value("${idcard.ocr.enable-emblem-detection:true}")
    private boolean enableEmblemDetection;
    
    private boolean pythonAvailable = false;
    
    // 身份证信息提取的正则表达式
    private static final Pattern NAME_PATTERN = Pattern.compile("姓名[\\s:：]*([\\u4e00-\\u9fa5·]{2,10})");
    private static final Pattern GENDER_PATTERN = Pattern.compile("性别[\\s:：]*([男女])");
    private static final Pattern NATION_PATTERN = Pattern.compile("民族[\\s:：]*([\\u4e00-\\u9fa5]{2,10})");
    private static final Pattern BIRTH_PATTERN = Pattern.compile("出生[\\s:：]*([0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日)");
    private static final Pattern ADDRESS_PATTERN = Pattern.compile("住址[\\s:：]*([\\u4e00-\\u9fa5\\d\\s]+)");
    private static final Pattern ID_NUMBER_PATTERN = Pattern.compile("([1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx])");
    private static final Pattern AUTHORITY_PATTERN = Pattern.compile("签发机关[\\s:：]*([\\u4e00-\\u9fa5\\s]+)");
    private static final Pattern VALID_PERIOD_PATTERN = Pattern.compile("有效期限[\\s:：]*([0-9]{4}\\.[0-9]{2}\\.[0-9]{2}[-—][0-9]{4}\\.[0-9]{2}\\.[0-9]{2}|长期)");
    
    @PostConstruct
    public void init() {
        if (!paddleOcrEnabled) {
            log.info("身份证OCR已禁用");
            return;
        }
        
        // 检查Python环境
        checkPythonEnvironment();
        
        // 检查身份证OCR脚本
        checkIdCardScript();
        
        log.info("身份证OCR服务初始化完成，Python可用: {}", pythonAvailable);
    }
    
    private void checkPythonEnvironment() {
        try {
            // 1. 检查Python版本
            ProcessBuilder pb = new ProcessBuilder(pythonPath, "--version");
            pb.redirectErrorStream(true); // 重定向错误流
            Process process = pb.start();

            // 消费输出流，避免进程阻塞
            consumeProcessOutput(process);

            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                log.warn("身份证OCR：Python环境检查超时");
                return;
            }

            if (process.exitValue() != 0) {
                log.warn("身份证OCR：Python环境检查失败，退出码: {}", process.exitValue());
                return;
            }

            // 2. 检查PaddleOCR是否已安装
            pb = new ProcessBuilder(pythonPath, "-c", "import paddleocr; print('PaddleOCR available')");
            pb.redirectErrorStream(true); // 重定向错误流
            process = pb.start();

            // 消费输出流
            consumeProcessOutput(process);

            finished = process.waitFor(10, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                log.warn("身份证OCR：PaddleOCR检查超时");
                return;
            }

            if (process.exitValue() == 0) {
                pythonAvailable = true;
                log.info("身份证OCR：Python环境和PaddleOCR检查成功");
            } else {
                log.warn("身份证OCR：PaddleOCR包未安装或不可用，退出码: {}", process.exitValue());
                log.info("请安装PaddleOCR：pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple");
            }
        } catch (Exception e) {
            log.warn("身份证OCR：Python环境检查异常: {}", e.getMessage());
        }
    }
    
    private void checkIdCardScript() {
        File scriptFile = new File(idCardScriptPath);
        if (!scriptFile.exists()) {
            log.warn("身份证OCR脚本不存在: {}", scriptFile.getAbsolutePath());
            log.info("将使用通用OCR脚本");
        } else {
            log.info("找到身份证OCR脚本: {}", scriptFile.getAbsolutePath());
        }
    }
    
    /**
     * 识别身份证信息
     */
    public IdCardInfo recognizeIdCard(MultipartFile imageFile) {
        if (!paddleOcrEnabled) {
            return IdCardInfo.error("unknown", "身份证OCR功能已禁用");
        }
        
        if (!pythonAvailable) {
            return createEnvironmentErrorResult();
        }
        
        try {
            log.info("开始身份证OCR识别，文件大小: {} bytes", imageFile.getSize());
            log.info("原始文件名: {}, Content-Type: {}", imageFile.getOriginalFilename(), imageFile.getContentType());

            // 预处理图像
            File processedImageFile = preprocessIdCardImage(imageFile);
            log.info("图像预处理完成，临时文件: {}, 大小: {} bytes",
                    processedImageFile.getAbsolutePath(), processedImageFile.length());

            // 根据配置选择OCR方式
            IdCardInfo idCardInfo;
            if (useCloudOCR) {
                log.info("使用云端OCR服务: {}", cloudProvider);
                idCardInfo = performCloudOCR(processedImageFile);
            } else {
                log.info("使用本地PaddleOCR");
                idCardInfo = performIdCardOCRWithJson(processedImageFile);
            }

            // 设置引擎信息
            idCardInfo.setOcrEngine("PaddleOCR");
            idCardInfo.setTimestamp(System.currentTimeMillis());
            
            // 清理临时文件
            if (processedImageFile != null && processedImageFile.exists()) {
                try {
                    Files.delete(processedImageFile.toPath());
                    log.debug("临时文件删除成功: {}", processedImageFile.getAbsolutePath());
                } catch (IOException e) {
                    log.debug("删除临时文件失败: {}, 错误: {}", processedImageFile.getAbsolutePath(), e.getMessage());
                    // 尝试在JVM退出时删除
                    processedImageFile.deleteOnExit();
                }
            }
            
            log.info("身份证识别完成，类型: {}, 成功: {}", idCardInfo.getCardType(), idCardInfo.isSuccess());
            return idCardInfo;
            
        } catch (Exception e) {
            log.error("身份证识别失败: {}", e.getMessage(), e);
            return IdCardInfo.error("unknown", "身份证识别失败: " + e.getMessage());
        }
    }
    
    /**
     * 优化的身份证图像预处理 - 减少处理步骤以提高速度
     */
    private File preprocessIdCardImage(MultipartFile imageFile) throws IOException {
        File tempFile = null;
        long startTime = System.currentTimeMillis();

        try {
            // 获取原始文件扩展名
            String originalFilename = imageFile.getOriginalFilename();
            String extension = "jpg"; // 默认扩展名
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
            }

            // 创建临时文件，保持原始格式以减少转换开销
            tempFile = File.createTempFile("idcard_ocr_", "." + extension);

            // 检查图像是否需要处理
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageFile.getBytes()));
            if (originalImage == null) {
                throw new IOException("无法读取图片文件，可能是不支持的图片格式");
            }

            // 简化的图像处理 - 只在必要时进行
            BufferedImage processedImage = originalImage;

            // 检查图像尺寸，只在过大时进行缩放
            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            if (width > 2000 || height > 1500) {
                log.debug("图像尺寸过大 ({}x{})，进行缩放", width, height);
                processedImage = resizeImage(originalImage, 1600, 1200);
            } else {
                log.debug("图像尺寸合适 ({}x{})，跳过处理", width, height);
            }

            // 保存处理后的图像，使用JPEG格式以减少文件大小和处理时间
            String outputFormat = "jpg".equals(extension) ? "JPEG" : "PNG";
            boolean writeSuccess = ImageIO.write(processedImage, outputFormat, tempFile);
            if (!writeSuccess) {
                throw new IOException("无法保存处理后的图像文件");
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.debug("身份证图像预处理完成: {}，耗时: {}ms", tempFile.getAbsolutePath(), processingTime);
            return tempFile;

        } catch (IOException e) {
            // 如果处理失败，清理临时文件
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                } catch (IOException deleteException) {
                    log.debug("清理失败的临时文件时发生异常: {}", deleteException.getMessage());
                }
            }
            throw e;
        }
    }

    /**
     * 云端OCR识别方法
     */
    private IdCardInfo performCloudOCR(File imageFile) throws IOException {
        try {
            // 构建云端OCR命令
            List<String> command = new ArrayList<>();
            command.add(pythonPath);
            command.add("scripts/cloud_ocr_api.py");
            command.add(imageFile.getAbsolutePath());
            command.add("--provider");
            command.add(cloudProvider);

            // 设置较短的超时时间（云端API通常很快）
            int cloudTimeout = Math.min(timeoutSeconds, 10);

            log.debug("执行云端OCR命令: {}", String.join(" ", command));

            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.directory(new File("."));
            processBuilder.redirectErrorStream(true);

            Process process = processBuilder.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待进程完成
            boolean finished = process.waitFor(cloudTimeout, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("云端OCR识别超时");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                throw new RuntimeException("云端OCR识别失败，退出码: " + exitCode);
            }

            // 解析JSON结果
            String jsonResult = output.toString().trim();
            log.debug("云端OCR识别结果: {}", jsonResult);

            return parseCloudOCRResult(jsonResult);

        } catch (Exception e) {
            log.error("云端OCR识别失败", e);
            throw new IOException("云端OCR识别失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析云端OCR结果
     */
    private IdCardInfo parseCloudOCRResult(String jsonResult) throws IOException {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(jsonResult);

            if (!rootNode.get("success").asBoolean()) {
                throw new IOException("云端OCR识别失败: " + rootNode.get("message").asText());
            }

            JsonNode structuredInfo = rootNode.get("structured_info");
            if (structuredInfo == null) {
                throw new IOException("云端OCR结果中缺少结构化信息");
            }

            IdCardInfo idCardInfo = new IdCardInfo();
            idCardInfo.setName(getJsonText(structuredInfo, "name"));
            idCardInfo.setGender(getJsonText(structuredInfo, "gender"));
            idCardInfo.setNation(getJsonText(structuredInfo, "nation"));
            idCardInfo.setBirthDate(getJsonText(structuredInfo, "birth_date"));
            idCardInfo.setAddress(getJsonText(structuredInfo, "address"));
            idCardInfo.setIdNumber(getJsonText(structuredInfo, "id_number"));
            idCardInfo.setIssuingAuthority(getJsonText(structuredInfo, "issuing_authority"));
            idCardInfo.setValidPeriod(getJsonText(structuredInfo, "valid_period"));

            // 设置置信度（云端API通常不提供详细置信度）
            idCardInfo.setConfidence(0.95);

            return idCardInfo;

        } catch (Exception e) {
            log.error("解析云端OCR结果失败", e);
            throw new IOException("解析云端OCR结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 简化的图像缩放方法
     */
    private BufferedImage resizeImage(BufferedImage originalImage, int maxWidth, int maxHeight) {
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();

        // 计算缩放比例
        double scaleX = (double) maxWidth / width;
        double scaleY = (double) maxHeight / height;
        double scale = Math.min(scaleX, scaleY);

        int newWidth = (int) (width * scale);
        int newHeight = (int) (height * scale);

        // 创建缩放后的图像
        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resizedImage.createGraphics();

        // 设置高质量缩放
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        return resizedImage;
    }

    /**
     * 增强身份证图像质量（保留但简化）
     */
    private BufferedImage enhanceIdCardImage(BufferedImage originalImage) {
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();
        
        // 创建增强后的图像
        BufferedImage enhancedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = enhancedImage.createGraphics();
        
        // 设置高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // 绘制原始图像
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose();
        
        return enhancedImage;
    }
    
    /**
     * 执行身份证OCR识别
     */
    private List<String> performIdCardOCR(File imageFile) throws IOException, InterruptedException {
        List<String> command;
        
        // 检查是否存在专用的身份证OCR脚本
        File scriptFile = new File(idCardScriptPath);
        if (scriptFile.exists()) {
            // 使用专用身份证OCR脚本，输出JSON格式以获取结构化信息
            command = Arrays.asList(
                pythonPath,
                scriptFile.getAbsolutePath(),
                imageFile.getAbsolutePath(),
                "--output-format", "json",
                "--card-type", "auto"
            );
            log.debug("使用专用身份证OCR脚本");
        } else {
            // 使用身份证OCR脚本，输出JSON格式
            command = Arrays.asList(pythonPath, idCardScriptPath, imageFile.getAbsolutePath(), "--output-format", "json");
            log.debug("使用身份证OCR脚本");
        }
        
        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectErrorStream(true); // 重定向错误流到标准输出
        Process process = pb.start();

        // 读取输出并过滤错误信息
        List<String> results = new ArrayList<>();
        List<String> errorLines = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream(), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    // 过滤掉下载信息、错误和警告信息
                    if (line.startsWith("download") ||
                        line.startsWith("Downloading") ||
                        line.startsWith("ERROR") ||
                        line.startsWith("WARNING") ||
                        line.contains("UserWarning") ||
                        line.contains("FutureWarning") ||
                        line.contains("DeprecationWarning") ||
                        line.contains("PaddleOCR 初始化成功")) {
                        // 记录但不输出到控制台
                        log.debug("过滤的输出: {}", line);
                        if (line.startsWith("ERROR")) {
                            errorLines.add(line);
                        }
                    } else {
                        results.add(line);
                    }
                }
            }
        }

        boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);
        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("身份证OCR识别超时");
        }

        if (process.exitValue() != 0) {
            String errorMsg = "身份证OCR识别失败，退出码: " + process.exitValue();
            if (!errorLines.isEmpty()) {
                errorMsg += "，错误信息: " + String.join("; ", errorLines);
            }
            throw new RuntimeException(errorMsg);
        }
        
        log.info("身份证OCR识别成功，识别到{}行文本", results.size());
        return results;
    }

    /**
     * 创建环境错误结果
     */
    private IdCardInfo createEnvironmentErrorResult() {
        IdCardInfo errorInfo = IdCardInfo.error("unknown", "身份证OCR环境配置错误");

        List<String> errorTexts = new ArrayList<>();
        errorTexts.add("❌ 身份证OCR环境配置错误");
        errorTexts.add("");
        errorTexts.add("🔧 快速解决方案：");
        errorTexts.add("请在命令行中执行以下命令安装PaddleOCR：");
        errorTexts.add("");
        errorTexts.add("pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple");
        errorTexts.add("");
        errorTexts.add("或者运行项目提供的安装脚本：");
        errorTexts.add("scripts/quick_install.bat");
        errorTexts.add("");
        errorTexts.add("安装完成后重启应用即可使用身份证识别功能");
        errorTexts.add("");
        errorTexts.add("📝 环境检查结果：");
        errorTexts.add("- Python环境: " + (checkPythonOnly() ? "✓ 可用" : "✗ 不可用"));
        errorTexts.add("- PaddleOCR包: ✗ 未安装");

        errorInfo.setRawTexts(errorTexts);
        errorInfo.setOcrEngine("环境检查");

        return errorInfo;
    }

    /**
     * 消费进程输出，避免进程阻塞和控制台错误信息
     */
    private void consumeProcessOutput(Process process) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream(), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 静默消费输出，避免控制台显示
                log.debug("进程输出: {}", line);
            }
        } catch (IOException e) {
            log.debug("消费进程输出时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 仅检查Python环境
     */
    private boolean checkPythonOnly() {
        try {
            ProcessBuilder pb = new ProcessBuilder(pythonPath, "--version");
            pb.redirectErrorStream(true); // 重定向错误流
            Process process = pb.start();

            // 消费输出流
            consumeProcessOutput(process);

            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                return false;
            }
            return process.exitValue() == 0;
        } catch (Exception e) {
            log.debug("Python环境检查异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 解析身份证信息
     */
    private IdCardInfo parseIdCardInfo(List<String> rawTexts) {
        if (rawTexts == null || rawTexts.isEmpty()) {
            return IdCardInfo.error("unknown", "未识别到任何文字内容");
        }

        // 合并所有文本用于分析
        String allText = String.join(" ", rawTexts);
        log.info("身份证原始文本: {}", allText);
        log.info("识别到的文本行数: {}", rawTexts.size());

        // 输出前10行文本用于调试
        for (int i = 0; i < Math.min(10, rawTexts.size()); i++) {
            log.info("文本行 {}: {}", i + 1, rawTexts.get(i));
        }

        // 判断身份证类型
        String cardType = detectCardType(allText);

        IdCardInfo idCardInfo = IdCardInfo.success(cardType, "身份证信息识别成功");

        if ("front".equals(cardType)) {
            // 解析正面信息
            parseFrontInfo(idCardInfo, rawTexts, allText);
        } else if ("back".equals(cardType)) {
            // 解析背面信息
            parseBackInfo(idCardInfo, rawTexts, allText);
        } else {
            // 尝试解析所有可能的信息
            parseFrontInfo(idCardInfo, rawTexts, allText);
            parseBackInfo(idCardInfo, rawTexts, allText);
        }

        // 验证解析结果
        validateIdCardInfo(idCardInfo);

        return idCardInfo;
    }

    /**
     * 检测身份证类型
     */
    private String detectCardType(String allText) {
        // 检查是否包含正面特征
        boolean hasFrontFeatures = allText.contains("姓名") ||
                                  allText.contains("性别") ||
                                  allText.contains("民族") ||
                                  allText.contains("出生") ||
                                  allText.contains("住址");

        // 检查是否包含背面特征
        boolean hasBackFeatures = allText.contains("签发机关") ||
                                 allText.contains("有效期限") ||
                                 allText.contains("公安局") ||
                                 allText.contains("派出所");

        log.info("身份证类型检测 - 正面特征: {}, 背面特征: {}", hasFrontFeatures, hasBackFeatures);
        log.info("检测关键词 - 姓名: {}, 性别: {}, 民族: {}, 出生: {}, 住址: {}",
                allText.contains("姓名"), allText.contains("性别"), allText.contains("民族"),
                allText.contains("出生"), allText.contains("住址"));
        log.info("检测关键词 - 签发机关: {}, 有效期限: {}, 公安局: {}, 派出所: {}",
                allText.contains("签发机关"), allText.contains("有效期限"),
                allText.contains("公安局"), allText.contains("派出所"));

        if (hasFrontFeatures && !hasBackFeatures) {
            return "front";
        } else if (hasBackFeatures && !hasFrontFeatures) {
            return "back";
        } else if (hasFrontFeatures && hasBackFeatures) {
            return "both";
        } else {
            return "unknown";
        }
    }

    /**
     * 解析正面信息
     */
    private void parseFrontInfo(IdCardInfo idCardInfo, List<String> rawTexts, String allText) {
        // 提取姓名
        Matcher nameMatcher = NAME_PATTERN.matcher(allText);
        if (nameMatcher.find()) {
            idCardInfo.setName(nameMatcher.group(1).trim());
        }

        // 提取性别
        Matcher genderMatcher = GENDER_PATTERN.matcher(allText);
        if (genderMatcher.find()) {
            idCardInfo.setGender(genderMatcher.group(1).trim());
        }

        // 提取民族
        Matcher nationMatcher = NATION_PATTERN.matcher(allText);
        if (nationMatcher.find()) {
            idCardInfo.setNation(nationMatcher.group(1).trim());
        }

        // 提取出生日期
        Matcher birthMatcher = BIRTH_PATTERN.matcher(allText);
        if (birthMatcher.find()) {
            idCardInfo.setBirthDate(birthMatcher.group(1).trim());
        }

        // 提取住址
        Matcher addressMatcher = ADDRESS_PATTERN.matcher(allText);
        if (addressMatcher.find()) {
            idCardInfo.setAddress(addressMatcher.group(1).trim());
        }

        // 提取身份证号码
        Matcher idMatcher = ID_NUMBER_PATTERN.matcher(allText);
        if (idMatcher.find()) {
            idCardInfo.setIdNumber(idMatcher.group(1).trim());
        }

        // 如果正则匹配失败，尝试基于位置的解析
        if (idCardInfo.getName() == null || idCardInfo.getIdNumber() == null) {
            parseByPosition(idCardInfo, rawTexts);
        }
    }

    /**
     * 解析背面信息
     */
    private void parseBackInfo(IdCardInfo idCardInfo, List<String> rawTexts, String allText) {
        // 提取签发机关
        Matcher authorityMatcher = AUTHORITY_PATTERN.matcher(allText);
        if (authorityMatcher.find()) {
            idCardInfo.setIssuingAuthority(authorityMatcher.group(1).trim());
        }

        // 提取有效期限
        Matcher validMatcher = VALID_PERIOD_PATTERN.matcher(allText);
        if (validMatcher.find()) {
            String validPeriod = validMatcher.group(1).trim();
            idCardInfo.setValidPeriod(validPeriod);

            // 解析有效期开始和结束日期
            if (!validPeriod.equals("长期")) {
                String[] dates = validPeriod.split("[-—]");
                if (dates.length == 2) {
                    idCardInfo.setValidFrom(dates[0].trim());
                    idCardInfo.setValidTo(dates[1].trim());
                }
            } else {
                idCardInfo.setValidTo("长期");
            }
        }
    }

    /**
     * 基于位置的解析（当正则匹配失败时）
     */
    private void parseByPosition(IdCardInfo idCardInfo, List<String> rawTexts) {
        for (int i = 0; i < rawTexts.size(); i++) {
            String text = rawTexts.get(i);

            // 查找身份证号码（18位数字）
            if (idCardInfo.getIdNumber() == null) {
                Matcher idMatcher = Pattern.compile("([1-9]\\d{17}|[1-9]\\d{16}[Xx])").matcher(text);
                if (idMatcher.find()) {
                    idCardInfo.setIdNumber(idMatcher.group(1));
                }
            }

            // 查找姓名（通常在前几行，2-4个汉字）
            if (idCardInfo.getName() == null && i < 5) {
                Matcher nameMatcher = Pattern.compile("([\\u4e00-\\u9fa5·]{2,4})").matcher(text);
                if (nameMatcher.find() && !text.contains("姓名") && !text.contains("性别")) {
                    String possibleName = nameMatcher.group(1);
                    if (!possibleName.equals("男") && !possibleName.equals("女") &&
                        !possibleName.equals("汉族") && !possibleName.contains("年")) {
                        idCardInfo.setName(possibleName);
                    }
                }
            }
        }
    }

    /**
     * 验证身份证信息
     */
    private void validateIdCardInfo(IdCardInfo idCardInfo) {
        boolean hasValidInfo = false;

        // 检查是否有有效的身份证号码
        if (idCardInfo.getIdNumber() != null && idCardInfo.isValidIdNumber()) {
            hasValidInfo = true;

            // 验证性别一致性
            if (!idCardInfo.isGenderConsistent()) {
                log.warn("身份证性别信息不一致");
            }
        }

        // 检查是否有其他有效信息
        if (idCardInfo.getName() != null ||
            idCardInfo.getIssuingAuthority() != null ||
            idCardInfo.getValidPeriod() != null) {
            hasValidInfo = true;
        }

        if (!hasValidInfo) {
            idCardInfo.setSuccess(false);
            idCardInfo.setMessage("未能识别到有效的身份证信息");
        }

        // 计算置信度
        double confidence = calculateConfidence(idCardInfo);
        idCardInfo.setConfidence(confidence);
    }

    /**
     * 计算识别置信度
     */
    private double calculateConfidence(IdCardInfo idCardInfo) {
        double confidence = 0.0;

        if (idCardInfo.getName() != null) confidence += 20;
        if (idCardInfo.getIdNumber() != null && idCardInfo.isValidIdNumber()) confidence += 40;
        if (idCardInfo.getGender() != null) confidence += 10;
        if (idCardInfo.getNation() != null) confidence += 10;
        if (idCardInfo.getBirthDate() != null) confidence += 10;
        if (idCardInfo.getAddress() != null) confidence += 5;
        if (idCardInfo.getIssuingAuthority() != null) confidence += 3;
        if (idCardInfo.getValidPeriod() != null) confidence += 2;

        return Math.min(confidence, 100.0);
    }

    /**
     * 执行身份证OCR识别并返回JSON结果
     */
    private IdCardInfo performIdCardOCRWithJson(File imageFile) throws IOException, InterruptedException {
        List<String> command;

        // 检查是否存在专用的身份证OCR脚本
        File scriptFile = new File(idCardScriptPath);
        if (scriptFile.exists()) {
            // 使用专用身份证OCR脚本，输出JSON格式以获取结构化信息
            command = Arrays.asList(
                pythonPath,
                scriptFile.getAbsolutePath(),
                imageFile.getAbsolutePath(),
                "--output-format", "json",
                "--card-type", "auto"
            );
            log.debug("使用专用身份证OCR脚本");
        } else {
            // 使用身份证OCR脚本，输出JSON格式
            command = Arrays.asList(pythonPath, idCardScriptPath, imageFile.getAbsolutePath(), "--output-format", "json");
            log.debug("使用身份证OCR脚本");
        }

        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectErrorStream(true); // 重定向错误流到标准输出
        Process process = pb.start();

        // 读取输出
        StringBuilder jsonOutput = new StringBuilder();
        List<String> errorLines = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream(), "UTF-8"))) {
            String line;
            boolean jsonStarted = false;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    // 检查是否是JSON开始
                    if (line.startsWith("{")) {
                        jsonStarted = true;
                        jsonOutput.append(line);
                    } else if (jsonStarted) {
                        jsonOutput.append(line);
                    } else {
                        // 过滤掉下载信息、错误和警告信息
                        if (line.startsWith("download") ||
                            line.startsWith("Downloading") ||
                            line.startsWith("ERROR") ||
                            line.startsWith("WARNING") ||
                            line.contains("UserWarning") ||
                            line.contains("FutureWarning") ||
                            line.contains("DeprecationWarning") ||
                            line.contains("正在初始化") ||
                            line.contains("初始化成功")) {
                            // 记录但不输出到控制台
                            log.debug("过滤的输出: {}", line);
                            if (line.startsWith("ERROR")) {
                                errorLines.add(line);
                            }
                        }
                    }
                }
            }
        }

        boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);
        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("身份证OCR识别超时");
        }

        if (process.exitValue() != 0) {
            String errorMsg = "身份证OCR识别失败，退出码: " + process.exitValue();
            if (!errorLines.isEmpty()) {
                errorMsg += "，错误信息: " + String.join("; ", errorLines);
            }
            throw new RuntimeException(errorMsg);
        }

        // 解析JSON响应
        String jsonString = jsonOutput.toString();
        log.info("身份证OCR JSON响应: {}", jsonString);

        if (jsonString.isEmpty()) {
            return IdCardInfo.error("unknown", "未收到OCR识别结果");
        }

        return parseJsonResponse(jsonString);
    }

    /**
     * 解析JSON响应
     */
    private IdCardInfo parseJsonResponse(String jsonString) {
        try {
            JSONObject jsonResponse = JSON.parseObject(jsonString);

            boolean success = jsonResponse.getBooleanValue("success");
            if (!success) {
                String message = jsonResponse.getString("message");
                return IdCardInfo.error("unknown", message != null ? message : "身份证识别失败");
            }

            String cardType = jsonResponse.getString("card_type");
            if (cardType == null) {
                cardType = "unknown";
            }

            // 创建IdCardInfo对象
            IdCardInfo idCardInfo = IdCardInfo.success(cardType, "身份证信息识别成功");

            // 设置原始文本
            JSONArray textsArray = jsonResponse.getJSONArray("texts");
            if (textsArray != null) {
                List<String> rawTexts = new ArrayList<>();
                for (int i = 0; i < textsArray.size(); i++) {
                    rawTexts.add(textsArray.getString(i));
                }
                idCardInfo.setRawTexts(rawTexts);
            }

            // 设置置信度
            JSONArray confidencesArray = jsonResponse.getJSONArray("confidences");
            if (confidencesArray != null && confidencesArray.size() > 0) {
                double avgConfidence = 0.0;
                for (int i = 0; i < confidencesArray.size(); i++) {
                    avgConfidence += confidencesArray.getDoubleValue(i);
                }
                avgConfidence = avgConfidence / confidencesArray.size() * 100; // 转换为百分比
                idCardInfo.setConfidence(avgConfidence);
            }

            // 解析结构化信息
            JSONObject structuredInfo = jsonResponse.getJSONObject("structured_info");
            if (structuredInfo != null) {
                // 正面信息
                if (structuredInfo.containsKey("name")) {
                    idCardInfo.setName(structuredInfo.getString("name"));
                }
                if (structuredInfo.containsKey("gender")) {
                    idCardInfo.setGender(structuredInfo.getString("gender"));
                }
                if (structuredInfo.containsKey("nation")) {
                    idCardInfo.setNation(structuredInfo.getString("nation"));
                }
                if (structuredInfo.containsKey("birth_date")) {
                    idCardInfo.setBirthDate(structuredInfo.getString("birth_date"));
                }
                if (structuredInfo.containsKey("address")) {
                    idCardInfo.setAddress(structuredInfo.getString("address"));
                }
                if (structuredInfo.containsKey("id_number")) {
                    idCardInfo.setIdNumber(structuredInfo.getString("id_number"));
                }

                // 背面信息
                if (structuredInfo.containsKey("issuing_authority")) {
                    idCardInfo.setIssuingAuthority(structuredInfo.getString("issuing_authority"));
                }
                if (structuredInfo.containsKey("valid_period")) {
                    idCardInfo.setValidPeriod(structuredInfo.getString("valid_period"));
                }
            }

            log.info("JSON解析完成 - 姓名: {}, 身份证号: {}, 卡片类型: {}",
                    idCardInfo.getName(), idCardInfo.getIdNumber(), idCardInfo.getCardType());

            return idCardInfo;

        } catch (Exception e) {
            log.error("解析JSON响应失败: {}", e.getMessage(), e);
            log.error("原始JSON: {}", jsonString);
            return IdCardInfo.error("unknown", "解析识别结果失败: " + e.getMessage());
        }
    }

    /**
     * 从JsonNode中安全获取文本值
     */
    private String getJsonText(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode != null && !fieldNode.isNull()) {
            return fieldNode.asText();
        }
        return null;
    }
}
