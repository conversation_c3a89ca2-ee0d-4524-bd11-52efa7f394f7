package com.xygk.api.ocr;

import ai.djl.Application;
import ai.djl.ModelException;
import ai.djl.inference.Predictor;
import ai.djl.modality.cv.Image;
import ai.djl.modality.cv.ImageFactory;
import ai.djl.modality.cv.output.DetectedObjects;
import ai.djl.repository.zoo.Criteria;
import ai.djl.repository.zoo.ModelZoo;
import ai.djl.repository.zoo.ZooModel;
import ai.djl.translate.TranslateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class PaddleOCRService {

    @Value("${paddle.ocr.model-dir:models/ocr}")
    private String modelDir;

    @Value("${paddle.ocr.enable:true}")
    private boolean ocrEnabled;

    @Value("${paddle.ocr.mock-mode:false}")
    private boolean mockMode;

    private ZooModel<Image, DetectedObjects> detectionModel;
    private ZooModel<Image, String> recognitionModel;
    private final ConcurrentHashMap<String, Object> modelCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (!ocrEnabled) {
            log.info("PaddleOCR已禁用");
            return;
        }

        if (mockMode) {
            log.info("PaddleOCR运行在模拟模式");
            return;
        }

        try {
            // 确保模型目录存在
            Files.createDirectories(Paths.get(modelDir));

            // 初始化文本检测模型
            initDetectionModel();

            // 初始化文本识别模型
            initRecognitionModel();

            log.info("PaddleOCR初始化成功");
        } catch (Exception e) {
            log.error("PaddleOCR初始化失败，将使用模拟模式: {}", e.getMessage());
            this.mockMode = true;
        }
    }

    private void initDetectionModel() throws ModelException, IOException {
        try {
            // 检查本地模型文件
            String detectionModelPath = modelDir + "/ch_PP-OCRv4_det_infer";
            File detectionModelDir = new File(detectionModelPath);

            if (detectionModelDir.exists() && detectionModelDir.isDirectory()) {
                log.info("找到本地检测模型目录: {}", detectionModelDir.getAbsolutePath());

                // 尝试从本地路径加载模型
                Criteria<Image, DetectedObjects> detectionCriteria = Criteria.builder()
                        .optApplication(Application.CV.OBJECT_DETECTION)
                        .setTypes(Image.class, DetectedObjects.class)
                        .optEngine("PaddlePaddle")
                        .optModelPath(Paths.get(detectionModelPath))
                        .build();

                detectionModel = ModelZoo.loadModel(detectionCriteria);
                log.info("本地文本检测模型加载成功");
            } else {
                log.warn("本地检测模型目录不存在: {}", detectionModelPath);
                // 尝试从模型库下载
                Criteria<Image, DetectedObjects> detectionCriteria = Criteria.builder()
                        .optApplication(Application.CV.OBJECT_DETECTION)
                        .setTypes(Image.class, DetectedObjects.class)
                        .optEngine("PaddlePaddle")
                        .build();

                detectionModel = ModelZoo.loadModel(detectionCriteria);
                log.info("在线文本检测模型加载成功");
            }
        } catch (Exception e) {
            log.error("检测模型加载失败: {}", e.getMessage(), e);
            detectionModel = null;
        }
    }

    private void initRecognitionModel() throws ModelException, IOException {
        try {
            // 检查本地模型文件
            String recognitionModelPath = modelDir + "/ch_PP-OCRv4_rec_infer";
            File recognitionModelDir = new File(recognitionModelPath);

            if (recognitionModelDir.exists() && recognitionModelDir.isDirectory()) {
                log.info("找到本地识别模型目录: {}", recognitionModelDir.getAbsolutePath());

                // 尝试从本地路径加载模型
                Criteria<Image, String> recognitionCriteria = Criteria.builder()
                        .optApplication(Application.CV.IMAGE_CLASSIFICATION)
                        .setTypes(Image.class, String.class)
                        .optEngine("PaddlePaddle")
                        .optModelPath(Paths.get(recognitionModelPath))
                        .build();

                recognitionModel = ModelZoo.loadModel(recognitionCriteria);
                log.info("本地文本识别模型加载成功");
            } else {
                log.warn("本地识别模型目录不存在: {}", recognitionModelPath);
                // 尝试从模型库下载
                Criteria<Image, String> recognitionCriteria = Criteria.builder()
                        .optApplication(Application.CV.IMAGE_CLASSIFICATION)
                        .setTypes(Image.class, String.class)
                        .optEngine("PaddlePaddle")
                        .build();

                recognitionModel = ModelZoo.loadModel(recognitionCriteria);
                log.info("在线文本识别模型加载成功");
            }
        } catch (Exception e) {
            log.error("识别模型加载失败: {}", e.getMessage(), e);
            recognitionModel = null;
        }
    }

    public OCRResult recognizeText(MultipartFile imageFile) {
        if (!ocrEnabled) {
            return OCRResult.error("OCR功能已禁用");
        }

        if (mockMode) {
            return getMockResult(imageFile.getOriginalFilename());
        }

        try {
            // 验证文件
            if (imageFile.isEmpty()) {
                return OCRResult.error("上传的文件为空");
            }

            if (!isValidImageFile(imageFile)) {
                return OCRResult.error("不支持的文件格式，请上传图片文件");
            }

            // 加载图片
            Image image = loadImage(imageFile);

            // 执行OCR识别
            List<String> textResults = performOCR(image);

            log.info("OCR识别完成，识别到{}行文本", textResults.size());
            return OCRResult.success(textResults);

        } catch (Exception e) {
            log.error("OCR识别失败: {}", e.getMessage(), e);
            return OCRResult.error("OCR识别失败: " + e.getMessage());
        }
    }

    private boolean isValidImageFile(MultipartFile file) {
        String contentType = file.getContentType();
        if (contentType == null) {
            return false;
        }

        return contentType.startsWith("image/") &&
               (contentType.contains("jpeg") ||
                contentType.contains("jpg") ||
                contentType.contains("png") ||
                contentType.contains("bmp") ||
                contentType.contains("gif"));
    }

    private Image loadImage(MultipartFile imageFile) throws IOException {
        try (InputStream inputStream = imageFile.getInputStream()) {
            return ImageFactory.getInstance().fromInputStream(inputStream);
        }
    }

    private List<String> performOCR(Image image) throws TranslateException {
        List<String> results = new ArrayList<>();

        try {
            if (detectionModel != null && recognitionModel != null) {
                // 使用完整的PaddleOCR流程
                results = performFullOCR(image);
            } else {
                // 使用简化的OCR流程
                results = performSimpleOCR(image);
            }
        } catch (Exception e) {
            log.warn("PaddleOCR识别失败，使用备用方案: {}", e.getMessage());
            results = performFallbackOCR(image);
        }

        // 确保至少有一个结果
        if (results.isEmpty()) {
            results.add("PaddleOCR识别完成，但未检测到文本内容");
        }

        return results;
    }

    private List<String> performFullOCR(Image image) throws TranslateException {
        List<String> results = new ArrayList<>();

        // 1. 文本检测
        try (Predictor<Image, DetectedObjects> detector = detectionModel.newPredictor()) {
            DetectedObjects detectedObjects = detector.predict(image);

            // 2. 对每个检测到的文本区域进行识别
            try (Predictor<Image, String> recognizer = recognitionModel.newPredictor()) {
                // 尝试不同的API方法来获取检测结果
                try {
                    // 方法1: 尝试使用items()方法
                    java.util.List<DetectedObjects.DetectedObject> items = detectedObjects.items();
                    for (DetectedObjects.DetectedObject obj : items) {
                        processDetectedObject(obj, image, recognizer, results);
                    }
                } catch (Exception e1) {
                    try {
                        // 方法2: 尝试使用getItems()方法
                        @SuppressWarnings("unchecked")
                        java.util.List<DetectedObjects.DetectedObject> items =
                            (java.util.List<DetectedObjects.DetectedObject>) detectedObjects.getClass()
                                .getMethod("getItems").invoke(detectedObjects);
                        for (DetectedObjects.DetectedObject obj : items) {
                            processDetectedObject(obj, image, recognizer, results);
                        }
                    } catch (Exception e2) {
                        log.warn("无法获取检测结果，跳过文本识别: {}", e2.getMessage());
                    }
                }
            }
        }

        return results;
    }

    private void processDetectedObject(DetectedObjects.DetectedObject obj, Image image,
                                     Predictor<Image, String> recognizer, List<String> results) {
        try {
            // 获取边界框信息
            ai.djl.modality.cv.output.BoundingBox bbox = obj.getBoundingBox();
            ai.djl.modality.cv.output.Rectangle rect = bbox.getBounds();

            // 裁剪文本区域
            Image textRegion = image.getSubImage(
                (int) rect.getX(),
                (int) rect.getY(),
                (int) rect.getWidth(),
                (int) rect.getHeight()
            );

            // 识别文本
            String text = recognizer.predict(textRegion);
            if (text != null && !text.trim().isEmpty()) {
                results.add(text.trim());
            }
        } catch (Exception e) {
            log.warn("处理检测对象时出错: {}", e.getMessage());
        }
    }

    private List<String> performSimpleOCR(Image image) {
        List<String> results = new ArrayList<>();

        try {
            // 如果有识别模型，尝试直接识别整个图片
            if (recognitionModel != null) {
                try (Predictor<Image, String> recognizer = recognitionModel.newPredictor()) {
                    String text = recognizer.predict(image);
                    if (text != null && !text.trim().isEmpty()) {
                        // 按行分割文本
                        String[] lines = text.split("\n");
                        for (String line : lines) {
                            String trimmedLine = line.trim();
                            if (!trimmedLine.isEmpty()) {
                                results.add(trimmedLine);
                            }
                        }
                        log.info("简化OCR模式识别成功，识别到{}行文本", results.size());
                    } else {
                        results.add("PaddleOCR未识别到文字内容");
                        log.warn("简化OCR模式未识别到文字");
                    }
                }
            } else {
                // 如果没有模型，返回提示信息
                results.add("PaddleOCR模型未加载，请检查模型文件");
                log.warn("PaddleOCR模型未加载");
            }
        } catch (Exception e) {
            log.error("简化OCR识别失败: {}", e.getMessage(), e);
            results.add("PaddleOCR识别失败: " + e.getMessage());
        }

        return results;
    }

    private List<String> performFallbackOCR(Image image) {
        List<String> results = new ArrayList<>();

        try {
            log.info("使用备用OCR方案");

            // 尝试基本的图像处理和文本提取
            // 这里可以集成其他OCR库或简单的文本检测算法

            // 检查图像基本信息
            int width = (int) image.getWidth();
            int height = (int) image.getHeight();

            log.debug("图像尺寸: {}x{}", width, height);

            if (width > 0 && height > 0) {
                results.add("PaddleOCR备用模式 - 图像已处理");
                results.add("图像尺寸: " + width + "x" + height);
                results.add("建议: 检查模型文件是否正确加载");
            } else {
                results.add("图像处理失败 - 无效的图像数据");
            }

        } catch (Exception e) {
            log.error("备用OCR方案也失败: {}", e.getMessage());
            results.add("所有OCR方案都失败，请检查配置");
        }

        return results;
    }

    private OCRResult getMockResult(String filename) {
        List<String> mockTexts = new ArrayList<>();
        mockTexts.add("这是模拟的OCR识别结果");
        mockTexts.add("文件名: " + (filename != null ? filename : "未知"));
        mockTexts.add("当前时间: " + java.time.LocalDateTime.now());
        mockTexts.add("PaddleOCR模拟模式");

        return OCRResult.success(mockTexts);
    }

    @PreDestroy
    public void cleanup() {
        try {
            if (detectionModel != null) {
                detectionModel.close();
                log.info("文本检测模型已关闭");
            }
            if (recognitionModel != null) {
                recognitionModel.close();
                log.info("文本识别模型已关闭");
            }
        } catch (Exception e) {
            log.error("清理OCR资源时出错: {}", e.getMessage());
        }
    }

    // OCR结果封装类
    public static class OCRResult {
        private boolean success;
        private String message;
        private List<String> texts;
        private long timestamp;

        private OCRResult(boolean success, String message, List<String> texts) {
            this.success = success;
            this.message = message;
            this.texts = texts;
            this.timestamp = System.currentTimeMillis();
        }

        public static OCRResult success(List<String> texts) {
            return new OCRResult(true, "识别成功", texts);
        }

        public static OCRResult error(String message) {
            return new OCRResult(false, message, new ArrayList<>());
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public List<String> getTexts() { return texts; }
        public long getTimestamp() { return timestamp; }
    }
}