package com.xygk.api.ocr;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 简化的PaddleOCR服务
 * 使用命令行调用Python PaddleOCR或提供模拟识别
 */
@Slf4j
@Service
public class SimplePaddleOCRService {
    
    @Value("${paddle.ocr.enable:true}")
    private boolean paddleOcrEnabled;
    
    @Value("${paddle.ocr.mock-mode:false}")
    private boolean mockMode;
    
    @Value("${paddle.ocr.python-path:python}")
    private String pythonPath;
    
    @Value("${paddle.ocr.script-path:scripts/paddle_ocr.py}")
    private String scriptPath;
    
    private boolean pythonAvailable = false;
    
    @PostConstruct
    public void init() {
        if (!paddleOcrEnabled) {
            log.info("SimplePaddleOCR已禁用");
            return;
        }
        
        if (mockMode) {
            log.info("SimplePaddleOCR运行在模拟模式");
            return;
        }
        
        // 检查Python环境
        checkPythonEnvironment();
        
        log.info("SimplePaddleOCR初始化完成，Python可用: {}", pythonAvailable);
    }
    
    private void checkPythonEnvironment() {
        try {
            // 1. 检查Python版本
            ProcessBuilder pb = new ProcessBuilder(pythonPath, "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);

            if (!finished || process.exitValue() != 0) {
                log.warn("Python环境检查失败：Python不可用");
                return;
            }

            // 2. 检查PaddleOCR是否已安装
            pb = new ProcessBuilder(pythonPath, "-c", "import paddleocr; print('PaddleOCR available')");
            process = pb.start();
            finished = process.waitFor(10, TimeUnit.SECONDS);

            if (finished && process.exitValue() == 0) {
                pythonAvailable = true;
                log.info("Python环境和PaddleOCR检查成功");
            } else {
                log.warn("PaddleOCR包未安装或不可用");
                log.info("请手动安装：pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple");
            }
        } catch (Exception e) {
            log.warn("Python环境检查异常: {}", e.getMessage());
        }
    }

    private boolean checkPythonOnly() {
        try {
            ProcessBuilder pb = new ProcessBuilder(pythonPath, "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            return finished && process.exitValue() == 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    public OCRResult recognizeText(MultipartFile imageFile) {
        if (!paddleOcrEnabled) {
            return OCRResult.error("SimplePaddleOCR已禁用");
        }

        if (mockMode) {
            return getMockResult(imageFile.getOriginalFilename());
        }

        if (!pythonAvailable) {
            return getEnvironmentErrorResult(imageFile);
        }

        return performPythonOCR(imageFile);
    }
    
    private OCRResult performPythonOCR(MultipartFile imageFile) {
        File tempFile = null;
        try {
            // 创建临时文件
            tempFile = File.createTempFile("paddle_ocr_", ".png");

            // 转换并保存图片
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageFile.getBytes()));
            if (image == null) {
                return OCRResult.error("无法读取图片文件");
            }

            // 转换为RGB格式并保存为PNG，提高识别准确性
            BufferedImage rgbImage = new BufferedImage(
                image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = rgbImage.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.drawImage(image, 0, 0, null);
            g2d.dispose();

            ImageIO.write(rgbImage, "PNG", tempFile);

            // 检查是否存在独立的Python脚本
            File scriptFile = new File("scripts/paddle_ocr.py");
            List<String> command;

            if (scriptFile.exists()) {
                // 使用独立的优化脚本
                command = Arrays.asList(
                    pythonPath,
                    scriptFile.getAbsolutePath(),
                    tempFile.getAbsolutePath(),
                    "--output-format", "list"
                );
                log.debug("使用优化的PaddleOCR脚本: {}", scriptFile.getAbsolutePath());
            } else {
                // 使用内联命令（优化版本）
                command = Arrays.asList(
                    pythonPath, "-c",
                    "from paddleocr import PaddleOCR; " +
                    "import sys, re; " +
                    "ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False, " +
                    "det_db_thresh=0.3, det_db_box_thresh=0.5, det_db_unclip_ratio=1.6); " +
                    "result = ocr.ocr(sys.argv[1], cls=True); " +
                    "def clean_text(text): " +
                    "    if not text or len(text.strip()) < 2: return ''; " +
                    "    text = re.sub(r'[^\\u4e00-\\u9fff\\u3400-\\u4dbf\\w\\s\\.,;:!?()（）【】\"\"''、。，；：！？]', '', text); " +
                    "    return text.strip() if text.strip() else ''; " +
                    "if result and result[0]: " +
                    "    for line in result[0]: " +
                    "        if line and len(line) >= 2: " +
                    "            text = line[1][0] if line[1] else ''; " +
                    "            cleaned = clean_text(text); " +
                    "            if cleaned: print(cleaned)",
                    tempFile.getAbsolutePath()
                );
                log.debug("使用内联PaddleOCR命令");
            }

            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 读取输出
            List<String> results = new ArrayList<>();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    // 过滤掉下载信息和错误信息
                    if (!line.isEmpty() &&
                        !line.startsWith("download") &&
                        !line.startsWith("ERROR") &&
                        !line.startsWith("WARNING") &&
                        !line.contains("PaddleOCR 初始化成功")) {
                        results.add(line);
                    }
                }
            }

            boolean finished = process.waitFor(45, TimeUnit.SECONDS); // 增加超时时间
            if (!finished) {
                process.destroyForcibly();
                return OCRResult.error("OCR识别超时，请尝试使用更小的图片");
            }

            if (process.exitValue() == 0) {
                log.info("Python PaddleOCR识别成功，识别到{}行文本", results.size());
                return OCRResult.success(results.isEmpty() ?
                    Arrays.asList("未识别到文字内容，建议检查图片清晰度") : results);
            } else {
                log.warn("Python PaddleOCR执行失败，退出码: {}", process.exitValue());
                return OCRResult.error("Python PaddleOCR执行失败，请检查Python环境和依赖");
            }

        } catch (Exception e) {
            log.error("Python PaddleOCR识别失败: {}", e.getMessage(), e);
            return getEnvironmentErrorResult(imageFile);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", e.getMessage());
                }
            }
        }
    }
    
    private OCRResult getEnvironmentErrorResult(MultipartFile imageFile) {
        List<String> results = new ArrayList<>();

        try {
            String filename = imageFile.getOriginalFilename();
            long size = imageFile.getSize();

            results.add("❌ PaddleOCR环境配置错误");
            results.add("文件: " + (filename != null ? filename : "未知"));
            results.add("大小: " + formatFileSize(size));
            results.add("");
            results.add("🔧 快速解决方案：");
            results.add("请在命令行中执行以下命令安装PaddleOCR：");
            results.add("");
            results.add("pip install paddlepaddle -i https://pypi.tuna.tsinghua.edu.cn/simple");
            results.add("pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple");
            results.add("pip install opencv-python -i https://pypi.tuna.tsinghua.edu.cn/simple");
            results.add("pip install pillow -i https://pypi.tuna.tsinghua.edu.cn/simple");
            results.add("");
            results.add("或者运行项目提供的安装脚本：");
            results.add("scripts/install_paddleocr.bat");
            results.add("");
            results.add("安装完成后重启应用即可使用PaddleOCR功能");
            results.add("");
            results.add("📝 环境检查结果：");
            results.add("- Python环境: " + (checkPythonOnly() ? "✓ 可用" : "✗ 不可用"));
            results.add("- PaddleOCR包: ✗ 未安装");

            log.warn("PaddleOCR环境不可用，返回安装指导信息");
            return OCRResult.success(results);

        } catch (Exception e) {
            log.error("生成环境错误信息失败: {}", e.getMessage());
            return OCRResult.error("环境检查失败");
        }
    }
    
    private OCRResult getMockResult(String filename) {
        List<String> mockTexts = new ArrayList<>();
        mockTexts.add("这是PaddleOCR的模拟识别结果");
        mockTexts.add("文件名: " + (filename != null ? filename : "未知"));
        mockTexts.add("当前时间: " + java.time.LocalDateTime.now());
        mockTexts.add("模式: Mock模式");
        mockTexts.add("建议: 配置Python环境以启用真实识别");
        
        return OCRResult.success(mockTexts);
    }
    
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        }
        if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        }
        return String.format("%.1f MB", size / (1024.0 * 1024.0));
    }
    
    public boolean isAvailable() {
        return paddleOcrEnabled && (mockMode || pythonAvailable);
    }
    
    public String getStatus() {
        if (!paddleOcrEnabled) {
            return "已禁用";
        }
        if (mockMode) {
            return "模拟模式";
        }
        if (pythonAvailable) {
            return "Python+PaddleOCR可用";
        }
        return "Python环境或PaddleOCR不可用，请运行安装脚本";
    }
    
    // OCR结果封装类
    public static class OCRResult {
        private boolean success;
        private String message;
        private List<String> texts;
        private long timestamp;
        
        private OCRResult(boolean success, String message, List<String> texts) {
            this.success = success;
            this.message = message;
            this.texts = texts;
            this.timestamp = System.currentTimeMillis();
        }
        
        public static OCRResult success(List<String> texts) {
            return new OCRResult(true, "识别成功", texts);
        }
        
        public static OCRResult error(String message) {
            return new OCRResult(false, message, new ArrayList<>());
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public List<String> getTexts() { return texts; }
        public long getTimestamp() { return timestamp; }
    }
}
