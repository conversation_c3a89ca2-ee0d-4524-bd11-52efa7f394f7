package com.xygk.api.wenxuan.dao;

import com.xygk.api.wenxuan.dto.BookMap;
import com.xygk.api.wenxuan.dto.BookMapTest;
import com.xygk.api.wenxuan.dto.WenXuanBook;
import com.xygk.api.wenxuan.dto.ZyTextbook;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/8 11:16
 */
@Mapper
public interface ZhYuanDao  {

    List<BookMapTest> selectBookMap();

    void insertBookMap(BookMap map);

    List<WenXuanBook> selectWenXuanBook(String bookId);

    List<ZyTextbook> selectZyBookList(String zyBookId);

    List<WenXuanBook> selectBookChild(String chapterId);

}
