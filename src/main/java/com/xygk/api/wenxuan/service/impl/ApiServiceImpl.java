package com.xygk.api.wenxuan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xygk.api.wenxuan.dao.ZhYuanDao;
import com.xygk.api.wenxuan.dto.BookMap;
import com.xygk.api.wenxuan.dto.WenXuanBook;
import com.xygk.api.wenxuan.dto.ZyTextbook;
import com.xygk.api.wenxuan.service.ApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/11 15:02
 */
@Service
public class ApiServiceImpl implements ApiService {
    @Autowired
    private ZhYuanDao zhYuanDao;

    @Override
    public void checkChild(BookMap bookMap) {
        //先查询文轩的章节是否存在子节点
        List<WenXuanBook> list = zhYuanDao.selectBookChild(bookMap.getChapterId());
        for (int i = 0; i < list.size(); i++) {
            WenXuanBook wenXuanBook = list.get(i);
            String level = wenXuanBook.getLevel();
            BookMap map = new BookMap();
            map.setBookId(bookMap.getBookId());
            map.setBookName(bookMap.getBookName());
            if ("2".equals(level)) {
                map.setChapterName("    " + wenXuanBook.getName());
            }
            if ("3".equals(level)) {
                map.setChapterName("        " + wenXuanBook.getName());
            }
            if ("4".equals(level)) {
                map.setChapterName("            " + wenXuanBook.getName());
            }
            map.setChapterId(wenXuanBook.getCode());
            map.setLevel(level);
            //状元网章节子节点信息
            if (ObjectUtil.isNotEmpty(BeanUtil.getFieldValue(bookMap, "zyChapterId"))) {
                List<ZyTextbook> zyTextbooks = zhYuanDao.selectZyBookList(bookMap.getZyChapterId());
                if (i < zyTextbooks.size()) {
                    ZyTextbook zyTextbook = zyTextbooks.get(i);
                    if (BeanUtil.isNotEmpty(zyTextbook)) {
                        map.setZyChapterId(String.valueOf(zyTextbook.getId()));
                        if ("2".equals(level)) {
                            map.setZyChapterName("    " + zyTextbook.getName());
                        }
                        if ("3".equals(level)) {
                            map.setZyChapterName("        " + zyTextbook.getName());
                        }
                        if ("4".equals(level)) {
                            map.setZyChapterName("            " + zyTextbook.getName());
                        }
                    }
                }
            }
            zhYuanDao.insertBookMap(map);
            checkChild(map);
        }
    }
}
