package com.xygk.api.wenxuan.dto;

import java.io.Serializable;

/**
 * 状元网数据字典结构对象
 *
 * <AUTHOR>
 * @date 2023/3/9 14:11
 */
public class ZhYuanDict implements Serializable {

    private static final long serialVersionUID = 1927194935597064948L;

    /**
     * 名称
     */
    private String dictLabel;

    /**
     * 值
     */
    private String dictValue;

    /**
     * 排序
     */
    private Integer dictSort;

    /**
     * 表名
     */
    private String tableName;

    public String getDictLabel() {
        return dictLabel;
    }

    public void setDictLabel(String dictLabel) {
        this.dictLabel = dictLabel;
    }

    public String getDictValue() {
        return dictValue;
    }

    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }

    public Integer getDictSort() {
        return dictSort;
    }

    public void setDictSort(Integer dictSort) {
        this.dictSort = dictSort;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
