package com.xygk.api.wenxuan.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/14 11:02
 */
public class TreeNode implements Serializable {
    private static final long serialVersionUID = -6295719059094073726L;
    /**
     * 原始名称
     */
    private String originalName;

    /**
     * 显示名称
     */
    private String displayName;
    /**
     * 唯一编码（UUID）
     */
    private String code;
    /**
     * 父节点UUID
     */
    private String parentCode;
    /**
     * 所有父节点
     */
    private String parentCodes;

    /**
     * 序号
     */
    private int sort;

    /**
     * 层级
     */
    private int level;

    /**
     * 是否为目录
     */
    private boolean isDirectory;
    /**
     * 完整路径
     */
    private String fullPath;

    private List<TreeNode> children = new ArrayList<>();

    public TreeNode(String originalName, String displayName, String code, String parentCode,
                    String parentCodes, int sort, int level,
                    boolean isDirectory,String fullPath) {
        this.originalName = originalName;
        this.displayName = displayName;
        this.code = code;
        this.parentCode = parentCode;
        this.parentCodes = parentCodes;
        this.level = level;
        this.sort = sort;
        this.isDirectory = isDirectory;
        this.fullPath = fullPath;
    }

    public TreeNode() {

    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getParentCodes() {
        return parentCodes;
    }

    public void setParentCodes(String parentCodes) {
        this.parentCodes = parentCodes;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public boolean isDirectory() {
        return isDirectory;
    }

    public void setDirectory(boolean directory) {
        isDirectory = directory;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public List<TreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<TreeNode> children) {
        this.children = children;
    }

}
