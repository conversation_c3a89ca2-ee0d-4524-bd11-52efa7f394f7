package com.xygk.api.wenxuan.dto;

import java.io.Serializable;

/**
 * 状元网树结构对象
 *
 * <AUTHOR>
 * @date 2023/3/9 14:11
 */
public class ZhYuanTree implements Serializable {

    private static final long serialVersionUID = 1329242174781421348L;
    /**
     * 学科Id
     */
    private Integer subjectId;
    /**
     * 学段Id
     */
    private String period;
    /**
     * 唯一标识
     */
    private Integer id;

    /**
     * 父Id
     */
    private Integer pId;

    /**
     * 名称
     */
    private String name;

    /**
     * 是否有子节点
     */
    private boolean leaf;

    /**
     * 表名
     */
    private String tableName;

    public Integer getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getpId() {
        return pId;
    }

    public void setpId(Integer pId) {
        this.pId = pId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isLeaf() {
        return leaf;
    }

    public void setLeaf(boolean leaf) {
        this.leaf = leaf;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
