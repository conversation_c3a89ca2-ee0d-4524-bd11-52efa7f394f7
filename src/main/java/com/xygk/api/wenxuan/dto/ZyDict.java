package com.xygk.api.wenxuan.dto;

/**
 * 字典翻译
 *
 * <AUTHOR>
 * @date 2023/3/8 10:38
 */
public class ZyDict {

    /**
     * 唯一标识符
     */
    private Integer id;

    /**
     * 状元网字典名称
     */
    private String zyDictName;

    /**
     * 状元网字典类型
     */
    private String zyDictType;

    /**
     * 状元网字典标签
     */
    private String zyDictLabel;

    /**
     * 状元网字典值
     */
    private String zyDictValue;

    /**
     * 转换之后的字典标签
     */
    private String dictLabel;

    /**
     * 转换之后的字典值
     */
    private String dictValue;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getZyDictName() {
        return zyDictName;
    }

    public void setZyDictName(String zyDictName) {
        this.zyDictName = zyDictName;
    }

    public String getZyDictType() {
        return zyDictType;
    }

    public void setZyDictType(String zyDictType) {
        this.zyDictType = zyDictType;
    }

    public String getZyDictLabel() {
        return zyDictLabel;
    }

    public void setZyDictLabel(String zyDictLabel) {
        this.zyDictLabel = zyDictLabel;
    }

    public String getZyDictValue() {
        return zyDictValue;
    }

    public void setZyDictValue(String zyDictValue) {
        this.zyDictValue = zyDictValue;
    }

    public String getDictLabel() {
        return dictLabel;
    }

    public void setDictLabel(String dictLabel) {
        this.dictLabel = dictLabel;
    }

    public String getDictValue() {
        return dictValue;
    }

    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }
}
