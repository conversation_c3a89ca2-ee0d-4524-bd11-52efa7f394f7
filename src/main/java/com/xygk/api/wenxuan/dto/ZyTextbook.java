package com.xygk.api.wenxuan.dto;

/**
 * 教材版本及章节
 *
 * <AUTHOR>
 * @date 2023/3/8 10:46
 */
public class ZyTextbook {

    /**
     * 唯一标识
     */
    private Integer id;

    /**
     * 父Id
     */
    private Integer pId;

    /**
     * 名称
     */
    private String name;

    /**
     * 是否有子节点
     */
    private boolean leaf;
    /**
     * 学段ID
     */
    private String period;

    /**
     * 学科ID
     */
    private Integer subjectId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getpId() {
        return pId;
    }

    public void setpId(Integer pId) {
        this.pId = pId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isLeaf() {
        return leaf;
    }

    public void setLeaf(boolean leaf) {
        this.leaf = leaf;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Integer getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }
}
