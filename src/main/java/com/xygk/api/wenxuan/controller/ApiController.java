package com.xygk.api.wenxuan.controller;

import cn.hutool.core.bean.BeanUtil;
import com.xygk.api.wenxuan.dao.ZhYuanDao;
import com.xygk.api.wenxuan.dto.BookMap;
import com.xygk.api.wenxuan.dto.BookMapTest;
import com.xygk.api.wenxuan.dto.WenXuanBook;
import com.xygk.api.wenxuan.dto.ZyTextbook;
import com.xygk.api.wenxuan.service.ApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/3 14:34
 */
@RestController
@RequestMapping("bookInfo/")
@Api(tags = "生成书籍信息")
public class ApiController {
    @Autowired
    private ZhYuanDao zhYuanDao;
    @Autowired
    private ApiService apiService;

    @GetMapping("generate")
    @ApiOperation("生成书籍信息")
    public void detailsTotal(@ApiIgnore @RequestParam Map<String, Object> params) {

        List<BookMapTest> bookMapTests = zhYuanDao.selectBookMap();
        for (BookMapTest bookMapTest : bookMapTests) {
            System.out.println("——开始生成书籍——");
            //书籍信息
            String bookId = bookMapTest.getBookId();
            String zyBookId = bookMapTest.getZyBookId();
            //查询文轩书籍章节
            List<WenXuanBook> wenXuanBooks = zhYuanDao.selectWenXuanBook(bookId);
            //查询状元网章节信息
            List<ZyTextbook> zyTextbooks = zhYuanDao.selectZyBookList(zyBookId);
            for (int i = 0; i < wenXuanBooks.size(); i++) {
                WenXuanBook wenXuanBook = wenXuanBooks.get(i);
                BookMap bookMap = new BookMap();
                bookMap.setBookId(bookId);
                bookMap.setLevel(wenXuanBook.getLevel());
                bookMap.setBookName(bookMapTest.getBookName());
                bookMap.setChapterId(wenXuanBook.getCode());
                bookMap.setChapterName(wenXuanBook.getName());
                if (i < zyTextbooks.size()) {
                    ZyTextbook zyTextbook = zyTextbooks.get(i);
                    if (BeanUtil.isNotEmpty(zyTextbook)) {
                        bookMap.setZyChapterId(String.valueOf(zyTextbook.getId()));
                        bookMap.setZyChapterName(zyTextbook.getName());
                    }
                }
                zhYuanDao.insertBookMap(bookMap);
                //校验文轩章节是否存在子节点
                apiService.checkChild(bookMap);

            }

        }
        System.out.println("——结束生成书籍——");

    }


}
