package com.xygk.api.classroom.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 课堂分贝检测控制器
 * 提供分贝数据存储、历史记录和统计分析功能
 * 
 * <AUTHOR>
 * @date 2025/08/21
 */
@Slf4j
@RestController
@RequestMapping("/api/classroom/decibel")
@Api(tags = "课堂分贝检测", description = "课堂环境分贝监测相关接口")
public class DecibelMonitorController {
    
    // 存储会话数据（实际项目中应该使用数据库）
    private final Map<String, List<DecibelRecord>> sessionData = new ConcurrentHashMap<>();
    
    // 存储会话统计信息
    private final Map<String, SessionStats> sessionStats = new ConcurrentHashMap<>();
    
    /**
     * 开始新的监测会话
     */
    @PostMapping("/session/start")
    @ApiOperation(value = "开始监测会话", notes = "创建新的分贝监测会话")
    public ResponseEntity<Map<String, Object>> startSession(
            @ApiParam(value = "会话配置") @RequestBody(required = false) Map<String, Object> config) {
        
        String sessionId = generateSessionId();
        log.info("开始新的分贝监测会话: {}", sessionId);
        
        // 初始化会话数据
        sessionData.put(sessionId, new ArrayList<>());
        
        // 初始化统计信息
        SessionStats stats = new SessionStats();
        stats.sessionId = sessionId;
        stats.startTime = LocalDateTime.now();
        stats.thresholds = extractThresholds(config);
        sessionStats.put(sessionId, stats);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("sessionId", sessionId);
        response.put("startTime", stats.startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        response.put("thresholds", stats.thresholds);
        response.put("message", "监测会话已开始");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 记录分贝数据
     */
    @PostMapping("/session/{sessionId}/record")
    @ApiOperation(value = "记录分贝数据", notes = "向指定会话添加分贝数据点")
    public ResponseEntity<Map<String, Object>> recordDecibel(
            @ApiParam(value = "会话ID", required = true) @PathVariable String sessionId,
            @ApiParam(value = "分贝数据") @RequestBody Map<String, Object> data) {
        
        if (!sessionData.containsKey(sessionId)) {
            return ResponseEntity.badRequest().body(createErrorResponse("会话不存在", 404));
        }
        
        try {
            Double decibel = ((Number) data.get("decibel")).doubleValue();
            Long timestamp = data.containsKey("timestamp") ? 
                ((Number) data.get("timestamp")).longValue() : System.currentTimeMillis();
            
            // 创建记录
            DecibelRecord record = new DecibelRecord();
            record.sessionId = sessionId;
            record.decibel = decibel;
            record.timestamp = timestamp;
            record.recordTime = LocalDateTime.now();
            
            // 添加到会话数据
            sessionData.get(sessionId).add(record);
            
            // 更新统计信息
            updateSessionStats(sessionId, record);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "数据记录成功");
            response.put("recordCount", sessionData.get(sessionId).size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("记录分贝数据失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createErrorResponse("数据格式错误", 400));
        }
    }
    
    /**
     * 批量记录分贝数据
     */
    @PostMapping("/session/{sessionId}/batch-record")
    @ApiOperation(value = "批量记录分贝数据", notes = "批量添加多个分贝数据点")
    public ResponseEntity<Map<String, Object>> batchRecordDecibel(
            @ApiParam(value = "会话ID", required = true) @PathVariable String sessionId,
            @ApiParam(value = "分贝数据数组") @RequestBody Map<String, Object> request) {
        
        if (!sessionData.containsKey(sessionId)) {
            return ResponseEntity.badRequest().body(createErrorResponse("会话不存在", 404));
        }
        
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) request.get("data");
            
            if (dataList == null || dataList.isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("数据不能为空", 400));
            }
            
            List<DecibelRecord> records = new ArrayList<>();
            for (Map<String, Object> data : dataList) {
                Double decibel = ((Number) data.get("decibel")).doubleValue();
                Long timestamp = data.containsKey("timestamp") ? 
                    ((Number) data.get("timestamp")).longValue() : System.currentTimeMillis();
                
                DecibelRecord record = new DecibelRecord();
                record.sessionId = sessionId;
                record.decibel = decibel;
                record.timestamp = timestamp;
                record.recordTime = LocalDateTime.now();
                
                records.add(record);
            }
            
            // 批量添加到会话数据
            sessionData.get(sessionId).addAll(records);
            
            // 批量更新统计信息
            for (DecibelRecord record : records) {
                updateSessionStats(sessionId, record);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "批量数据记录成功");
            response.put("recordCount", records.size());
            response.put("totalCount", sessionData.get(sessionId).size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("批量记录分贝数据失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(createErrorResponse("数据格式错误", 400));
        }
    }
    
    /**
     * 获取会话统计信息
     */
    @GetMapping("/session/{sessionId}/stats")
    @ApiOperation(value = "获取会话统计", notes = "获取指定会话的统计信息")
    public ResponseEntity<Map<String, Object>> getSessionStats(
            @ApiParam(value = "会话ID", required = true) @PathVariable String sessionId) {
        
        if (!sessionStats.containsKey(sessionId)) {
            return ResponseEntity.badRequest().body(createErrorResponse("会话不存在", 404));
        }
        
        SessionStats stats = sessionStats.get(sessionId);
        List<DecibelRecord> records = sessionData.get(sessionId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("sessionId", sessionId);
        response.put("startTime", stats.startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        response.put("endTime", stats.endTime != null ? 
            stats.endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) : null);
        response.put("totalRecords", records.size());
        response.put("averageDecibel", stats.averageDecibel);
        response.put("maxDecibel", stats.maxDecibel);
        response.put("minDecibel", stats.minDecibel);
        response.put("activeTime", stats.activeTime);
        response.put("totalDuration", stats.getTotalDuration());
        response.put("thresholds", stats.thresholds);
        
        // 分级统计
        Map<String, Integer> levelStats = calculateLevelStats(records, stats.thresholds);
        response.put("levelStats", levelStats);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取会话历史数据
     */
    @GetMapping("/session/{sessionId}/history")
    @ApiOperation(value = "获取历史数据", notes = "获取指定会话的历史分贝数据")
    public ResponseEntity<Map<String, Object>> getSessionHistory(
            @ApiParam(value = "会话ID", required = true) @PathVariable String sessionId,
            @ApiParam(value = "限制数量") @RequestParam(defaultValue = "100") int limit) {
        
        if (!sessionData.containsKey(sessionId)) {
            return ResponseEntity.badRequest().body(createErrorResponse("会话不存在", 404));
        }
        
        List<DecibelRecord> records = sessionData.get(sessionId);
        
        // 限制返回数量
        List<DecibelRecord> limitedRecords = records.size() > limit ? 
            records.subList(Math.max(0, records.size() - limit), records.size()) : records;
        
        // 转换为响应格式
        List<Map<String, Object>> historyData = new ArrayList<>();
        for (DecibelRecord record : limitedRecords) {
            Map<String, Object> item = new HashMap<>();
            item.put("decibel", record.decibel);
            item.put("timestamp", record.timestamp);
            item.put("recordTime", record.recordTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            historyData.add(item);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("sessionId", sessionId);
        response.put("totalRecords", records.size());
        response.put("returnedRecords", historyData.size());
        response.put("data", historyData);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 结束监测会话
     */
    @PostMapping("/session/{sessionId}/end")
    @ApiOperation(value = "结束监测会话", notes = "结束指定的分贝监测会话")
    public ResponseEntity<Map<String, Object>> endSession(
            @ApiParam(value = "会话ID", required = true) @PathVariable String sessionId) {
        
        if (!sessionStats.containsKey(sessionId)) {
            return ResponseEntity.badRequest().body(createErrorResponse("会话不存在", 404));
        }
        
        SessionStats stats = sessionStats.get(sessionId);
        stats.endTime = LocalDateTime.now();
        
        log.info("结束分贝监测会话: {}, 总记录数: {}", sessionId, sessionData.get(sessionId).size());
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("sessionId", sessionId);
        response.put("endTime", stats.endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        response.put("totalDuration", stats.getTotalDuration());
        response.put("totalRecords", sessionData.get(sessionId).size());
        response.put("message", "监测会话已结束");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取所有会话列表
     */
    @GetMapping("/sessions")
    @ApiOperation(value = "获取会话列表", notes = "获取所有监测会话的列表")
    public ResponseEntity<Map<String, Object>> getSessions() {
        
        List<Map<String, Object>> sessions = new ArrayList<>();
        for (SessionStats stats : sessionStats.values()) {
            Map<String, Object> session = new HashMap<>();
            session.put("sessionId", stats.sessionId);
            session.put("startTime", stats.startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            session.put("endTime", stats.endTime != null ? 
                stats.endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) : null);
            session.put("recordCount", sessionData.get(stats.sessionId).size());
            session.put("averageDecibel", stats.averageDecibel);
            session.put("maxDecibel", stats.maxDecibel);
            sessions.add(session);
        }
        
        // 按开始时间倒序排列
        sessions.sort((a, b) -> ((String) b.get("startTime")).compareTo((String) a.get("startTime")));
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("totalSessions", sessions.size());
        response.put("sessions", sessions);
        
        return ResponseEntity.ok(response);
    }
    
    // 辅助方法
    private String generateSessionId() {
        return "session_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString(new Random().nextInt());
    }
    
    private Map<String, Double> extractThresholds(Map<String, Object> config) {
        Map<String, Double> thresholds = new HashMap<>();
        thresholds.put("quiet", 40.0);
        thresholds.put("normal", 60.0);
        thresholds.put("active", 80.0);
        
        if (config != null && config.containsKey("thresholds")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> configThresholds = (Map<String, Object>) config.get("thresholds");
            for (Map.Entry<String, Object> entry : configThresholds.entrySet()) {
                if (entry.getValue() instanceof Number) {
                    thresholds.put(entry.getKey(), ((Number) entry.getValue()).doubleValue());
                }
            }
        }
        
        return thresholds;
    }
    
    private void updateSessionStats(String sessionId, DecibelRecord record) {
        SessionStats stats = sessionStats.get(sessionId);
        if (stats == null) return;
        
        stats.totalRecords++;
        stats.totalDecibel += record.decibel;
        stats.averageDecibel = stats.totalDecibel / stats.totalRecords;
        
        if (record.decibel > stats.maxDecibel) {
            stats.maxDecibel = record.decibel;
        }
        
        if (stats.minDecibel == 0 || record.decibel < stats.minDecibel) {
            stats.minDecibel = record.decibel;
        }
        
        // 计算活跃时间（分贝大于正常阈值）
        if (record.decibel >= stats.thresholds.get("normal")) {
            stats.activeTime += 1; // 假设每个记录代表1秒
        }
    }
    
    private Map<String, Integer> calculateLevelStats(List<DecibelRecord> records, Map<String, Double> thresholds) {
        Map<String, Integer> levelStats = new HashMap<>();
        levelStats.put("quiet", 0);
        levelStats.put("normal", 0);
        levelStats.put("active", 0);
        levelStats.put("loud", 0);
        
        for (DecibelRecord record : records) {
            if (record.decibel < thresholds.get("quiet")) {
                levelStats.put("quiet", levelStats.get("quiet") + 1);
            } else if (record.decibel < thresholds.get("normal")) {
                levelStats.put("normal", levelStats.get("normal") + 1);
            } else if (record.decibel < thresholds.get("active")) {
                levelStats.put("active", levelStats.get("active") + 1);
            } else {
                levelStats.put("loud", levelStats.get("loud") + 1);
            }
        }
        
        return levelStats;
    }
    
    private Map<String, Object> createErrorResponse(String message, int code) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("code", code);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
    
    // 内部数据类
    private static class DecibelRecord {
        String sessionId;
        Double decibel;
        Long timestamp;
        LocalDateTime recordTime;
    }
    
    private static class SessionStats {
        String sessionId;
        LocalDateTime startTime;
        LocalDateTime endTime;
        int totalRecords = 0;
        double totalDecibel = 0;
        double averageDecibel = 0;
        double maxDecibel = 0;
        double minDecibel = 0;
        long activeTime = 0; // 活跃时间（秒）
        Map<String, Double> thresholds = new HashMap<>();
        
        public long getTotalDuration() {
            if (endTime != null) {
                return java.time.Duration.between(startTime, endTime).getSeconds();
            } else {
                return java.time.Duration.between(startTime, LocalDateTime.now()).getSeconds();
            }
        }
    }
}
