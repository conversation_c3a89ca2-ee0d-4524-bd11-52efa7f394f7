package com.xygk.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * 树节点实体类，用于构建文件目录树
 *
 * <AUTHOR>
 * @date 2025/4/14
 */
public class TreeNode {

    // 原始名称
    private String name;

    // 显示名称（去掉序号前缀）
    private String displayName;

    // 节点唯一标识码
    private String code;

    // 父节点标识码
    private String parentCode;

    // 所有父节点标识码，逗号分隔
    private String parentCodes;

    // 排序号
    private int sort;

    // 层级，根节点为0
    private int level;

    // 是否为目录
    private boolean isDirectory;

    // 文件或目录的完整路径
    private String path;

    // 子节点列表
    private List<TreeNode> children = new ArrayList<>();

    public TreeNode() {
    }

    public TreeNode(String name, String displayName, String code, String parentCode, String parentCodes,
            int sort, int level, boolean isDirectory, String path) {
        this.name = name;
        this.displayName = displayName;
        this.code = code;
        this.parentCode = parentCode;
        this.parentCodes = parentCodes;
        this.sort = sort;
        this.level = level;
        this.isDirectory = isDirectory;
        this.path = path;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getParentCodes() {
        return parentCodes;
    }

    public void setParentCodes(String parentCodes) {
        this.parentCodes = parentCodes;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public boolean isDirectory() {
        return isDirectory;
    }

    public void setDirectory(boolean directory) {
        isDirectory = directory;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public List<TreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<TreeNode> children) {
        this.children = children;
    }
}