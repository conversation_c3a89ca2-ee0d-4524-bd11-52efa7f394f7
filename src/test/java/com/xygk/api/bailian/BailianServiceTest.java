package com.xygk.api.bailian;

import com.xygk.api.bailian.config.BailianConfig;
import com.xygk.api.bailian.dto.BailianRequest;
import com.xygk.api.bailian.service.impl.BailianService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.*;

/**
 * 百炼智能体服务测试类
 * <AUTHOR>
 * @date 2025-07-03
 */
@SpringBootTest
@TestPropertySource(properties = {
    "bailian.api-key=test-api-key",
    "bailian.default-app-id=test-app-id",
    "bailian.timeout=30000",
    "bailian.session-timeout=30"
})
public class BailianServiceTest {
    
    private BailianService bailianService;
    private BailianConfig bailianConfig;
    
    @BeforeEach
    void setUp() {
        bailianConfig = new BailianConfig();
        bailianConfig.setApiKey("test-api-key");
        bailianConfig.setDefaultAppId("test-app-id");
        bailianConfig.setTimeout(30000);
        bailianConfig.setSessionTimeout(30);
        bailianConfig.setIncrementalOutput(true);
        
        bailianService = new BailianService();
        // 注入配置（在实际测试中应该使用@Autowired）
    }
    
    @Test
    void testChatRequest() {
        // 创建测试请求
        BailianRequest request = new BailianRequest();
        request.setAppId("test-app-id");
        request.setPrompt("你好，请介绍一下自己");
        request.setSessionId("test-session-123");
        request.setIncrementalOutput(false);
        
        // 注意：这个测试需要真实的API密钥才能运行
        // 在实际环境中，您需要配置真实的API密钥
        System.out.println("测试请求构建成功");
        System.out.println("AppId: " + request.getAppId());
        System.out.println("Prompt: " + request.getPrompt());
        System.out.println("SessionId: " + request.getSessionId());
    }
    
    @Test
    void testMultiTurnChatRequest() {
        BailianRequest request = new BailianRequest();
        request.setAppId("test-app-id");
        request.setSessionId("test-session-456");
        
        // 构建多轮对话消息
        List<Map<String, String>> messages = new ArrayList<>();
        
        Map<String, String> message1 = new HashMap<>();
        message1.put("role", "user");
        message1.put("content", "什么是人工智能？");
        messages.add(message1);
        
        Map<String, String> message2 = new HashMap<>();
        message2.put("role", "assistant");
        message2.put("content", "人工智能是计算机科学的一个分支...");
        messages.add(message2);
        
        Map<String, String> message3 = new HashMap<>();
        message3.put("role", "user");
        message3.put("content", "它有哪些应用场景？");
        messages.add(message3);
        
        request.setMessages(messages);
        
        System.out.println("多轮对话测试请求构建成功");
        System.out.println("消息数量: " + messages.size());
    }
    
    @Test
    void testKnowledgeBaseSearch() {
        BailianRequest request = new BailianRequest();
        request.setAppId("test-app-id");
        request.setQuery("机器学习算法");
        
        System.out.println("知识库搜索测试请求构建成功");
        System.out.println("查询内容: " + request.getQuery());
    }
    
    @Test
    void testRequestValidation() {
        // 测试空请求
        try {
            BailianRequest emptyRequest = new BailianRequest();
            // bailianService.chat(emptyRequest); // 这会抛出异常
            System.out.println("空请求验证测试通过");
        } catch (Exception e) {
            System.out.println("捕获到预期异常: " + e.getMessage());
        }
    }
}
